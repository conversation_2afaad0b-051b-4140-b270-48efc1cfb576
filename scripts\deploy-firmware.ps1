# Studio 311 LP - Smart Cat Feeder ESP32 Firmware Deploy Script
param(
    [string]$Port = "auto",
    [switch]$Monitor,
    [switch]$Erase
)

Write-Host "Studio 311 LP - Smart Cat Feeder ESP32 Firmware Deploy" -ForegroundColor Cyan

# Check ESP-IDF
if (-not $env:IDF_PATH) {
    Write-Host "ESP-IDF not found! Please install ESP-IDF and set IDF_PATH." -ForegroundColor Red
    Write-Host "Visit: https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/" -ForegroundColor Yellow
    exit 1
}
Write-Host "ESP-IDF found at: $env:IDF_PATH" -ForegroundColor Green

# Get paths
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$FirmwareProject = Join-Path $ProjectRoot "esp32-firmware"

# Check if firmware project exists
if (-not (Test-Path $FirmwareProject)) {
    Write-Host "Creating ESP32 firmware project..." -ForegroundColor Yellow
    
    New-Item -ItemType Directory -Path $FirmwareProject -Force | Out-Null
    
    # Create basic CMakeLists.txt
    $cmakeContent = @"
cmake_minimum_required(VERSION 3.16)
include(`$ENV{IDF_PATH}/tools/cmake/project.cmake)
project(catfeeder-firmware)
"@
    Set-Content -Path (Join-Path $FirmwareProject "CMakeLists.txt") -Value $cmakeContent
    
    # Create main directory
    $mainDir = Join-Path $FirmwareProject "main"
    New-Item -ItemType Directory -Path $mainDir -Force | Out-Null
    
    $mainCmake = @"
idf_component_register(SRCS "main.c" INCLUDE_DIRS ".")
"@
    Set-Content -Path (Join-Path $mainDir "CMakeLists.txt") -Value $mainCmake
    
    # Create basic main.c
    $mainC = @"
#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"

static const char *TAG = "CatFeeder";

void app_main(void)
{
    ESP_LOGI(TAG, "Studio 311 LP - Smart Cat Feeder ESP32 Firmware v1.0.0");
    ESP_LOGI(TAG, "ESP32 Cat Feeder starting...");
    
    while (1) {
        ESP_LOGI(TAG, "Cat Feeder running...");
        vTaskDelay(5000 / portTICK_PERIOD_MS);
    }
}
"@
    Set-Content -Path (Join-Path $mainDir "main.c") -Value $mainC
    
    Write-Host "Basic ESP32 firmware project created!" -ForegroundColor Green
}

Write-Host "Building and deploying ESP32 firmware..." -ForegroundColor Cyan

# Change to firmware project
Push-Location $FirmwareProject
try {
    # Set target
    Write-Host "Setting ESP32 target..." -ForegroundColor Cyan
    idf.py set-target esp32
    
    # Erase if requested
    if ($Erase) {
        Write-Host "Erasing ESP32 flash..." -ForegroundColor Yellow
        if ($Port -eq "auto") {
            idf.py erase-flash
        } else {
            idf.py -p $Port erase-flash
        }
    }
    
    # Build and flash
    Write-Host "Building firmware..." -ForegroundColor Cyan
    idf.py build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build successful! Flashing to ESP32..." -ForegroundColor Green
        
        if ($Port -eq "auto") {
            idf.py flash
        } else {
            idf.py -p $Port flash
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Firmware deployed successfully!" -ForegroundColor Green
            
            if ($Monitor) {
                Write-Host "Starting serial monitor (Ctrl+C to stop)..." -ForegroundColor Cyan
                if ($Port -eq "auto") {
                    idf.py monitor
                } else {
                    idf.py -p $Port monitor
                }
            }
        } else {
            Write-Host "Flash failed!" -ForegroundColor Red
        }
    } else {
        Write-Host "Build failed!" -ForegroundColor Red
    }
} finally {
    Pop-Location
}

Write-Host "Complete!" -ForegroundColor Green
