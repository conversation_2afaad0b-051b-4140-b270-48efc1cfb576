using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace CatFeeder.Core.Services
{
    public interface ILoggingService
    {
        void LogDebug(string message);
        void LogInfo(string message);
        void LogWarning(string message);
        void LogError(string message, Exception? exception = null);
        void LogCritical(string message, Exception? exception = null);
        Task<string> GetLogFilePathAsync();
        Task<string> GetLogContentsAsync(int maxLines = 1000);
        Task ClearLogsAsync();
    }

    public class LoggingService : ILoggingService
    {
        private readonly ILogger _logger;
        private readonly SemaphoreSlim _fileLock = new SemaphoreSlim(1, 1);
        private readonly string _logFileName = "catfeeder_log.txt";
        private readonly int _maxLogFileSize = 5 * 1024 * 1024; // 5MB

        public LoggingService(ILogger<LoggingService> logger)
        {
            _logger = logger;
            InitializeLogFileAsync().ConfigureAwait(false).GetAwaiter().GetResult();
        }

        private async Task InitializeLogFileAsync()
        {
            try
            {
                var logFilePath = await GetLogFilePathAsync();
                
                // Create directory if it doesn't exist
                var directory = Path.GetDirectoryName(logFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                // Create log file if it doesn't exist
                if (!File.Exists(logFilePath))
                {
                    using (var stream = File.Create(logFilePath))
                    {
                        var header = Encoding.UTF8.GetBytes($"=== STUDIO 311 LP - SMART CAT FEEDER LOG ===\r\nStarted: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\r\n\r\n");
                        await stream.WriteAsync(header, 0, header.Length);
                    }
                }
                
                // Trim log file if it's too large
                await TrimLogFileIfNeededAsync(logFilePath);
                
                // Log startup message
                await LogToFileAsync("INFO", "Logging service initialized");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize log file: {Message}", ex.Message);
            }
        }

        public void LogDebug(string message)
        {
            _logger.LogDebug(message);
            _ = LogToFileAsync("DEBUG", message);
        }

        public void LogInfo(string message)
        {
            _logger.LogInformation(message);
            _ = LogToFileAsync("INFO", message);
        }

        public void LogWarning(string message)
        {
            _logger.LogWarning(message);
            _ = LogToFileAsync("WARNING", message);
        }

        public void LogError(string message, Exception? exception = null)
        {
            if (exception != null)
            {
                _logger.LogError(exception, message);
                _ = LogToFileAsync("ERROR", $"{message}\r\nException: {exception.Message}\r\nStack Trace: {exception.StackTrace}");
            }
            else
            {
                _logger.LogError(message);
                _ = LogToFileAsync("ERROR", message);
            }
        }

        public void LogCritical(string message, Exception? exception = null)
        {
            if (exception != null)
            {
                _logger.LogCritical(exception, message);
                _ = LogToFileAsync("CRITICAL", $"{message}\r\nException: {exception.Message}\r\nStack Trace: {exception.StackTrace}");
            }
            else
            {
                _logger.LogCritical(message);
                _ = LogToFileAsync("CRITICAL", message);
            }
        }

        public async Task<string> GetLogFilePathAsync()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            var logDirectory = Path.Combine(appDataPath, "Studio311LP", "CatFeeder", "Logs");
            return Path.Combine(logDirectory, _logFileName);
        }

        public async Task<string> GetLogContentsAsync(int maxLines = 1000)
        {
            await _fileLock.WaitAsync();
            try
            {
                var logFilePath = await GetLogFilePathAsync();
                if (!File.Exists(logFilePath))
                {
                    return "Log file does not exist.";
                }

                var lines = new List<string>();
                using (var reader = new StreamReader(logFilePath))
                {
                    string? line;
                    while ((line = await reader.ReadLineAsync()) != null)
                    {
                        lines.Add(line);
                        if (lines.Count > maxLines)
                        {
                            lines.RemoveAt(0);
                        }
                    }
                }

                return string.Join(Environment.NewLine, lines);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to read log file: {Message}", ex.Message);
                return $"Failed to read log file: {ex.Message}";
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task ClearLogsAsync()
        {
            await _fileLock.WaitAsync();
            try
            {
                var logFilePath = await GetLogFilePathAsync();
                if (File.Exists(logFilePath))
                {
                    File.Delete(logFilePath);
                }
                
                // Create new log file with header
                using (var stream = File.Create(logFilePath))
                {
                    var header = Encoding.UTF8.GetBytes($"=== STUDIO 311 LP - SMART CAT FEEDER LOG ===\r\nCleared: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\r\n\r\n");
                    await stream.WriteAsync(header, 0, header.Length);
                }
                
                _logger.LogInformation("Log file cleared");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to clear log file: {Message}", ex.Message);
            }
            finally
            {
                _fileLock.Release();
            }
        }

        private async Task LogToFileAsync(string level, string message)
        {
            await _fileLock.WaitAsync();
            try
            {
                var logFilePath = await GetLogFilePathAsync();
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var logEntry = $"[{timestamp}] [{level}] {message}\r\n";
                
                await File.AppendAllTextAsync(logFilePath, logEntry);
                
                // Check if we need to trim the log file
                await TrimLogFileIfNeededAsync(logFilePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to write to log file: {Message}", ex.Message);
            }
            finally
            {
                _fileLock.Release();
            }
        }

        private async Task TrimLogFileIfNeededAsync(string logFilePath)
        {
            try
            {
                var fileInfo = new FileInfo(logFilePath);
                if (fileInfo.Exists && fileInfo.Length > _maxLogFileSize)
                {
                    // Read all lines, keep only the last half
                    var lines = await File.ReadAllLinesAsync(logFilePath);
                    var linesToKeep = lines.Skip(lines.Length / 2).ToArray();
                    
                    // Write back the trimmed content
                    await File.WriteAllLinesAsync(logFilePath, linesToKeep);
                    
                    // Log the trim operation
                    var trimMessage = $"Log file trimmed from {lines.Length} to {linesToKeep.Length} lines";
                    await File.AppendAllTextAsync(logFilePath, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [INFO] {trimMessage}\r\n");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to trim log file: {Message}", ex.Message);
            }
        }
    }
}
