/**
 * Motor Control Component
 * 
 * Provides unified interface for both stepper and DC motors
 * Supports flexible configuration for different motor types
 */

#pragma once

#include "esp_err.h"
#include "driver/gpio.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Motor types supported
 */
typedef enum {
    MOTOR_TYPE_STEPPER,     ///< Stepper motor with precise step control
    MOTOR_TYPE_DC_TIME,     ///< DC motor with time-based control
    MOTOR_TYPE_DC_SENSOR    ///< DC motor with sensor-based stop detection
} motor_type_t;

/**
 * Stepper motor configuration
 */
typedef struct {
    gpio_num_t step_pin;        ///< Step pulse pin
    gpio_num_t dir_pin;         ///< Direction pin
    gpio_num_t enable_pin;      ///< Enable pin (optional, set to -1 if not used)
    uint32_t steps_per_rev;     ///< Steps per revolution
    uint32_t microsteps;        ///< Microstepping (1, 2, 4, 8, 16, 32)
    uint32_t max_speed_hz;      ///< Maximum step frequency in Hz
    uint32_t acceleration;      ///< Acceleration in steps/sec²
    bool invert_direction;      ///< Invert direction signal
    bool invert_enable;         ///< Invert enable signal (active low)
} stepper_config_t;

/**
 * DC motor configuration
 */
typedef struct {
    gpio_num_t pwm_pin;         ///< PWM control pin
    gpio_num_t dir_pin;         ///< Direction pin (optional, set to -1 if not used)
    gpio_num_t enable_pin;      ///< Enable pin (optional, set to -1 if not used)
    gpio_num_t sensor_pin;      ///< Stop sensor pin (for MOTOR_TYPE_DC_SENSOR, -1 if not used)
    uint32_t pwm_frequency;     ///< PWM frequency in Hz
    uint32_t pwm_duty_cycle;    ///< PWM duty cycle (0-100%)
    uint32_t run_time_ms;       ///< Run time in milliseconds (for MOTOR_TYPE_DC_TIME)
    bool invert_direction;      ///< Invert direction signal
    bool invert_enable;         ///< Invert enable signal
    bool sensor_active_low;     ///< Sensor is active low
} dc_config_t;

/**
 * Motor configuration structure
 */
typedef struct {
    motor_type_t type;          ///< Motor type
    stepper_config_t stepper_config;  ///< Stepper motor configuration
    dc_config_t dc_config;      ///< DC motor configuration
} motor_config_t;

/**
 * Motor status
 */
typedef enum {
    MOTOR_STATUS_IDLE,          ///< Motor is idle
    MOTOR_STATUS_RUNNING,       ///< Motor is running
    MOTOR_STATUS_ERROR          ///< Motor error state
} motor_status_t;

/**
 * Motor movement parameters
 */
typedef struct {
    uint32_t steps;             ///< Number of steps (for stepper)
    uint32_t duration_ms;       ///< Duration in milliseconds (for DC time-based)
    bool clockwise;             ///< Direction (true = clockwise, false = counter-clockwise)
    uint32_t speed_hz;          ///< Speed in Hz (for stepper) or PWM duty cycle (for DC)
} motor_move_params_t;

/**
 * Initialize motor control system
 * 
 * @param config Motor configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_init(const motor_config_t *config);

/**
 * Deinitialize motor control system
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_deinit(void);

/**
 * Move motor with specified parameters
 * 
 * @param params Movement parameters
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_move(const motor_move_params_t *params);

/**
 * Stop motor immediately
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_stop(void);

/**
 * Enable/disable motor
 * 
 * @param enable true to enable, false to disable
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_enable(bool enable);

/**
 * Get motor status
 * 
 * @return Current motor status
 */
motor_status_t motor_get_status(void);

/**
 * Check if motor is busy (running)
 * 
 * @return true if motor is running, false otherwise
 */
bool motor_is_busy(void);

/**
 * Get current position (for stepper motors)
 * 
 * @return Current position in steps
 */
int32_t motor_get_position(void);

/**
 * Set current position (for stepper motors)
 * 
 * @param position Position in steps
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_set_position(int32_t position);

/**
 * Home motor (move to home position)
 * Only applicable for motors with home sensor
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_home(void);

/**
 * Get motor configuration
 * 
 * @param config Pointer to store configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_get_config(motor_config_t *config);

/**
 * Update motor configuration
 * 
 * @param config New configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_update_config(const motor_config_t *config);

/**
 * Calibrate motor (determine optimal parameters)
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_calibrate(void);

#ifdef __cplusplus
}
#endif
