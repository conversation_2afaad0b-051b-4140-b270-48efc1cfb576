/**
 * ESP32-WROOM-32 Simple Test Project
 * 
 * This is a simplified version that tests basic functionality:
 * - Built-in LED blinking patterns
 * - WiFi connection (hardcoded credentials for testing)
 * - Web status page
 * 
 * LED Patterns:
 * - Fast blink (200ms): Starting up
 * - Slow blink (1000ms): Wi<PERSON><PERSON> connecting
 * - Solid ON: Wi<PERSON><PERSON> connected, web server running
 * - Double blink: Error state
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <inttypes.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_http_server.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "driver/gpio.h"
#include "esp_timer.h"

static const char *TAG = "ESP32_SIMPLE_TEST";

// ESP-WROOM-32 built-in LED pin (GPIO 2)
#define LED_PIN GPIO_NUM_2

// WiFi credentials - CHANGE THESE TO YOUR NETWORK
#define WIFI_SSID "TomTom"
#define WIFI_PASS "LIETUVATEVYNEMUSU"

// Event group for synchronization
static EventGroupHandle_t status_event_group;

// Event bits
#define WIFI_CONNECTED_BIT  BIT0
#define ERROR_BIT           BIT1

// LED control
static esp_timer_handle_t led_timer;
static bool led_state = false;
static int led_pattern = 0; // 0=off, 1=fast, 2=slow, 3=solid, 4=double

static httpd_handle_t server = NULL;

/**
 * LED control timer callback
 */
static void led_timer_callback(void* arg)
{
    switch (led_pattern) {
        case 0: // Off
            gpio_set_level(LED_PIN, 0);
            break;
        case 1: // Fast blink (200ms) - Starting up
            led_state = !led_state;
            gpio_set_level(LED_PIN, led_state);
            break;
        case 2: // Slow blink (1000ms) - WiFi connecting
            led_state = !led_state;
            gpio_set_level(LED_PIN, led_state);
            break;
        case 3: // Solid ON - WiFi connected
            gpio_set_level(LED_PIN, 1);
            break;
        case 4: // Double blink - Error
            static int blink_count = 0;
            if (blink_count < 4) {
                led_state = !led_state;
                gpio_set_level(LED_PIN, led_state);
                blink_count++;
            } else {
                gpio_set_level(LED_PIN, 0);
                blink_count = 0;
                vTaskDelay(pdMS_TO_TICKS(1000)); // Pause between double blinks
            }
            break;
    }
}

/**
 * Set LED pattern
 */
static void set_led_pattern(int pattern)
{
    led_pattern = pattern;
    
    // Stop current timer
    if (led_timer) {
        esp_timer_stop(led_timer);
    }
    
    // Start timer with appropriate interval
    uint64_t interval_us;
    switch (pattern) {
        case 1: interval_us = 200000; break;  // 200ms
        case 2: interval_us = 1000000; break; // 1000ms
        case 3: interval_us = 100000; break;  // 100ms (for solid, just set once)
        case 4: interval_us = 150000; break;  // 150ms
        default: return; // Pattern 0 (off) doesn't need timer
    }
    
    if (pattern > 0) {
        esp_timer_start_periodic(led_timer, interval_us);
    }
}

/**
 * Initialize LED
 */
static void init_led(void)
{
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << LED_PIN),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    gpio_config(&io_conf);
    
    // Create LED timer
    esp_timer_create_args_t timer_args = {
        .callback = led_timer_callback,
        .name = "led_timer"
    };
    esp_timer_create(&timer_args, &led_timer);
    
    ESP_LOGI(TAG, "LED initialized on GPIO %d", LED_PIN);
}

/**
 * WiFi event handler
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        ESP_LOGI(TAG, "WiFi disconnected, trying to reconnect...");
        set_led_pattern(2); // Slow blink - connecting
        esp_wifi_connect();
        xEventGroupClearBits(status_event_group, WIFI_CONNECTED_BIT);
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        xEventGroupSetBits(status_event_group, WIFI_CONNECTED_BIT);
        set_led_pattern(3); // Solid ON - connected
    }
}

/**
 * HTTP GET handler for status page
 */
static esp_err_t status_get_handler(httpd_req_t *req)
{
    const char* html_template = 
        "<!DOCTYPE html>"
        "<html><head><title>ESP32 Cat Feeder Test</title>"
        "<meta name='viewport' content='width=device-width, initial-scale=1'>"
        "<style>"
        "body{font-family:Arial;margin:20px;background:#f0f0f0}"
        ".container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1)}"
        ".status{padding:10px;margin:10px 0;border-radius:5px}"
        ".ok{background:#d4edda;color:#155724;border:1px solid #c3e6cb}"
        ".warning{background:#fff3cd;color:#856404;border:1px solid #ffeaa7}"
        ".error{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb}"
        "h1{color:#333;text-align:center}"
        "h2{color:#666;border-bottom:2px solid #007bff;padding-bottom:5px}"
        ".config-item{margin:10px 0;padding:8px;background:#f8f9fa;border-left:4px solid #007bff}"
        "button{background:#007bff;color:white;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;margin:5px}"
        "button:hover{background:#0056b3}"
        "</style></head><body>"
        "<div class='container'>"
        "<h1>🐱 ESP32 Cat Feeder Simple Test</h1>"
        "<h2>Device Status</h2>"
        "<div class='status %s'>WiFi: %s</div>"
        "<div class='status ok'>Device: ESP32-WROOM-32 Working!</div>"
        "<h2>System Information</h2>"
        "<div class='config-item'><strong>Chip Model:</strong> ESP32-WROOM-32</div>"
        "<div class='config-item'><strong>Free Heap:</strong> %" PRIu32 " bytes</div>"
        "<div class='config-item'><strong>Uptime:</strong> %lld seconds</div>"
        "<div class='config-item'><strong>WiFi SSID:</strong> %s</div>"
        "<h2>Actions</h2>"
        "<button onclick='location.reload()'>Refresh Status</button>"
        "<button onclick='testLED()'>Test LED</button>"
        "<h2>Next Steps</h2>"
        "<div class='config-item'>"
        "<strong>✅ Basic Test Complete!</strong><br>"
        "Your ESP32-WROOM-32 is working correctly. You can now:<br>"
        "• Use the full Cat Feeder firmware<br>"
        "• Add motor drivers and sensors<br>"
        "• Build the mechanical feeding system"
        "</div>"
        "<script>"
        "function testLED(){fetch('/test_led').then(()=>alert('LED test completed!'))}"
        "</script>"
        "</div></body></html>";

    char html_response[2048];
    
    // Get system info
    uint32_t free_heap = esp_get_free_heap_size();
    int64_t uptime = esp_timer_get_time() / 1000000;
    
    // Status strings
    bool wifi_connected = (xEventGroupGetBits(status_event_group) & WIFI_CONNECTED_BIT) != 0;
    
    snprintf(html_response, sizeof(html_response), html_template,
        wifi_connected ? "ok" : "error",
        wifi_connected ? "Connected" : "Disconnected",
        free_heap,
        uptime,
        WIFI_SSID
    );
    
    httpd_resp_set_type(req, "text/html");
    httpd_resp_send(req, html_response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

/**
 * HTTP GET handler for LED test
 */
static esp_err_t test_led_handler(httpd_req_t *req)
{
    ESP_LOGI(TAG, "LED test requested");
    
    // Save current pattern
    int original_pattern = led_pattern;
    
    // Test sequence: fast blink for 2 seconds
    set_led_pattern(1);
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // Restore original pattern
    set_led_pattern(original_pattern);
    
    httpd_resp_send(req, "LED test completed", HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

/**
 * Start HTTP server
 */
static httpd_handle_t start_webserver(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.server_port = 80;
    
    ESP_LOGI(TAG, "Starting HTTP server on port %d", config.server_port);
    
    if (httpd_start(&server, &config) == ESP_OK) {
        // Register URI handlers
        httpd_uri_t status_uri = {
            .uri = "/",
            .method = HTTP_GET,
            .handler = status_get_handler,
            .user_ctx = NULL
        };
        httpd_register_uri_handler(server, &status_uri);
        
        httpd_uri_t test_led_uri = {
            .uri = "/test_led",
            .method = HTTP_GET,
            .handler = test_led_handler,
            .user_ctx = NULL
        };
        httpd_register_uri_handler(server, &test_led_uri);
        
        return server;
    }
    
    ESP_LOGE(TAG, "Failed to start HTTP server");
    return NULL;
}

/**
 * Initialize WiFi
 */
static void init_wifi(void)
{
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();
    
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL));
    
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_start());
    
    ESP_LOGI(TAG, "WiFi initialized");
}

/**
 * Connect to WiFi
 */
static void connect_wifi(void)
{
    wifi_config_t wifi_config = {
        .sta = {
            .ssid = WIFI_SSID,
            .password = WIFI_PASS,
        },
    };
    
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_LOGI(TAG, "Connecting to WiFi SSID: %s", WIFI_SSID);
    
    set_led_pattern(2); // Slow blink - connecting
    ESP_ERROR_CHECK(esp_wifi_connect());
}

/**
 * Initialize NVS
 */
static void init_nvs(void)
{
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    ESP_LOGI(TAG, "NVS initialized");
}

/**
 * Main application task
 */
static void app_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Application task started");
    
    // Start with fast blink to show device is starting
    set_led_pattern(1);
    vTaskDelay(pdMS_TO_TICKS(3000)); // Blink for 3 seconds
    
    // Connect to WiFi
    connect_wifi();
    
    // Wait for WiFi connection
    xEventGroupWaitBits(status_event_group, WIFI_CONNECTED_BIT, pdFALSE, pdFALSE, portMAX_DELAY);
    
    // Start web server
    ESP_LOGI(TAG, "Starting web server...");
    server = start_webserver();
    if (server) {
        ESP_LOGI(TAG, "Web server started successfully");
        ESP_LOGI(TAG, "Open your browser and go to the ESP32's IP address");
    }
    
    // Main application loop
    while (1) {
        EventBits_t bits = xEventGroupGetBits(status_event_group);
        
        // Status logging every 30 seconds
        static int log_counter = 0;
        if (++log_counter >= 30) {
            log_counter = 0;
            ESP_LOGI(TAG, "Status - WiFi:%s Server:%s Free Heap:%" PRIu32,
                (bits & WIFI_CONNECTED_BIT) ? "OK" : "NO",
                server ? "OK" : "NO",
                esp_get_free_heap_size()
            );
        }
        
        vTaskDelay(pdMS_TO_TICKS(1000)); // 1 second delay
    }
}

/**
 * Application entry point
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32-WROOM-32 Simple Test v1.0");
    ESP_LOGI(TAG, "Built on %s %s", __DATE__, __TIME__);
    
    // Create event group
    status_event_group = xEventGroupCreate();
    if (status_event_group == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return;
    }
    
    // Initialize components
    init_nvs();
    init_led();
    init_wifi();
    
    // Create main application task
    xTaskCreate(app_task, "app_task", 4096, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "=== ESP32 Simple Test Ready ===");
    ESP_LOGI(TAG, "LED Patterns:");
    ESP_LOGI(TAG, "  Fast blink (200ms) = Starting up");
    ESP_LOGI(TAG, "  Slow blink (1000ms) = WiFi connecting");
    ESP_LOGI(TAG, "  Solid ON = WiFi connected, web server running");
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "IMPORTANT: Edit main_simple.c and change WiFi credentials:");
    ESP_LOGI(TAG, "  #define WIFI_SSID \"YourWiFiName\"");
    ESP_LOGI(TAG, "  #define WIFI_PASS \"YourWiFiPassword\"");
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "After WiFi connects, access web interface at ESP32's IP address");
}
