<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="CatFeeder.Mobile.Views.CrashSafePage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="Smart Cat Feeder - Safe Mode"
             BackgroundColor="#FF6B6B">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20" VerticalOptions="CenterAndExpand">
            
            <!-- Error Icon and Title -->
            <StackLayout HorizontalOptions="Center" Spacing="15">
                <Label Text="⚠️" 
                       FontSize="80" 
                       HorizontalOptions="Center" />
                
                <Label Text="Safe Mode" 
                       FontSize="32" 
                       FontAttributes="Bold" 
                       TextColor="White" 
                       HorizontalOptions="Center" />
                
                <Label Text="Studio 311 LP - Smart Cat Feeder" 
                       FontSize="16" 
                       TextColor="LightGray" 
                       HorizontalOptions="Center" />
            </StackLayout>

            <!-- Error Information -->
            <Frame BackgroundColor="White" 
                   CornerRadius="10" 
                   Padding="20" 
                   HasShadow="True">
                
                <StackLayout Spacing="15">
                    <Label Text="App Crash Detected" 
                           FontSize="20" 
                           FontAttributes="Bold" 
                           TextColor="#FF6B6B" />

                    <Label Text="The app encountered an error and is running in safe mode to help diagnose the issue." 
                           FontSize="14" 
                           TextColor="#666" 
                           LineBreakMode="WordWrap" />

                    <!-- Error Details Section -->
                    <StackLayout x:Name="ErrorDetailsSection" IsVisible="False" Spacing="10">
                        <Label Text="Error Details:" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               TextColor="#333" />
                        
                        <Frame BackgroundColor="#FFF5F5" 
                               CornerRadius="5" 
                               Padding="10" 
                               HasShadow="False">
                            <Label x:Name="ErrorDetailsLabel" 
                                   Text="No error details available" 
                                   FontSize="12" 
                                   FontFamily="Courier" 
                                   TextColor="#D32F2F" 
                                   LineBreakMode="WordWrap" />
                        </Frame>
                    </StackLayout>

                    <!-- System Information -->
                    <StackLayout x:Name="SystemInfoSection" Spacing="8">
                        <Label Text="System Information:" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               TextColor="#333" />
                        
                        <Label x:Name="SystemInfoLabel" 
                               Text="Loading system info..." 
                               FontSize="12" 
                               TextColor="#666" 
                               LineBreakMode="WordWrap" />
                    </StackLayout>

                    <!-- Diagnostic Steps -->
                    <StackLayout Spacing="8">
                        <Label Text="Diagnostic Steps:" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               TextColor="#333" />
                        
                        <StackLayout x:Name="DiagnosticSteps" Spacing="5">
                            <!-- Steps will be added dynamically -->
                        </StackLayout>
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!-- Action Buttons -->
            <StackLayout Spacing="10">
                <Button x:Name="ShowErrorDetailsButton"
                        Text="Show Error Details"
                        BackgroundColor="#FF8C00"
                        TextColor="White"
                        FontSize="16"
                        CornerRadius="25"
                        HeightRequest="50"
                        Clicked="OnShowErrorDetailsClicked" />

                <Button x:Name="TryAgainButton"
                        Text="Try Starting App Again"
                        BackgroundColor="#4CAF50"
                        TextColor="White"
                        FontSize="16"
                        FontAttributes="Bold"
                        CornerRadius="25"
                        HeightRequest="50"
                        Clicked="OnTryAgainClicked" />

                <Button x:Name="SafeModeButton"
                        Text="Continue in Safe Mode"
                        BackgroundColor="#2196F3"
                        TextColor="White"
                        FontSize="16"
                        CornerRadius="25"
                        HeightRequest="50"
                        Clicked="OnSafeModeClicked" />

                <Button x:Name="SendReportButton"
                        Text="Send Error Report"
                        BackgroundColor="#9C27B0"
                        TextColor="White"
                        FontSize="16"
                        CornerRadius="25"
                        HeightRequest="50"
                        Clicked="OnSendReportClicked" />
            </StackLayout>

            <!-- Help Information -->
            <Frame BackgroundColor="#E3F2FD" 
                   CornerRadius="8" 
                   Padding="15" 
                   HasShadow="False">
                
                <StackLayout Spacing="8">
                    <Label Text="💡 Troubleshooting Tips" 
                           FontSize="14" 
                           FontAttributes="Bold" 
                           TextColor="#1976D2" />
                    
                    <Label Text="• Restart your device and try again" 
                           FontSize="12" 
                           TextColor="#424242" />
                    
                    <Label Text="• Check if you have enough storage space" 
                           FontSize="12" 
                           TextColor="#424242" />
                    
                    <Label Text="• Ensure Bluetooth and Location permissions are granted" 
                           FontSize="12" 
                           TextColor="#424242" />
                    
                    <Label Text="• Try uninstalling and reinstalling the app" 
                           FontSize="12" 
                           TextColor="#424242" />
                    
                    <Label Text="• Contact Studio 311 LP support if the issue persists" 
                           FontSize="12" 
                           TextColor="#424242" />
                </StackLayout>
            </Frame>

        </StackLayout>
    </ScrollView>

</ContentPage>
