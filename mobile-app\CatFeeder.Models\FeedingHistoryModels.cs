using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace CatFeeder.Models
{
    /// <summary>
    /// Feeding result types
    /// </summary>
    public enum FeedingResult
    {
        Success = 0,
        MotorError = 1,
        SensorError = 2,
        PortionTooLarge = 3,
        DailyLimitReached = 4,
        ManualStop = 5,
        UnknownError = 6
    }

    /// <summary>
    /// Feeding trigger types
    /// </summary>
    public enum FeedingTrigger
    {
        Manual = 0,
        Scheduled = 1,
        Bluetooth = 2,
        Web = 3,
        Button = 4
    }

    /// <summary>
    /// Single feeding history entry
    /// </summary>
    public class FeedingEntry
    {
        [Key]
        public uint Id { get; set; }

        public DateTime Timestamp { get; set; }

        public uint PortionSize { get; set; }

        public uint ActualPortion { get; set; }

        public FeedingResult Result { get; set; }

        public FeedingTrigger Trigger { get; set; }

        public uint DurationMs { get; set; }

        public float FoodLevelBefore { get; set; }

        public float FoodLevelAfter { get; set; }

        public string User { get; set; } = "";

        public string Notes { get; set; } = "";

        /// <summary>
        /// Device ID that recorded this feeding (for multi-device support)
        /// </summary>
        public string DeviceId { get; set; } = "";

        /// <summary>
        /// When this entry was synced from the device
        /// </summary>
        public DateTime SyncedAt { get; set; }

        /// <summary>
        /// Whether this entry was successfully synced
        /// </summary>
        public bool IsSynced { get; set; }

        /// <summary>
        /// Get human-readable result string
        /// </summary>
        public string ResultString => Result switch
        {
            FeedingResult.Success => "Success",
            FeedingResult.MotorError => "Motor Error",
            FeedingResult.SensorError => "Sensor Error",
            FeedingResult.PortionTooLarge => "Portion Too Large",
            FeedingResult.DailyLimitReached => "Daily Limit Reached",
            FeedingResult.ManualStop => "Manual Stop",
            FeedingResult.UnknownError => "Unknown Error",
            _ => "Invalid Result"
        };

        /// <summary>
        /// Get human-readable trigger string
        /// </summary>
        public string TriggerString => Trigger switch
        {
            FeedingTrigger.Manual => "Manual",
            FeedingTrigger.Scheduled => "Scheduled",
            FeedingTrigger.Bluetooth => "Bluetooth",
            FeedingTrigger.Web => "Web",
            FeedingTrigger.Button => "Button",
            _ => "Unknown"
        };

        /// <summary>
        /// Get success indicator for UI
        /// </summary>
        public bool IsSuccessful => Result == FeedingResult.Success;

        /// <summary>
        /// Get formatted duration string
        /// </summary>
        public string DurationString => DurationMs < 1000 
            ? $"{DurationMs}ms" 
            : $"{DurationMs / 1000.0:F1}s";

        /// <summary>
        /// Get formatted portion string
        /// </summary>
        public string PortionString => ActualPortion != PortionSize
            ? $"{ActualPortion}/{PortionSize} units"
            : $"{ActualPortion} units";

        /// <summary>
        /// Get formatted portion size string (alias for PortionString for XAML compatibility)
        /// </summary>
        public string PortionSizeString => PortionString;

        /// <summary>
        /// Get formatted time string
        /// </summary>
        public string TimeString => Timestamp.ToString("MMM dd, HH:mm");
    }

    /// <summary>
    /// Feeding statistics
    /// </summary>
    public class FeedingStatistics
    {
        public uint TotalFeedings { get; set; }
        public uint FeedingsToday { get; set; }
        public uint FeedingsThisWeek { get; set; }
        public uint FeedingsThisMonth { get; set; }
        public DateTime LastFeedingTime { get; set; }
        public uint LastFeedingPortion { get; set; }
        public FeedingResult LastFeedingResult { get; set; }
        public uint SuccessfulFeedingsToday { get; set; }
        public uint FailedFeedingsToday { get; set; }
        public float AvgPortionSizeWeek { get; set; }
        public float TotalFoodDispensedToday { get; set; }
        public float FoodLevelPercent { get; set; }

        /// <summary>
        /// Success rate today as percentage
        /// </summary>
        public float SuccessRateToday => FeedingsToday > 0 
            ? (SuccessfulFeedingsToday / (float)FeedingsToday) * 100 
            : 0;

        /// <summary>
        /// Get formatted last feeding time
        /// </summary>
        public string LastFeedingTimeString => LastFeedingTime == DateTime.MinValue 
            ? "Never" 
            : LastFeedingTime.ToString("MMM dd, HH:mm");
    }

    /// <summary>
    /// Delta sync request to ESP32
    /// </summary>
    public class SyncRequest
    {
        public uint LastSyncId { get; set; }
        public DateTime LastSyncTime { get; set; }
        public uint MaxEntries { get; set; } = 10;
    }

    /// <summary>
    /// Delta sync response from ESP32
    /// </summary>
    public class SyncResponse
    {
        public uint LatestId { get; set; }
        public uint EntryCount { get; set; }
        public List<FeedingEntry> Entries { get; set; } = new();
        public bool HasMore { get; set; }
        public FeedingStatistics CurrentStats { get; set; } = new();
    }

    /// <summary>
    /// Feeding history analytics for charts and insights
    /// </summary>
    public class FeedingAnalytics
    {
        public List<DailyFeedingData> DailyData { get; set; } = new();
        public List<HourlyFeedingData> HourlyData { get; set; } = new();
        public List<WeeklyFeedingData> WeeklyData { get; set; } = new();
        public List<MonthlyFeedingData> MonthlyData { get; set; } = new();
    }

    /// <summary>
    /// Daily feeding data for analytics
    /// </summary>
    public class DailyFeedingData
    {
        public DateTime Date { get; set; }
        public uint FeedingCount { get; set; }
        public uint SuccessfulFeedings { get; set; }
        public uint FailedFeedings { get; set; }
        public float TotalFood { get; set; }
        public float AvgPortionSize { get; set; }

        public float SuccessRate => FeedingCount > 0 ? (SuccessfulFeedings / (float)FeedingCount) * 100 : 0;
        public string DateString => Date.ToString("MMM dd");
    }

    /// <summary>
    /// Hourly feeding data for analytics
    /// </summary>
    public class HourlyFeedingData
    {
        public int Hour { get; set; }
        public uint FeedingCount { get; set; }
        public float AvgPortionSize { get; set; }

        public string HourString => $"{Hour:D2}:00";
    }

    /// <summary>
    /// Weekly feeding data for analytics
    /// </summary>
    public class WeeklyFeedingData
    {
        public DateTime WeekStart { get; set; }
        public uint FeedingCount { get; set; }
        public uint SuccessfulFeedings { get; set; }
        public float TotalFood { get; set; }
        public float AvgPortionSize { get; set; }

        public string WeekString => $"Week of {WeekStart:MMM dd}";
    }

    /// <summary>
    /// Monthly feeding data for analytics
    /// </summary>
    public class MonthlyFeedingData
    {
        public DateTime Month { get; set; }
        public uint FeedingCount { get; set; }
        public uint SuccessfulFeedings { get; set; }
        public float TotalFood { get; set; }
        public float AvgPortionSize { get; set; }

        public string MonthString => Month.ToString("MMM yyyy");
    }

    /// <summary>
    /// Feeding history filter options
    /// </summary>
    public class FeedingHistoryFilter
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public FeedingResult? Result { get; set; }
        public FeedingTrigger? Trigger { get; set; }
        public string? User { get; set; }
        public string? DeviceId { get; set; }
        public uint? MinPortionSize { get; set; }
        public uint? MaxPortionSize { get; set; }
        public bool SuccessfulOnly { get; set; }
        public bool FailedOnly { get; set; }
    }

    /// <summary>
    /// Feeding history export options
    /// </summary>
    public class FeedingHistoryExport
    {
        public FeedingHistoryFilter Filter { get; set; } = new();
        public ExportFormat Format { get; set; } = ExportFormat.Csv;
        public bool IncludeStatistics { get; set; } = true;
        public bool IncludeCharts { get; set; } = false;
    }

    /// <summary>
    /// Export format options
    /// </summary>
    public enum ExportFormat
    {
        Csv,
        Json,
        Pdf
    }
}
