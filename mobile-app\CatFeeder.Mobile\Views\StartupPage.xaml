<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="CatFeeder.Mobile.Views.StartupPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             Title="Smart Cat Feeder"
             BackgroundColor="#4A90E2">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20" VerticalOptions="CenterAndExpand">
            
            <!-- App Logo and Title -->
            <StackLayout HorizontalOptions="Center" Spacing="10">
                <Image Source="catfeeder_icon.svg"
                       WidthRequest="120"
                       HeightRequest="120"
                       HorizontalOptions="Center" />
                
                <Label Text="Smart Cat Feeder" 
                       FontSize="28" 
                       FontAttributes="Bold" 
                       TextColor="White" 
                       HorizontalOptions="Center" />
                
                <Label Text="ESP32 Bluetooth Control"
                       FontSize="16"
                       TextColor="LightGray"
                       HorizontalOptions="Center" />

                <Label x:Name="VersionLabel"
                       Text="Studio 311 LP - v1.0.5 (Build 6)"
                       FontSize="12"
                       TextColor="LightGray"
                       HorizontalOptions="Center"
                       Margin="0,5,0,0" />
            </StackLayout>

            <!-- Status Section -->
            <Frame BackgroundColor="White" 
                   CornerRadius="10" 
                   Padding="20" 
                   HasShadow="True">
                
                <StackLayout Spacing="15">
                    <Label Text="App Status" 
                           FontSize="20" 
                           FontAttributes="Bold" 
                           TextColor="#333" />

                    <!-- Loading Indicator -->
                    <StackLayout x:Name="LoadingSection" IsVisible="True" Orientation="Horizontal" Spacing="10">
                        <ActivityIndicator IsRunning="True" Color="#4A90E2" />
                        <Label x:Name="LoadingLabel" 
                               Text="Initializing app..." 
                               FontSize="16" 
                               TextColor="#666" 
                               VerticalOptions="Center" />
                    </StackLayout>

                    <!-- Error Section -->
                    <StackLayout x:Name="ErrorSection" IsVisible="False" Spacing="10">
                        <Label Text="⚠️ Setup Required" 
                               FontSize="18" 
                               FontAttributes="Bold" 
                               TextColor="#FF6B6B" />
                        
                        <Label x:Name="ErrorLabel" 
                               Text="Some permissions are required for the app to work properly." 
                               FontSize="14" 
                               TextColor="#666" 
                               LineBreakMode="WordWrap" />
                    </StackLayout>

                    <!-- Permission Status -->
                    <StackLayout x:Name="PermissionSection" IsVisible="False" Spacing="8">
                        <Label Text="Required Permissions:" 
                               FontSize="16" 
                               FontAttributes="Bold" 
                               TextColor="#333" />

                        <StackLayout x:Name="PermissionList" Spacing="5">
                            <!-- Permission items will be added dynamically -->
                        </StackLayout>
                    </StackLayout>

                    <!-- Success Section -->
                    <StackLayout x:Name="SuccessSection" IsVisible="False" Spacing="10">
                        <Label Text="✅ Ready to Go!" 
                               FontSize="18" 
                               FontAttributes="Bold" 
                               TextColor="#4CAF50" />
                        
                        <Label Text="All permissions granted. You can now connect to your ESP32 Cat Feeder." 
                               FontSize="14" 
                               TextColor="#666" 
                               LineBreakMode="WordWrap" />
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!-- Action Buttons -->
            <StackLayout Spacing="10">
                <Button x:Name="RequestPermissionsButton"
                        Text="Grant Permissions"
                        BackgroundColor="#FF8C00"
                        TextColor="White"
                        FontSize="16"
                        FontAttributes="Bold"
                        CornerRadius="25"
                        HeightRequest="50"
                        IsVisible="False"
                        Clicked="OnRequestPermissionsClicked" />

                <Button x:Name="OpenSettingsButton"
                        Text="Open App Settings"
                        BackgroundColor="#FF6B6B"
                        TextColor="White"
                        FontSize="16"
                        CornerRadius="25"
                        HeightRequest="50"
                        IsVisible="False"
                        Clicked="OnOpenSettingsClicked" />

                <Button x:Name="ContinueButton"
                        Text="Continue to App"
                        BackgroundColor="#4CAF50"
                        TextColor="White"
                        FontSize="16"
                        FontAttributes="Bold"
                        CornerRadius="25"
                        HeightRequest="50"
                        IsVisible="False"
                        Clicked="OnContinueClicked" />

                <Button x:Name="RetryButton"
                        Text="Check Again"
                        BackgroundColor="#2196F3"
                        TextColor="White"
                        FontSize="16"
                        CornerRadius="25"
                        HeightRequest="50"
                        IsVisible="False"
                        Clicked="OnRetryClicked" />
            </StackLayout>

            <!-- Help Text -->
            <Frame BackgroundColor="#E3F2FD" 
                   CornerRadius="8" 
                   Padding="15" 
                   HasShadow="False">
                
                <StackLayout Spacing="8">
                    <Label Text="💡 Why do we need these permissions?" 
                           FontSize="14" 
                           FontAttributes="Bold" 
                           TextColor="#1976D2" />
                    
                    <Label Text="• Bluetooth: To connect to your ESP32 Cat Feeder device" 
                           FontSize="12" 
                           TextColor="#424242" />
                    
                    <Label Text="• Location: Required by Android for Bluetooth device scanning" 
                           FontSize="12" 
                           TextColor="#424242" />
                    
                    <Label Text="• Network: To check WiFi status and connectivity" 
                           FontSize="12" 
                           TextColor="#424242" />
                </StackLayout>
            </Frame>

        </StackLayout>
    </ScrollView>

</ContentPage>
