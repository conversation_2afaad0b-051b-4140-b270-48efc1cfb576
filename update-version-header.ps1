# PowerShell script to update version.h from version.json
# Studio 311 LP - CatFeeder Version Management System

param(
    [string]$VersionFile = "version.json",
    [string]$HeaderFile = "esp32-firmware/main/version.h"
)

$ErrorActionPreference = "Stop"

Write-Host "=== Studio 311 LP - Version Header Generator ===" -ForegroundColor Cyan

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$VersionPath = Join-Path $ScriptDir $VersionFile
$HeaderPath = Join-Path $ScriptDir $HeaderFile

if (-not (Test-Path $VersionPath)) {
    Write-Error "Version file not found: $VersionPath"
    exit 1
}

# Read version data
$versionData = Get-Content $VersionPath | ConvertFrom-Json

Write-Host "Updating version header from: $VersionPath" -ForegroundColor Yellow
Write-Host "Target header file: $HeaderPath" -ForegroundColor Yellow
Write-Host "Version: $($versionData.version)" -ForegroundColor Green

# Generate header content
$headerContent = @"
/**
 * @file version.h
 * @brief Version information for CatFeeder ESP32 Firmware
 * <AUTHOR> 311 LP
 * 
 * This file is auto-generated by the build system.
 * Do not edit manually - changes will be overwritten.
 * Generated: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
 */

#ifndef VERSION_H
#define VERSION_H

#ifdef __cplusplus
extern "C" {
#endif

// Version information - auto-generated from version.json
#define VERSION_MAJOR $($versionData.major)
#define VERSION_MINOR $($versionData.minor)
#define VERSION_PATCH $($versionData.patch)
#define VERSION_BUILD $($versionData.build)

#define VERSION_STRING "$($versionData.version)"
#define COMPANY_NAME "$($versionData.company)"
#define PRODUCT_NAME "CatFeeder ESP32"

// Build information
#define BUILD_DATE __DATE__
#define BUILD_TIME __TIME__

// Version as integer for comparison (MAJOR.MINOR.PATCH.BUILD)
#define VERSION_INT (($($versionData.major) << 24) | ($($versionData.minor) << 16) | ($($versionData.patch) << 8) | $($versionData.build))

/**
 * @brief Get version string
 * @return Pointer to version string
 */
const char* get_version_string(void);

/**
 * @brief Get full version info including build date/time
 * @return Pointer to full version info string
 */
const char* get_full_version_info(void);

/**
 * @brief Get version as integer for comparison
 * @return Version as 32-bit integer
 */
uint32_t get_version_int(void);

#ifdef __cplusplus
}
#endif

#endif // VERSION_H
"@

# Write header file
Set-Content -Path $HeaderPath -Value $headerContent -Encoding UTF8

Write-Host "Version header updated successfully!" -ForegroundColor Green
Write-Host "Header file: $HeaderPath" -ForegroundColor Gray
