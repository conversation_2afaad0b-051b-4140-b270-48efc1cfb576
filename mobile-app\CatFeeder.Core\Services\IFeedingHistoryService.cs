using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CatFeeder.Models;

namespace CatFeeder.Core.Services
{
    /// <summary>
    /// Feeding history service interface for local storage and analytics
    /// </summary>
    public interface IFeedingHistoryService
    {
        /// <summary>
        /// Feeding history updated event
        /// </summary>
        event EventHandler<FeedingHistoryEventArgs> HistoryUpdated;

        /// <summary>
        /// Initialize the feeding history service
        /// </summary>
        Task InitializeAsync();

        /// <summary>
        /// Add a new feeding entry to local storage
        /// </summary>
        Task AddFeedingEntryAsync(FeedingEntry entry);

        /// <summary>
        /// Get all feeding entries with optional filtering
        /// </summary>
        Task<List<FeedingEntry>> GetFeedingEntriesAsync(FeedingHistoryFilter? filter = null);

        /// <summary>
        /// Get feeding entries for a specific date range
        /// </summary>
        Task<List<FeedingEntry>> GetFeedingEntriesAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Get recent feeding entries
        /// </summary>
        Task<List<FeedingEntry>> GetRecentFeedingEntriesAsync(int count = 10);

        /// <summary>
        /// Get feeding statistics
        /// </summary>
        Task<FeedingStatistics> GetFeedingStatisticsAsync();

        /// <summary>
        /// Get feeding analytics data
        /// </summary>
        Task<FeedingAnalytics> GetFeedingAnalyticsAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Sync with device and update local storage
        /// </summary>
        Task<SyncResponse> SyncWithDeviceAsync(IBluetoothService bluetoothService, string deviceId);

        /// <summary>
        /// Get the last sync ID for a device
        /// </summary>
        Task<uint> GetLastSyncIdAsync(string deviceId);

        /// <summary>
        /// Update the last sync ID for a device
        /// </summary>
        Task UpdateLastSyncIdAsync(string deviceId, uint syncId);

        /// <summary>
        /// Clear all feeding history (with confirmation)
        /// </summary>
        Task ClearAllHistoryAsync();

        /// <summary>
        /// Export feeding history
        /// </summary>
        Task<string> ExportFeedingHistoryAsync(FeedingHistoryExport exportOptions);

        /// <summary>
        /// Get feeding count for today
        /// </summary>
        Task<int> GetTodayFeedingCountAsync();

        /// <summary>
        /// Get last feeding entry
        /// </summary>
        Task<FeedingEntry?> GetLastFeedingEntryAsync();

        /// <summary>
        /// Check if feeding limit is reached for today
        /// </summary>
        Task<bool> IsDailyLimitReachedAsync(int dailyLimit);
    }

    /// <summary>
    /// Feeding history service implementation using SQLite
    /// </summary>
    public class FeedingHistoryService : IFeedingHistoryService
    {
        public event EventHandler<FeedingHistoryEventArgs>? HistoryUpdated;

        private readonly List<FeedingEntry> _feedingHistory = new();
        private readonly Dictionary<string, uint> _lastSyncIds = new();
        private bool _isInitialized = false;

        public async Task InitializeAsync()
        {
            if (_isInitialized) return;

            // In a real implementation, this would initialize SQLite database
            await Task.Delay(100);

            // Load existing data from local storage
            await LoadFromLocalStorageAsync();

            _isInitialized = true;
        }

        public async Task AddFeedingEntryAsync(FeedingEntry entry)
        {
            if (!_isInitialized) await InitializeAsync();

            entry.SyncedAt = DateTime.Now;
            entry.IsSynced = true;

            _feedingHistory.Add(entry);

            // In a real implementation, save to SQLite
            await SaveToLocalStorageAsync();

            OnHistoryUpdated(new FeedingHistoryEventArgs 
            { 
                Action = HistoryAction.Added, 
                Entry = entry 
            });
        }

        public async Task<List<FeedingEntry>> GetFeedingEntriesAsync(FeedingHistoryFilter? filter = null)
        {
            if (!_isInitialized) await InitializeAsync();

            var entries = new List<FeedingEntry>(_feedingHistory);

            if (filter != null)
            {
                entries = ApplyFilter(entries, filter);
            }

            return entries.OrderByDescending(e => e.Timestamp).ToList();
        }

        public async Task<List<FeedingEntry>> GetFeedingEntriesAsync(DateTime startDate, DateTime endDate)
        {
            if (!_isInitialized) await InitializeAsync();

            return _feedingHistory
                .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
                .OrderByDescending(e => e.Timestamp)
                .ToList();
        }

        public async Task<List<FeedingEntry>> GetRecentFeedingEntriesAsync(int count = 10)
        {
            if (!_isInitialized) await InitializeAsync();

            return _feedingHistory
                .OrderByDescending(e => e.Timestamp)
                .Take(count)
                .ToList();
        }

        public async Task<FeedingStatistics> GetFeedingStatisticsAsync()
        {
            if (!_isInitialized) await InitializeAsync();

            var now = DateTime.Now;
            var today = now.Date;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(today.Year, today.Month, 1);

            var todayEntries = _feedingHistory.Where(e => e.Timestamp.Date == today).ToList();
            var weekEntries = _feedingHistory.Where(e => e.Timestamp >= weekStart).ToList();
            var monthEntries = _feedingHistory.Where(e => e.Timestamp >= monthStart).ToList();

            var lastEntry = _feedingHistory.OrderByDescending(e => e.Timestamp).FirstOrDefault();

            return new FeedingStatistics
            {
                TotalFeedings = (uint)_feedingHistory.Count,
                FeedingsToday = (uint)todayEntries.Count,
                FeedingsThisWeek = (uint)weekEntries.Count,
                FeedingsThisMonth = (uint)monthEntries.Count,
                LastFeedingTime = lastEntry?.Timestamp ?? DateTime.MinValue,
                LastFeedingPortion = lastEntry?.ActualPortion ?? 0,
                LastFeedingResult = lastEntry?.Result ?? FeedingResult.UnknownError,
                SuccessfulFeedingsToday = (uint)todayEntries.Count(e => e.Result == FeedingResult.Success),
                FailedFeedingsToday = (uint)todayEntries.Count(e => e.Result != FeedingResult.Success),
                AvgPortionSizeWeek = weekEntries.Any() ? (float)weekEntries.Average(e => e.ActualPortion) : 0,
                TotalFoodDispensedToday = todayEntries.Sum(e => e.ActualPortion)
            };
        }

        public async Task<FeedingAnalytics> GetFeedingAnalyticsAsync(DateTime startDate, DateTime endDate)
        {
            if (!_isInitialized) await InitializeAsync();

            var entries = _feedingHistory
                .Where(e => e.Timestamp >= startDate && e.Timestamp <= endDate)
                .ToList();

            var analytics = new FeedingAnalytics();

            // Generate daily data
            var dailyGroups = entries
                .GroupBy(e => e.Timestamp.Date)
                .OrderBy(g => g.Key);

            foreach (var group in dailyGroups)
            {
                var dayEntries = group.ToList();
                analytics.DailyData.Add(new DailyFeedingData
                {
                    Date = group.Key,
                    FeedingCount = (uint)dayEntries.Count,
                    SuccessfulFeedings = (uint)dayEntries.Count(e => e.Result == FeedingResult.Success),
                    FailedFeedings = (uint)dayEntries.Count(e => e.Result != FeedingResult.Success),
                    TotalFood = dayEntries.Sum(e => e.ActualPortion),
                    AvgPortionSize = dayEntries.Any() ? (float)dayEntries.Average(e => e.ActualPortion) : 0
                });
            }

            // Generate hourly data
            var hourlyGroups = entries
                .GroupBy(e => e.Timestamp.Hour)
                .OrderBy(g => g.Key);

            foreach (var group in hourlyGroups)
            {
                var hourEntries = group.ToList();
                analytics.HourlyData.Add(new HourlyFeedingData
                {
                    Hour = group.Key,
                    FeedingCount = (uint)hourEntries.Count,
                    AvgPortionSize = hourEntries.Any() ? (float)hourEntries.Average(e => e.ActualPortion) : 0
                });
            }

            return analytics;
        }

        public async Task<SyncResponse> SyncWithDeviceAsync(IBluetoothService bluetoothService, string deviceId)
        {
            if (!_isInitialized) await InitializeAsync();

            var lastSyncId = await GetLastSyncIdAsync(deviceId);
            
            var request = new SyncRequest
            {
                LastSyncId = lastSyncId,
                LastSyncTime = DateTime.Now,
                MaxEntries = 20
            };

            var response = await bluetoothService.SyncFeedingHistoryAsync(request);

            // Add new entries to local storage
            foreach (var entry in response.Entries)
            {
                entry.DeviceId = deviceId;
                await AddFeedingEntryAsync(entry);
            }

            // Update last sync ID
            if (response.LatestId > lastSyncId)
            {
                await UpdateLastSyncIdAsync(deviceId, response.LatestId);
            }

            return response;
        }

        public async Task<uint> GetLastSyncIdAsync(string deviceId)
        {
            await Task.Delay(1); // Placeholder for async operation
            return _lastSyncIds.TryGetValue(deviceId, out var syncId) ? syncId : 0;
        }

        public async Task UpdateLastSyncIdAsync(string deviceId, uint syncId)
        {
            _lastSyncIds[deviceId] = syncId;
            // In a real implementation, save to persistent storage
            await Task.Delay(1);
        }

        public async Task ClearAllHistoryAsync()
        {
            if (!_isInitialized) await InitializeAsync();

            _feedingHistory.Clear();
            _lastSyncIds.Clear();

            await SaveToLocalStorageAsync();

            OnHistoryUpdated(new FeedingHistoryEventArgs 
            { 
                Action = HistoryAction.Cleared 
            });
        }

        public async Task<string> ExportFeedingHistoryAsync(FeedingHistoryExport exportOptions)
        {
            var entries = await GetFeedingEntriesAsync(exportOptions.Filter);
            
            // In a real implementation, generate CSV/JSON/PDF export
            return $"Exported {entries.Count} feeding entries";
        }

        public async Task<int> GetTodayFeedingCountAsync()
        {
            if (!_isInitialized) await InitializeAsync();

            var today = DateTime.Now.Date;
            return _feedingHistory.Count(e => e.Timestamp.Date == today);
        }

        public async Task<FeedingEntry?> GetLastFeedingEntryAsync()
        {
            if (!_isInitialized) await InitializeAsync();

            return _feedingHistory.OrderByDescending(e => e.Timestamp).FirstOrDefault();
        }

        public async Task<bool> IsDailyLimitReachedAsync(int dailyLimit)
        {
            var todayCount = await GetTodayFeedingCountAsync();
            return todayCount >= dailyLimit;
        }

        private List<FeedingEntry> ApplyFilter(List<FeedingEntry> entries, FeedingHistoryFilter filter)
        {
            var filtered = entries.AsEnumerable();

            if (filter.StartDate.HasValue)
                filtered = filtered.Where(e => e.Timestamp >= filter.StartDate.Value);

            if (filter.EndDate.HasValue)
                filtered = filtered.Where(e => e.Timestamp <= filter.EndDate.Value);

            if (filter.Result.HasValue)
                filtered = filtered.Where(e => e.Result == filter.Result.Value);

            if (filter.Trigger.HasValue)
                filtered = filtered.Where(e => e.Trigger == filter.Trigger.Value);

            if (!string.IsNullOrEmpty(filter.User))
                filtered = filtered.Where(e => e.User.Contains(filter.User, StringComparison.OrdinalIgnoreCase));

            if (!string.IsNullOrEmpty(filter.DeviceId))
                filtered = filtered.Where(e => e.DeviceId == filter.DeviceId);

            if (filter.SuccessfulOnly)
                filtered = filtered.Where(e => e.Result == FeedingResult.Success);

            if (filter.FailedOnly)
                filtered = filtered.Where(e => e.Result != FeedingResult.Success);

            return filtered.ToList();
        }

        private async Task LoadFromLocalStorageAsync()
        {
            // In a real implementation, load from SQLite database
            await Task.Delay(1);
        }

        private async Task SaveToLocalStorageAsync()
        {
            // In a real implementation, save to SQLite database
            await Task.Delay(1);
        }

        protected virtual void OnHistoryUpdated(FeedingHistoryEventArgs e)
        {
            HistoryUpdated?.Invoke(this, e);
        }
    }

    /// <summary>
    /// Feeding history event arguments
    /// </summary>
    public class FeedingHistoryEventArgs : EventArgs
    {
        public HistoryAction Action { get; set; }
        public FeedingEntry? Entry { get; set; }
        public List<FeedingEntry>? Entries { get; set; }
    }

    /// <summary>
    /// History action types
    /// </summary>
    public enum HistoryAction
    {
        Added,
        Updated,
        Deleted,
        Synced,
        Cleared
    }
}
