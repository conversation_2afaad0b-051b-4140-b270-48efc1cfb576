/**
 * Feeding Scheduler Component
 * 
 * Manages feeding schedules and triggers automatic feedings
 */

#pragma once

#include "esp_err.h"
#include <time.h>

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_SCHEDULED_FEEDINGS 8

/**
 * Feeding schedule entry
 */
typedef struct {
    uint8_t hour;                   ///< Hour (0-23)
    uint8_t minute;                 ///< Minute (0-59)
    uint32_t portion_size;          ///< Portion size for this feeding
    bool enabled;                   ///< Whether this schedule entry is enabled
    bool repeat_daily;              ///< Repeat daily
    uint8_t days_of_week;           ///< Days of week bitmask (bit 0 = Sunday)
} feeding_schedule_entry_t;

/**
 * Complete feeding schedule
 */
typedef struct {
    feeding_schedule_entry_t entries[MAX_SCHEDULED_FEEDINGS];
    uint8_t num_entries;            ///< Number of active entries
    bool scheduler_enabled;         ///< Global scheduler enable/disable
} feeding_schedule_t;

/**
 * Scheduler callback function type
 */
typedef void (*scheduler_callback_t)(uint32_t portion_size);

/**
 * Initialize scheduler
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_init(void);

/**
 * Deinitialize scheduler
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_deinit(void);

/**
 * Start scheduler
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_start(void);

/**
 * Stop scheduler
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_stop(void);

/**
 * Set feeding schedule
 * 
 * @param schedule Feeding schedule
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_set_schedule(const feeding_schedule_t *schedule);

/**
 * Get current feeding schedule
 * 
 * @param schedule Pointer to store schedule
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_get_schedule(feeding_schedule_t *schedule);

/**
 * Add feeding schedule entry
 * 
 * @param entry Schedule entry to add
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_add_entry(const feeding_schedule_entry_t *entry);

/**
 * Remove feeding schedule entry
 * 
 * @param index Index of entry to remove
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_remove_entry(uint8_t index);

/**
 * Enable/disable scheduler
 * 
 * @param enabled true to enable, false to disable
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_set_enabled(bool enabled);

/**
 * Check if scheduler is enabled
 * 
 * @return true if enabled, false otherwise
 */
bool scheduler_is_enabled(void);

/**
 * Set scheduler callback function
 * 
 * @param callback Callback function to call when feeding is scheduled
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_set_callback(scheduler_callback_t callback);

/**
 * Get next scheduled feeding time
 * 
 * @param next_time Pointer to store next feeding time
 * @return ESP_OK on success, ESP_ERR_NOT_FOUND if no scheduled feedings
 */
esp_err_t scheduler_get_next_feeding(time_t *next_time);

/**
 * Manually trigger next scheduled feeding
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t scheduler_trigger_next(void);

#ifdef __cplusplus
}
#endif
