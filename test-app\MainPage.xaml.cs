﻿namespace TestApp;

public partial class MainPage : ContentPage
{
	private int _clickCount = 0;

	public MainPage()
	{
		try
		{
			InitializeComponent();
			StatusLabel.Text = $"Initialized at {DateTime.Now:HH:mm:ss}";
		}
		catch (Exception ex)
		{
			Title = $"Error: {ex.Message}";
		}
	}

	private void OnTestButtonClicked(object? sender, EventArgs e)
	{
		try
		{
			_clickCount++;
			ResultLabel.Text = $"Button clicked {_clickCount} times. Last: {DateTime.Now:HH:mm:ss}";

			if (_clickCount == 1)
			{
				ResultLabel.TextColor = Colors.Green;
				StatusLabel.Text = "✅ Basic functionality working!";
			}
			else if (_clickCount == 5)
			{
				ResultLabel.Text += "\n🎉 Test successful!";
				ResultLabel.TextColor = Colors.Blue;
				StatusLabel.Text = "✅ Studio 311 LP - Test Complete!";
			}
		}
		catch (Exception ex)
		{
			ResultLabel.Text = $"Error: {ex.Message}";
			ResultLabel.TextColor = Colors.Red;
		}
	}
}
