#include "app_config.h"
#include "esp_log.h"

static const char *TAG = "app_config";

// Default configuration values
static app_config_t current_config = {
    .device_name = "CatFeeder_ESP32",
    .bluetooth_pin = "1234",
    .wifi_ssid = "",
    .wifi_password = "",
    .motor_type = MOTOR_TYPE_STEPPER,
    .feeding_schedules_count = 0,
    .level_sensor_enabled = false,
    .led_indicators_enabled = true,
    .auto_feed_enabled = false,
    .manual_feed_portion_size = 5
};

esp_err_t app_config_init(void)
{
    ESP_LOGI(TAG, "Initializing application configuration");
    
    // Initialize with default values
    strcpy(current_config.device_name, "CatFeeder_ESP32");
    strcpy(current_config.bluetooth_pin, "1234");
    current_config.motor_type = MOTOR_TYPE_STEPPER;
    current_config.feeding_schedules_count = 0;
    current_config.level_sensor_enabled = false;
    current_config.led_indicators_enabled = true;
    current_config.auto_feed_enabled = false;
    current_config.manual_feed_portion_size = 5;
    
    ESP_LOGI(TAG, "Application configuration initialized");
    ESP_LOGI(TAG, "Device Name: %s", current_config.device_name);
    ESP_LOGI(TAG, "Bluetooth PIN: %s", current_config.bluetooth_pin);
    ESP_LOGI(TAG, "Motor Type: %d", current_config.motor_type);
    
    return ESP_OK;
}

app_config_t* app_config_get(void)
{
    return &current_config;
}

esp_err_t app_config_set_device_name(const char* name)
{
    if (name == NULL || strlen(name) >= sizeof(current_config.device_name)) {
        return ESP_ERR_INVALID_ARG;
    }
    
    strcpy(current_config.device_name, name);
    ESP_LOGI(TAG, "Device name set to: %s", name);
    return ESP_OK;
}

esp_err_t app_config_set_bluetooth_pin(const char* pin)
{
    if (pin == NULL || strlen(pin) >= sizeof(current_config.bluetooth_pin)) {
        return ESP_ERR_INVALID_ARG;
    }
    
    strcpy(current_config.bluetooth_pin, pin);
    ESP_LOGI(TAG, "Bluetooth PIN set to: %s", pin);
    return ESP_OK;
}

esp_err_t app_config_set_wifi_credentials(const char* ssid, const char* password)
{
    if (ssid == NULL || password == NULL || 
        strlen(ssid) >= sizeof(current_config.wifi_ssid) ||
        strlen(password) >= sizeof(current_config.wifi_password)) {
        return ESP_ERR_INVALID_ARG;
    }
    
    strcpy(current_config.wifi_ssid, ssid);
    strcpy(current_config.wifi_password, password);
    ESP_LOGI(TAG, "WiFi credentials set for SSID: %s", ssid);
    return ESP_OK;
}

esp_err_t app_config_set_motor_type(motor_type_t motor_type)
{
    if (motor_type < MOTOR_TYPE_STEPPER || motor_type > MOTOR_TYPE_SERVO) {
        return ESP_ERR_INVALID_ARG;
    }
    
    current_config.motor_type = motor_type;
    ESP_LOGI(TAG, "Motor type set to: %d", motor_type);
    return ESP_OK;
}

esp_err_t app_config_add_feeding_schedule(uint8_t hour, uint8_t minute, uint8_t portion_size)
{
    if (current_config.feeding_schedules_count >= MAX_FEEDING_SCHEDULES) {
        ESP_LOGE(TAG, "Maximum feeding schedules reached");
        return ESP_ERR_NO_MEM;
    }
    
    if (hour > 23 || minute > 59 || portion_size == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    feeding_schedule_t* schedule = &current_config.feeding_schedules[current_config.feeding_schedules_count];
    schedule->hour = hour;
    schedule->minute = minute;
    schedule->portion_size = portion_size;
    schedule->enabled = true;
    
    current_config.feeding_schedules_count++;
    ESP_LOGI(TAG, "Added feeding schedule: %02d:%02d, portion: %d", hour, minute, portion_size);
    
    return ESP_OK;
}

esp_err_t app_config_remove_feeding_schedule(uint8_t hour, uint8_t minute)
{
    for (int i = 0; i < current_config.feeding_schedules_count; i++) {
        if (current_config.feeding_schedules[i].hour == hour && 
            current_config.feeding_schedules[i].minute == minute) {
            
            // Shift remaining schedules down
            for (int j = i; j < current_config.feeding_schedules_count - 1; j++) {
                current_config.feeding_schedules[j] = current_config.feeding_schedules[j + 1];
            }
            
            current_config.feeding_schedules_count--;
            ESP_LOGI(TAG, "Removed feeding schedule: %02d:%02d", hour, minute);
            return ESP_OK;
        }
    }
    
    ESP_LOGW(TAG, "Feeding schedule not found: %02d:%02d", hour, minute);
    return ESP_ERR_NOT_FOUND;
}

esp_err_t app_config_clear_feeding_schedules(void)
{
    current_config.feeding_schedules_count = 0;
    ESP_LOGI(TAG, "All feeding schedules cleared");
    return ESP_OK;
}

esp_err_t app_config_set_level_sensor_enabled(bool enabled)
{
    current_config.level_sensor_enabled = enabled;
    ESP_LOGI(TAG, "Level sensor %s", enabled ? "enabled" : "disabled");
    return ESP_OK;
}

esp_err_t app_config_set_led_indicators_enabled(bool enabled)
{
    current_config.led_indicators_enabled = enabled;
    ESP_LOGI(TAG, "LED indicators %s", enabled ? "enabled" : "disabled");
    return ESP_OK;
}

esp_err_t app_config_set_auto_feed_enabled(bool enabled)
{
    current_config.auto_feed_enabled = enabled;
    ESP_LOGI(TAG, "Auto feed %s", enabled ? "enabled" : "disabled");
    return ESP_OK;
}

esp_err_t app_config_set_manual_feed_portion_size(uint8_t portion_size)
{
    if (portion_size == 0 || portion_size > 20) {
        return ESP_ERR_INVALID_ARG;
    }
    
    current_config.manual_feed_portion_size = portion_size;
    ESP_LOGI(TAG, "Manual feed portion size set to: %d", portion_size);
    return ESP_OK;
}
