# ESP32 Cat Feeder Firmware Configuration

# Enable Bluetooth
CONFIG_BT_ENABLED=y
CONFIG_BT_BLUEDROID_ENABLED=y
CONFIG_BT_CLASSIC_ENABLED=y
CONFIG_BT_SPP_ENABLED=y
CONFIG_BT_BTC_TASK_STACK_SIZE=3072
CONFIG_BT_BTU_TASK_STACK_SIZE=4096

# Bluetooth Controller Configuration
CONFIG_BTDM_CTRL_MODE_BLE_ONLY=n
CONFIG_BTDM_CTRL_MODE_BR_EDR_ONLY=y
CONFIG_BTDM_CTRL_MODE_BTDM=n
CONFIG_BTDM_CTRL_BLE_MAX_CONN=0
CONFIG_BTDM_CTRL_BR_EDR_MAX_ACL_CONN=2
CONFIG_BTDM_CTRL_BR_EDR_MAX_SYNC_CONN=0

# WiFi Configuration
CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE=y
CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM=10
CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=32

# HTTP Server
CONFIG_HTTPD_MAX_REQ_HDR_LEN=1024
CONFIG_HTTPD_MAX_URI_LEN=512

# NVS
CONFIG_NVS_ENCRYPTION=n

# FreeRTOS
CONFIG_FREERTOS_HZ=1000

# Main task stack size
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192

# Component config
CONFIG_ESP_TIMER_TASK_STACK_SIZE=4096

# Partition Table
CONFIG_PARTITION_TABLE_SINGLE_APP=y

# Serial flasher config
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
