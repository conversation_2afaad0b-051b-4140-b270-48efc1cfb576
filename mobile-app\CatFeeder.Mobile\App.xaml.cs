﻿using CatFeeder.Core.Services;
using CatFeeder.Mobile.Views;

namespace CatFeeder.Mobile;

public partial class App : Application
{
    private ILoggingService? _loggingService;

    public App()
    {
        try
        {
            InitializeComponent();

            // Set up global exception handling
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
        }
        catch (Exception ex)
        {
            // Critical failure during app initialization
            System.Diagnostics.Debug.WriteLine($"CRITICAL: App initialization failed: {ex}");
        }
    }

    protected override Window CreateWindow(IActivationState? activationState)
    {
        try
        {
            // Try to get the logging service
            _loggingService = Handler?.MauiContext?.Services?.GetService<ILoggingService>();
            _loggingService?.LogInfo("Application starting");

            // Start with the actual Cat Feeder startup page
            return new Window(new StartupPage());
        }
        catch (Exception ex)
        {
            // Log the error
            System.Diagnostics.Debug.WriteLine($"MINIMAL TEST FAILED: {ex}");
            _loggingService?.LogCritical("Failed to create main window", ex);

            // If even minimal page fails, go directly to crash safe mode
            try
            {
                return new Window(new CrashSafePage(ex));
            }
            catch
            {
                // Last resort - create the most basic page possible
                return new Window(new ContentPage
                {
                    Title = "Critical Error",
                    BackgroundColor = Colors.Red,
                    Content = new Label
                    {
                        Text = "CRITICAL ERROR\nStudio 311 LP\nSmart Cat Feeder\n\nApp failed to start.\nPlease contact support.",
                        HorizontalOptions = LayoutOptions.Center,
                        VerticalOptions = LayoutOptions.Center,
                        TextColor = Colors.White,
                        FontSize = 16,
                        HorizontalTextAlignment = TextAlignment.Center
                    }
                });
            }
        }
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            var exception = e.ExceptionObject as Exception;
            System.Diagnostics.Debug.WriteLine($"UNHANDLED EXCEPTION: {exception}");

            // Switch to crash safe mode
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    MainPage = new CrashSafePage(exception);
                }
                catch
                {
                    // Last resort - minimal error page
                    MainPage = new ContentPage
                    {
                        Title = "Critical Error",
                        Content = new Label
                        {
                            Text = "Studio 311 LP - Smart Cat Feeder\n\nCritical error occurred.\nPlease restart the app.",
                            HorizontalOptions = LayoutOptions.Center,
                            VerticalOptions = LayoutOptions.Center,
                            TextColor = Colors.Red,
                            FontSize = 16
                        }
                    };
                }
            });
        }
        catch
        {
            // Can't even handle the exception handler failure
            System.Diagnostics.Debug.WriteLine("CRITICAL: Exception handler failed");
        }
    }

    private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"UNOBSERVED TASK EXCEPTION: {e.Exception}");
            e.SetObserved(); // Prevent app termination

            // Switch to crash safe mode
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    MainPage = new CrashSafePage(e.Exception);
                }
                catch
                {
                    // Ignore if we can't even show the crash page
                }
            });
        }
        catch
        {
            // Can't handle task exception
            System.Diagnostics.Debug.WriteLine("CRITICAL: Task exception handler failed");
        }
    }
}
