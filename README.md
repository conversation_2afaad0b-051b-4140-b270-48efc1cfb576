# ESP32 Cat Feeder Project

A smart cat feeder system with ESP32 microcontroller and MAUI mobile app for remote control.

## Project Overview

This project consists of two main components:
1. **ESP32 Firmware** - Controls the feeding mechanism, WiFi/Bluetooth connectivity
2. **MAUI Mobile App** - Provides remote control and scheduling interface

## Features

### ESP32 Firmware Features
- **Motor Control**: Support for both stepper and DC motors
  - Stepper: Precise portion control with step counting
  - DC: Time-based or sensor-based control
- **WiFi Connectivity**: Remote control via HTTP API
- **Bluetooth**: Local configuration and backup control
- **Real-time Scheduling**: Automated feeding times
- **Web Server**: RESTful API for mobile app communication
- **Optional Components**:
  - Food level sensor (load cell)
  - LED status indicators
  - Buzzer notifications

### Mobile App Features
- Schedule feeding times
- Manual feeding control
- Real-time device status
- Feeding history
- Device configuration
- Push notifications

## Hardware Requirements

### Core Components
- ESP32 development board (ESP32-DevKitC recommended)
- Motor (Stepper or DC)
- Motor driver (A4988 for stepper, L298N for DC)
- Power supply (12V for motors, 5V for ESP32)
- Food dispensing mechanism

### Optional Components
- Load cell + HX711 (food level monitoring)
- LEDs (status indication)
- Buzzer (audio notifications)
- Limit switch (DC motor stop detection)

## Development Environment

### ESP32 Development
- **Framework**: ESP-IDF v5.4.1
- **IDE**: VS Code with ESP-IDF extension
- **Language**: C/C++

### Mobile App Development
- **Framework**: .NET 8 MAUI
- **IDE**: Visual Studio 2022 or VS Code
- **Language**: C#

## Project Structure

```
CatFeederV2/
├── esp32-firmware/              # ESP-IDF project
│   ├── main/                    # Main application code
│   ├── components/              # Custom components
│   │   ├── motor_control/       # Motor abstraction layer
│   │   ├── wifi_manager/        # WiFi connectivity
│   │   ├── bluetooth_manager/   # Bluetooth connectivity
│   │   ├── web_server/          # HTTP API server
│   │   ├── scheduler/           # Feeding scheduler
│   │   └── config/              # Configuration management
│   ├── CMakeLists.txt
│   └── sdkconfig
├── mobile-app/                  # MAUI application
│   ├── CatFeeder.Mobile/        # Main MAUI project
│   ├── CatFeeder.Core/          # Shared business logic
│   └── CatFeeder.Models/        # Data models
├── docs/                        # Documentation
└── README.md
```

## Getting Started

### 1. Install Development Tools

#### ESP-IDF Setup
1. Download ESP-IDF Tools Installer from: https://dl.espressif.com/dl/esp-idf/
2. Run the installer and select ESP-IDF v5.4.1
3. Install VS Code ESP-IDF extension

#### MAUI Setup
1. Install .NET 8 SDK
2. Install MAUI workload: `dotnet workload install maui`
3. Install Visual Studio 2022 with MAUI workload

### 2. Build and Flash ESP32 Firmware

```bash
cd esp32-firmware
idf.py set-target esp32
idf.py menuconfig  # Configure project settings
idf.py build
idf.py -p COM_PORT flash monitor
```

### 3. Build and Run Mobile App

```bash
cd mobile-app/CatFeeder.Mobile
dotnet build
dotnet run  # For development
```

## Configuration

### Motor Configuration
The firmware supports both stepper and DC motors through a unified interface:

- **Stepper Motor**: Configure steps per revolution, microstepping
- **DC Motor**: Configure run time or use limit switch for stop detection

### WiFi Configuration
- SSID and password can be configured via Bluetooth or web interface
- Supports WPA2/WPA3 security

### Feeding Schedule
- Multiple daily feeding times
- Portion size control
- Manual feeding override

## API Documentation

The ESP32 exposes a RESTful API for mobile app communication:

- `GET /api/status` - Get device status
- `POST /api/feed` - Manual feeding
- `GET /api/schedule` - Get feeding schedule
- `POST /api/schedule` - Update feeding schedule
- `GET /api/config` - Get device configuration
- `POST /api/config` - Update device configuration

## License

This project is open source. See LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## Support

For questions and support, please open an issue in the GitHub repository.
