# Bluetooth Configuration System

## Overview

I've created a comprehensive Bluetooth configuration system that allows you to configure your ESP32 Cat Feeder completely via Bluetooth, including motor type selection and all optional features. This eliminates the need for hardcoded settings and makes the device user-friendly.

## ✅ What You Requested

### 1. **Motor Type Selection via Bluetooth** ✅
- **Stepper Motor**: Precise step-based control
- **DC Motor (Time-based)**: Duration-controlled feeding  
- **DC Motor (Sensor-based)**: Stop-on-sensor feeding
- Command: `MOTOR:0|1|2`

### 2. **Optional Features Configuration via Bluetooth** ✅
- **Food Level Sensor**: Enable/disable load cell monitoring
- **LED Indicators**: Enable/disable status LEDs
- **Buzzer**: Enable/disable audio notifications
- **Manual Button**: Enable/disable physical feed button
- Command: `FEATURES:1,0,1,0` (sensor,led,buzzer,button)

### 3. **ESP-WROOM-32 Test Code** ✅
- Built-in LED status indicators
- Bluetooth configuration interface
- WiFi connection with web status page
- Complete device functionality test

### 4. **LED Status Patterns** ✅
- **Fast blink (200ms)**: Bluetooth ready for configuration
- **Slow blink (1000ms)**: WiFi connecting
- **Solid ON**: WiFi connected, web server running
- **Double blink**: Error state

## 🚀 Complete Test Project Created

### Project Structure
```
esp32-test-project/
├── CMakeLists.txt                    # ESP-IDF build configuration
├── main/
│   ├── CMakeLists.txt               # Main component build
│   └── main.c                       # Complete test application (668 lines)
├── bluetooth-config-tool.py         # Python configuration tool
├── build-and-flash.bat             # Windows build script
└── README.md                       # Comprehensive documentation
```

### Key Features Implemented

#### 🔧 Bluetooth Configuration Interface
- **Device Name**: "CatFeeder_ESP32"
- **Auto-discoverable**: Appears in Bluetooth device lists
- **Command-based**: Simple text commands for configuration
- **Persistent Storage**: Saves configuration to NVS flash

#### 📱 Configuration Commands
```bash
GET_STATUS                    # Get current device status
WIFI:MySSID,MyPassword       # Set WiFi credentials
MOTOR:0                      # Set motor type (0=stepper, 1=dc_time, 2=dc_sensor)
FEATURES:1,1,0,1            # Set features (food_sensor,led,buzzer,button)
SAVE_CONFIG                 # Save configuration and connect to WiFi
```

#### 🌐 Web Status Page
- **Real-time Status**: Device status, configuration, system info
- **Professional UI**: Responsive design with status indicators
- **LED Test**: Remote LED testing functionality
- **System Monitoring**: Memory usage, uptime, connection status

#### 💾 Persistent Configuration
- **NVS Storage**: Configuration saved to flash memory
- **Auto-load**: Restores settings on power-up
- **Factory Reset**: Can clear configuration if needed

## 🛠️ How to Use

### Step 1: Build and Flash Test Project

```bash
cd esp32-test-project
idf.py set-target esp32
idf.py build
idf.py -p COM_PORT flash monitor
```

Or use the Windows script:
```bash
build-and-flash.bat COM3
```

### Step 2: Configure via Bluetooth

#### Option A: Python Tool (Recommended)
```bash
pip install pybluez
python bluetooth-config-tool.py
```

#### Option B: Any Bluetooth Terminal App
1. Pair with "CatFeeder_ESP32"
2. Connect via Bluetooth terminal
3. Send configuration commands

### Step 3: Access Web Interface
1. Watch LED change to solid ON (WiFi connected)
2. Check serial monitor for IP address
3. Open browser to ESP32's IP address

## 📋 Configuration Examples

### Example 1: Stepper Motor with All Features
```
WIFI:MyNetwork,MyPassword
MOTOR:0
FEATURES:1,1,1,1
SAVE_CONFIG
```

### Example 2: Simple DC Motor Setup
```
WIFI:MyNetwork,MyPassword
MOTOR:1
FEATURES:0,1,0,1
SAVE_CONFIG
```

### Example 3: Minimal Configuration
```
WIFI:MyNetwork,MyPassword
MOTOR:0
FEATURES:0,0,0,0
SAVE_CONFIG
```

## 🎯 Benefits of This System

### 1. **User-Friendly Setup**
- No need to modify code for different configurations
- Visual feedback via LED patterns
- Simple Bluetooth commands

### 2. **Flexible Motor Support**
- Choose motor type after hardware assembly
- Easy to change motor configuration
- Supports all three motor types

### 3. **Optional Features**
- Enable only the features you have hardware for
- Saves memory and processing power
- Easy to add/remove features later

### 4. **Professional Interface**
- Web-based status monitoring
- Real-time device information
- Remote testing capabilities

### 5. **Robust Storage**
- Configuration survives power cycles
- Factory reset capability
- Version management for future updates

## 🔍 Technical Implementation

### Bluetooth Stack
- **Classic Bluetooth**: SPP (Serial Port Profile)
- **Auto-pairing**: Device appears as "CatFeeder_ESP32"
- **Command Parser**: Handles configuration commands
- **Response System**: Confirms successful operations

### WiFi Management
- **Station Mode**: Connects to existing network
- **Auto-reconnect**: Handles connection drops
- **Event-driven**: Responds to connection state changes

### Web Server
- **HTTP Server**: Lightweight ESP32 HTTP server
- **Responsive Design**: Works on mobile and desktop
- **Real-time Data**: Live system information
- **RESTful API**: Extensible for mobile app integration

### Storage System
- **NVS (Non-Volatile Storage)**: ESP32's flash storage
- **Structured Data**: Organized configuration structure
- **Error Handling**: Graceful handling of storage issues

## 🚀 Next Steps

### 1. **Test the System**
- Flash the test project to your ESP-WROOM-32
- Verify LED patterns work correctly
- Test Bluetooth configuration
- Confirm WiFi connection and web interface

### 2. **Plan Your Hardware**
- Based on testing, choose your motor type
- Select which optional features you want
- Order components according to hardware guide

### 3. **Integration**
- The configuration system is ready for the full Cat Feeder project
- All motor types and features are supported
- Web interface can be extended for full device control

## 📞 Support

The test project includes comprehensive logging and error handling. If you encounter issues:

1. **Check Serial Monitor**: Detailed logging shows what's happening
2. **LED Patterns**: Visual indication of system state
3. **Bluetooth Commands**: Use `GET_STATUS` to check current state
4. **Web Interface**: Shows complete system information

This Bluetooth configuration system makes your ESP32 Cat Feeder completely user-configurable without any code changes! 🎉
