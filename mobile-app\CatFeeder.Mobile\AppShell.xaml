<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="CatFeeder.Mobile.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:CatFeeder.Mobile"
    xmlns:views="clr-namespace:CatFeeder.Mobile.Views"
    Shell.FlyoutBehavior="Disabled">

    <TabBar>
        <Tab Title="Home" Icon="home_icon.png">
            <ShellContent ContentTemplate="{DataTemplate views:MainPage}" />
        </Tab>
        <!-- History tab temporarily disabled - HistoryPage not implemented yet -->
        <!--<Tab Title="History" Icon="history_icon.png">
            <ShellContent ContentTemplate="{DataTemplate views:HistoryPage}" />
        </Tab>-->
        <Tab Title="Settings" Icon="settings_icon.png">
            <ShellContent ContentTemplate="{DataTemplate views:SettingsPage}" />
        </Tab>
    </TabBar>
</Shell>
