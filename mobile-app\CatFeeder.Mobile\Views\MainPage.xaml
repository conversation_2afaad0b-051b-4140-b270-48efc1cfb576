<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:views="clr-namespace:CatFeeder.Mobile.Views"
             xmlns:models="clr-namespace:CatFeeder.Models;assembly=CatFeeder.Models"
             x:Class="CatFeeder.Mobile.Views.MainPage"
             x:DataType="views:MainPage"
             Title="Cat Feeder">
    <ScrollView>
        <VerticalStackLayout Spacing="25" Padding="30">
            <!-- Status Card -->
            <Frame BorderColor="LightGray" CornerRadius="10" Padding="15">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Device Status" FontSize="20" FontAttributes="Bold"/>
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto,Auto" ColumnSpacing="15" RowSpacing="10">
                        <Label Grid.Row="0" Grid.Column="0" Text="Connection:" />
                        <Label Grid.Row="0" Grid.Column="1" Text="{Binding ConnectionStatus}" TextColor="{Binding ConnectionStatusColor}" />
                        
                        <Label Grid.Row="1" Grid.Column="0" Text="Last Feeding:" />
                        <Label Grid.Row="1" Grid.Column="1" Text="{Binding LastFeedingTime}" />
                        
                        <Label Grid.Row="2" Grid.Column="0" Text="Feedings Today:" />
                        <Label Grid.Row="2" Grid.Column="1" Text="{Binding FeedingsToday}" />
                        
                        <Label Grid.Row="3" Grid.Column="0" Text="Food Level:" />
                        <ProgressBar Grid.Row="3" Grid.Column="1" Progress="{Binding FoodLevel}" />
                    </Grid>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Recent History Preview -->
            <Frame BorderColor="LightGray" CornerRadius="10" Padding="15">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Recent Feedings" FontSize="20" FontAttributes="Bold"/>
                    <CollectionView ItemsSource="{Binding RecentFeedings}" HeightRequest="150">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:FeedingEntry">
                                <Grid Padding="5" ColumnDefinitions="*,Auto">
                                    <Label Grid.Column="0" Text="{Binding TimeString}" />
                                    <Label Grid.Column="1" Text="{Binding PortionSizeString}" />
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Manual Feed Button -->
            <Button Text="Manual Feed" 
                    BackgroundColor="#4CAF50"
                    TextColor="White"
                    FontSize="18"
                    HeightRequest="60"
                    CornerRadius="30"
                    Clicked="OnManualFeedClicked"
                    IsEnabled="{Binding IsConnected}" />
                    
            <!-- Activity Log -->
            <Frame BorderColor="LightGray" CornerRadius="10" Padding="15">
                <VerticalStackLayout>
                    <Label Text="Activity Log" FontSize="16" FontAttributes="Bold"/>
                    <ScrollView HeightRequest="100">
                        <Label Text="{Binding ActivityLog}" FontSize="12" />
                    </ScrollView>
                </VerticalStackLayout>
            </Frame>

            <!-- Version Information -->
            <Frame BorderColor="LightGray" CornerRadius="10" Padding="15">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Version Information" FontSize="16" FontAttributes="Bold"/>
                    <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,Auto,Auto" ColumnSpacing="15" RowSpacing="5">
                        <Label Grid.Row="0" Grid.Column="0" Text="Mobile App:" FontSize="12"/>
                        <Label Grid.Row="0" Grid.Column="1" Text="{Binding MobileAppVersion}" FontSize="12"/>

                        <Label Grid.Row="1" Grid.Column="0" Text="ESP32 Firmware:" FontSize="12"/>
                        <Label Grid.Row="1" Grid.Column="1" Text="{Binding ESP32Version}" FontSize="12"/>

                        <Label Grid.Row="2" Grid.Column="0" Text="Version Status:" FontSize="12"/>
                        <Label Grid.Row="2" Grid.Column="1" Text="{Binding VersionStatus}" TextColor="{Binding VersionStatusColor}" FontSize="12"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>