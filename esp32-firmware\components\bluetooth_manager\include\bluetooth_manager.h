/**
 * Bluetooth Manager Component
 * 
 * Manages Bluetooth connectivity, authentication, and complete device configuration
 */

#pragma once

#include "esp_err.h"
#include "esp_bt.h"
#include "esp_gap_bt_api.h"
#include "esp_spp_api.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

#define BT_DEVICE_NAME_MAX_LEN 32
#define BT_PIN_CODE_LEN 4
#define BT_MAX_CONNECTED_DEVICES 3
#define BT_COMMAND_MAX_LEN 512
#define BT_RESPONSE_MAX_LEN 1024

/**
 * Bluetooth connection status
 */
typedef enum {
    BT_STATUS_DISABLED,
    BT_STATUS_INITIALIZING,
    BT_STATUS_DISCOVERABLE,
    BT_STATUS_CONNECTED,
    BT_STATUS_ERROR
} bt_status_t;

/**
 * Bluetooth device info
 */
typedef struct {
    esp_bd_addr_t address;
    char name[BT_DEVICE_NAME_MAX_LEN];
    bool is_authenticated;
    char username[32];
    uint32_t connection_time;
    uint32_t last_activity;
} bt_device_info_t;

/**
 * Bluetooth configuration
 */
typedef struct {
    char device_name[BT_DEVICE_NAME_MAX_LEN];
    char pin_code[BT_PIN_CODE_LEN + 1];
    bool auto_accept_connections;
    bool require_authentication;
    uint32_t discoverable_timeout;
    bool enable_encryption;
} bt_config_t;

/**
 * Command handler function type
 */
typedef esp_err_t (*bt_command_handler_t)(const char* command, const char* params, 
                                         char* response, size_t response_size,
                                         const char* username);

/**
 * Connection event callback type
 */
typedef void (*bt_connection_callback_t)(esp_bd_addr_t remote_addr, bool connected);

/**
 * Initialize Bluetooth manager
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_init(void);

/**
 * Deinitialize Bluetooth manager
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_deinit(void);

/**
 * Start Bluetooth service
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_start(void);

/**
 * Stop Bluetooth service
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_stop(void);

/**
 * Get Bluetooth status
 * 
 * @return Current Bluetooth status
 */
bt_status_t bluetooth_manager_get_status(void);

/**
 * Set Bluetooth configuration
 * 
 * @param config Bluetooth configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_set_config(const bt_config_t* config);

/**
 * Get Bluetooth configuration
 * 
 * @param config Pointer to store configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_get_config(bt_config_t* config);

/**
 * Register command handler
 * 
 * @param command Command name
 * @param handler Handler function
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_register_command(const char* command, bt_command_handler_t handler);

/**
 * Send response to connected device
 * 
 * @param device_addr Device address
 * @param response Response string
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_send_response(esp_bd_addr_t device_addr, const char* response);

/**
 * Broadcast message to all connected devices
 * 
 * @param message Message to broadcast
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_broadcast(const char* message);

/**
 * Get connected devices
 * 
 * @param devices Array to store device info
 * @param max_devices Maximum number of devices
 * @return Number of connected devices
 */
int bluetooth_manager_get_connected_devices(bt_device_info_t* devices, int max_devices);

/**
 * Disconnect device
 * 
 * @param device_addr Device address to disconnect
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_disconnect_device(esp_bd_addr_t device_addr);

/**
 * Set connection callback
 * 
 * @param callback Callback function
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_set_connection_callback(bt_connection_callback_t callback);

/**
 * Check if device is authenticated
 * 
 * @param device_addr Device address
 * @return true if authenticated
 */
bool bluetooth_manager_is_device_authenticated(esp_bd_addr_t device_addr);

/**
 * Authenticate device
 * 
 * @param device_addr Device address
 * @param username Username
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_authenticate_device(esp_bd_addr_t device_addr, const char* username);

/**
 * Get device MAC address as string
 * 
 * @param addr_str Buffer to store address string (minimum 18 bytes)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_get_local_address(char* addr_str);

/**
 * Set discoverable mode
 * 
 * @param discoverable True to enable discoverable mode
 * @param timeout Timeout in seconds (0 = indefinite)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_set_discoverable(bool discoverable, uint32_t timeout);

/**
 * Get signal strength for connected device
 * 
 * @param device_addr Device address
 * @param rssi Pointer to store RSSI value
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bluetooth_manager_get_rssi(esp_bd_addr_t device_addr, int8_t* rssi);

#ifdef __cplusplus
}
#endif
