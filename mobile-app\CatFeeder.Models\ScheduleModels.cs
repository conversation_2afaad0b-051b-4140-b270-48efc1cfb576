using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace CatFeeder.Models
{
    /// <summary>
    /// Days of the week flags
    /// </summary>
    [Flags]
    public enum DaysOfWeek
    {
        None = 0,
        Sunday = 1,
        Monday = 2,
        Tuesday = 4,
        Wednesday = 8,
        Thursday = 16,
        Friday = 32,
        Saturday = 64,
        Weekdays = Monday | Tuesday | Wednesday | Thursday | Friday,
        Weekends = Saturday | Sunday,
        All = Sunday | Monday | Tuesday | Wednesday | Thursday | Friday | Saturday
    }

    /// <summary>
    /// Feeding schedule entry
    /// </summary>
    public class FeedingScheduleEntry
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        
        [Range(0, 23)]
        public int Hour { get; set; }
        
        [Range(0, 59)]
        public int Minute { get; set; }
        
        [Range(1, 50000)]
        public uint PortionSize { get; set; }
        
        public bool Enabled { get; set; } = true;
        public bool RepeatDaily { get; set; } = true;
        public DaysOfWeek DaysOfWeek { get; set; } = DaysOfWeek.All;
        
        [StringLength(100)]
        public string Name { get; set; } = "";
        
        [StringLength(500)]
        public string Notes { get; set; } = "";
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public DateTime? LastTriggered { get; set; }

        /// <summary>
        /// Get the time as TimeSpan
        /// </summary>
        public TimeSpan Time => new TimeSpan(Hour, Minute, 0);

        /// <summary>
        /// Get formatted time string
        /// </summary>
        public string TimeString => $"{Hour:D2}:{Minute:D2}";

        /// <summary>
        /// Check if this entry should trigger on the given day
        /// </summary>
        public bool ShouldTriggerOnDay(DayOfWeek dayOfWeek)
        {
            if (!Enabled) return false;
            
            var dayFlag = dayOfWeek switch
            {
                DayOfWeek.Sunday => DaysOfWeek.Sunday,
                DayOfWeek.Monday => DaysOfWeek.Monday,
                DayOfWeek.Tuesday => DaysOfWeek.Tuesday,
                DayOfWeek.Wednesday => DaysOfWeek.Wednesday,
                DayOfWeek.Thursday => DaysOfWeek.Thursday,
                DayOfWeek.Friday => DaysOfWeek.Friday,
                DayOfWeek.Saturday => DaysOfWeek.Saturday,
                _ => DaysOfWeek.None
            };
            
            return DaysOfWeek.HasFlag(dayFlag);
        }

        /// <summary>
        /// Get next scheduled time
        /// </summary>
        public DateTime? GetNextScheduledTime(DateTime fromTime)
        {
            if (!Enabled) return null;

            var today = fromTime.Date;
            var scheduledTimeToday = today.Add(Time);

            // Check if we can schedule for today
            if (scheduledTimeToday > fromTime && ShouldTriggerOnDay(today.DayOfWeek))
            {
                return scheduledTimeToday;
            }

            // Look for next valid day within the next week
            for (int i = 1; i <= 7; i++)
            {
                var nextDay = today.AddDays(i);
                if (ShouldTriggerOnDay(nextDay.DayOfWeek))
                {
                    return nextDay.Add(Time);
                }
            }

            return null; // No valid schedule found
        }

        /// <summary>
        /// Get days of week as string
        /// </summary>
        public string DaysOfWeekString
        {
            get
            {
                if (DaysOfWeek == DaysOfWeek.All)
                    return "Every day";
                if (DaysOfWeek == DaysOfWeek.Weekdays)
                    return "Weekdays";
                if (DaysOfWeek == DaysOfWeek.Weekends)
                    return "Weekends";

                var days = new List<string>();
                if (DaysOfWeek.HasFlag(DaysOfWeek.Sunday)) days.Add("Sun");
                if (DaysOfWeek.HasFlag(DaysOfWeek.Monday)) days.Add("Mon");
                if (DaysOfWeek.HasFlag(DaysOfWeek.Tuesday)) days.Add("Tue");
                if (DaysOfWeek.HasFlag(DaysOfWeek.Wednesday)) days.Add("Wed");
                if (DaysOfWeek.HasFlag(DaysOfWeek.Thursday)) days.Add("Thu");
                if (DaysOfWeek.HasFlag(DaysOfWeek.Friday)) days.Add("Fri");
                if (DaysOfWeek.HasFlag(DaysOfWeek.Saturday)) days.Add("Sat");

                return string.Join(", ", days);
            }
        }
    }

    /// <summary>
    /// Complete feeding schedule
    /// </summary>
    public class FeedingSchedule
    {
        public List<FeedingScheduleEntry> Entries { get; set; } = new();
        public bool SchedulerEnabled { get; set; } = true;
        public DateTime LastModified { get; set; } = DateTime.Now;
        public uint MaxEntriesAllowed { get; set; } = 8;

        /// <summary>
        /// Get all enabled entries
        /// </summary>
        public IEnumerable<FeedingScheduleEntry> EnabledEntries => 
            Entries.Where(e => e.Enabled);

        /// <summary>
        /// Get entries sorted by time
        /// </summary>
        public IEnumerable<FeedingScheduleEntry> EntriesByTime => 
            Entries.OrderBy(e => e.Time);

        /// <summary>
        /// Get next scheduled feeding
        /// </summary>
        public FeedingScheduleEntry? GetNextScheduledFeeding(DateTime fromTime)
        {
            if (!SchedulerEnabled) return null;

            return EnabledEntries
                .Select(entry => new { Entry = entry, NextTime = entry.GetNextScheduledTime(fromTime) })
                .Where(x => x.NextTime.HasValue)
                .OrderBy(x => x.NextTime)
                .FirstOrDefault()?.Entry;
        }

        /// <summary>
        /// Get next scheduled time
        /// </summary>
        public DateTime? GetNextScheduledTime(DateTime fromTime)
        {
            return GetNextScheduledFeeding(fromTime)?.GetNextScheduledTime(fromTime);
        }

        /// <summary>
        /// Add new schedule entry
        /// </summary>
        public bool AddEntry(FeedingScheduleEntry entry)
        {
            if (Entries.Count >= MaxEntriesAllowed)
                return false;

            Entries.Add(entry);
            LastModified = DateTime.Now;
            return true;
        }

        /// <summary>
        /// Remove schedule entry
        /// </summary>
        public bool RemoveEntry(Guid entryId)
        {
            var entry = Entries.FirstOrDefault(e => e.Id == entryId);
            if (entry != null)
            {
                Entries.Remove(entry);
                LastModified = DateTime.Now;
                return true;
            }
            return false;
        }

        /// <summary>
        /// Update schedule entry
        /// </summary>
        public bool UpdateEntry(FeedingScheduleEntry updatedEntry)
        {
            var index = Entries.FindIndex(e => e.Id == updatedEntry.Id);
            if (index >= 0)
            {
                Entries[index] = updatedEntry;
                LastModified = DateTime.Now;
                return true;
            }
            return false;
        }

        /// <summary>
        /// Get entries for today
        /// </summary>
        public IEnumerable<FeedingScheduleEntry> GetTodaysEntries(DateTime date)
        {
            return EnabledEntries.Where(e => e.ShouldTriggerOnDay(date.DayOfWeek));
        }

        /// <summary>
        /// Validate schedule
        /// </summary>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (Entries.Count > MaxEntriesAllowed)
                errors.Add($"Too many schedule entries. Maximum allowed: {MaxEntriesAllowed}");

            // Check for duplicate times on same days
            var duplicates = Entries
                .Where(e => e.Enabled)
                .GroupBy(e => new { e.Hour, e.Minute, e.DaysOfWeek })
                .Where(g => g.Count() > 1)
                .ToList();

            if (duplicates.Any())
                errors.Add("Duplicate feeding times found for the same days");

            // Validate individual entries
            foreach (var entry in Entries)
            {
                if (entry.Hour < 0 || entry.Hour > 23)
                    errors.Add($"Invalid hour in schedule entry: {entry.Hour}");
                
                if (entry.Minute < 0 || entry.Minute > 59)
                    errors.Add($"Invalid minute in schedule entry: {entry.Minute}");
                
                if (entry.PortionSize == 0)
                    errors.Add("Portion size cannot be zero");
                
                if (entry.DaysOfWeek == DaysOfWeek.None)
                    errors.Add("At least one day must be selected for each schedule entry");
            }

            return errors;
        }
    }
}
