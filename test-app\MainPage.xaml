﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="TestApp.MainPage"
             Title="Studio 311 LP Test"
             BackgroundColor="LightGreen">

    <StackLayout Padding="20" Spacing="20" VerticalOptions="CenterAndExpand">

        <Label Text="🧪"
               FontSize="60"
               HorizontalOptions="Center" />

        <Label Text="Studio 311 LP"
               FontSize="24"
               FontAttributes="Bold"
               TextColor="DarkGreen"
               HorizontalOptions="Center" />

        <Label Text="Ultra-Simple Test App"
               FontSize="18"
               TextColor="DarkGreen"
               HorizontalOptions="Center" />

        <Label x:Name="StatusLabel"
               Text="App started successfully!"
               FontSize="14"
               TextColor="Green"
               HorizontalOptions="Center"
               HorizontalTextAlignment="Center" />

        <Button x:Name="TestButton"
                Text="Test Button"
                BackgroundColor="DarkGreen"
                TextColor="White"
                Clicked="OnTestButtonClicked" />

        <Label x:Name="ResultLabel"
               Text="Click the button to test"
               FontSize="12"
               TextColor="DarkGray"
               HorizontalOptions="Center"
               HorizontalTextAlignment="Center" />

    </StackLayout>

</ContentPage>
