# Build directories
build/
**/build/
**/bin/
**/obj/
**/Debug/
**/Release/
**/x64/
**/x86/
**/ARM/
**/ARM64/
**/AnyCPU/

# ESP-IDF specific
esp32-firmware/build/
esp32-firmware/sdkconfig
esp32-firmware/sdkconfig.old
esp32-firmware/dependencies.lock
esp32-firmware/managed_components/
esp32-firmware/**/sdkconfig.backup
esp32-firmware/.config
esp32-firmware/bootloader/
esp32-firmware/partition_table/

# Compiled files
*.o
*.obj
*.elf
*.bin
*.map
*.hex
*.dis
*.a
*.lib
*.so
*.dll
*.exe

# Generated files
*.d
*.pyc
__pycache__/
.cache/
.pytest_cache/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files (moved to OS generated files section)

# Log files
*.log
**/logs/
**/log/

# Script generated files
scripts/temp/
scripts/*.tmp

# Version management (keep version.json but ignore generated files)
esp32-firmware/main/version.h
mobile-app/**/AssemblyInfo.cs
mobile-app/**/GlobalAssemblyInfo.cs

# Environment and configuration files
.env
.env.local
.env.*.local
config.local.json

# Backup files
*.bak
*.backup
*.orig

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Mobile app build artifacts (.NET/MAUI)
mobile-app/**/bin/
mobile-app/**/obj/
mobile-app/**/.vs/
mobile-app/**/packages/
mobile-app/**/*.user
mobile-app/**/*.suo
mobile-app/**/*.userprefs
mobile-app/**/TestResults/
mobile-app/**/AppPackages/
mobile-app/**/BundleArtifacts/
mobile-app/**/Package.StoreAssociation.xml
mobile-app/**/*.apk
mobile-app/**/*.aab
mobile-app/**/*.ipa
mobile-app/**/*.app
mobile-app/**/*.dSYM/

# NuGet packages
mobile-app/**/packages/
mobile-app/**/packages.config
mobile-app/**/.nuget/
mobile-app/**/project.lock.json
mobile-app/**/project.fragment.lock.json
mobile-app/**/artifacts/

# Visual Studio cache/options directory
mobile-app/**/.vs/
mobile-app/**/launchSettings.json

# MSBuild Binary and Structured Log
mobile-app/**/*.binlog

# Android specific
mobile-app/**/platforms/android/
mobile-app/**/android/
mobile-app/**/*.keystore
mobile-app/**/*.jks

# iOS specific
mobile-app/**/platforms/ios/
mobile-app/**/ios/
mobile-app/**/*.mobileprovision
mobile-app/**/*.p12