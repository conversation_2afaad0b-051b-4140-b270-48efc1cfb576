@echo off
echo ========================================
echo ESP32 Cat Feeder - Windows Setup Script
echo ========================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo [1/6] Checking prerequisites...

:: Check if chocolatey is installed
choco --version >nul 2>&1
if %errorLevel% neq 0 (
    echo Installing Chocolatey package manager...
    powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    if %errorLevel% neq 0 (
        echo ERROR: Failed to install Chocolatey
        pause
        exit /b 1
    )
    echo Chocolatey installed successfully
) else (
    echo Chocolatey already installed
)

echo.
echo [2/6] Installing Git (if not present)...
git --version >nul 2>&1
if %errorLevel% neq 0 (
    choco install git -y
    if %errorLevel% neq 0 (
        echo ERROR: Failed to install Git
        pause
        exit /b 1
    )
    echo Git installed successfully
) else (
    echo Git already installed
)

echo.
echo [3/6] Installing .NET 8 SDK...
dotnet --version >nul 2>&1
if %errorLevel% neq 0 (
    echo Downloading .NET 8 SDK...
    powershell -Command "Invoke-WebRequest -Uri 'https://download.visualstudio.microsoft.com/download/pr/93961dfb-d1e0-49c8-9230-abcba1ebab5a/811ed1eb63d7652325727720edda26a8/dotnet-sdk-8.0.404-win-x64.exe' -OutFile 'dotnet-sdk-installer.exe'"
    echo Installing .NET 8 SDK...
    dotnet-sdk-installer.exe /quiet
    if %errorLevel% neq 0 (
        echo ERROR: Failed to install .NET 8 SDK
        pause
        exit /b 1
    )
    del dotnet-sdk-installer.exe
    echo .NET 8 SDK installed successfully
) else (
    echo .NET 8 SDK already installed
)

echo.
echo [4/6] Installing MAUI workload...
dotnet workload install maui
if %errorLevel% neq 0 (
    echo ERROR: Failed to install MAUI workload
    pause
    exit /b 1
)
echo MAUI workload installed successfully

echo.
echo [5/6] Installing Visual Studio Code...
code --version >nul 2>&1
if %errorLevel% neq 0 (
    choco install vscode -y
    if %errorLevel% neq 0 (
        echo ERROR: Failed to install Visual Studio Code
        pause
        exit /b 1
    )
    echo Visual Studio Code installed successfully
) else (
    echo Visual Studio Code already installed
)

echo.
echo [6/6] Installing ESP-IDF...
if not exist "C:\Espressif" (
    echo Downloading ESP-IDF Tools Installer...
    powershell -Command "Invoke-WebRequest -Uri 'https://dl.espressif.com/dl/esp-idf-tools-setup-online-2.3.5.exe' -OutFile 'esp-idf-installer.exe'"
    echo.
    echo IMPORTANT: ESP-IDF installer will open now.
    echo Please follow these settings:
    echo - Select ESP-IDF v5.4.1
    echo - Install to C:\Espressif
    echo - Install all components
    echo - Check "Run ESP-IDF Command Prompt" at the end
    echo.
    pause
    esp-idf-installer.exe
    del esp-idf-installer.exe
) else (
    echo ESP-IDF appears to be already installed at C:\Espressif
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Install VS Code extensions:
echo    - ESP-IDF extension
echo    - C# extension
echo    - .NET MAUI extension
echo.
echo 2. Test ESP-IDF installation:
echo    - Open ESP-IDF Command Prompt
echo    - Run: idf.py --version
echo.
echo 3. Test MAUI installation:
echo    - Open Command Prompt
echo    - Run: dotnet workload list
echo.
echo 4. Open the project in VS Code:
echo    - code "%~dp0"
echo.
echo 5. Read the documentation:
echo    - README.md
echo    - setup-development-environment.md
echo    - docs/hardware-setup-guide.md
echo.
echo Happy coding!
pause
