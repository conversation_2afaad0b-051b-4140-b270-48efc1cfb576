/**
 * Feeding Controller
 * 
 * High-level feeding control and management
 */

#pragma once

#include "esp_err.h"
#include "app_config.h"
#include "feeding_history.h"

#ifdef __cplusplus
extern "C" {
#endif

// feeding_result_t is now defined in feeding_history.h

/**
 * Feeding controller status
 */
typedef struct {
    bool motor_ready;               ///< Motor is ready for operation
    uint32_t feedings_today;        ///< Number of feedings today
    int64_t last_feeding_time;      ///< Last feeding timestamp (Unix time)
    float food_level_percent;       ///< Food level percentage (0-100, -1 if no sensor)
    feeding_result_t last_result;   ///< Result of last feeding attempt
    bool manual_button_pressed;    ///< Manual button state
} feeding_controller_status_t;

/**
 * Initialize feeding controller
 * 
 * @param config Application configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_controller_init(const app_config_t *config);

/**
 * Deinitialize feeding controller
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_controller_deinit(void);

/**
 * Execute manual feeding
 * 
 * @param portion_size Portion size (steps for stepper, time for DC motor)
 * @return Feeding result code
 */
feeding_result_t feeding_controller_feed_manual(uint32_t portion_size);

/**
 * Execute scheduled feeding
 * 
 * @return Feeding result code
 */
feeding_result_t feeding_controller_feed_scheduled(void);

/**
 * Get feeding controller status
 * 
 * @param status Pointer to status structure
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_controller_get_status(feeding_controller_status_t *status);

/**
 * Check if feeding is allowed (safety checks)
 * 
 * @param portion_size Requested portion size
 * @return true if feeding is allowed, false otherwise
 */
bool feeding_controller_is_feeding_allowed(uint32_t portion_size);

/**
 * Get food level percentage
 * 
 * @return Food level percentage (0-100), -1 if sensor not available
 */
float feeding_controller_get_food_level(void);

/**
 * Calibrate food level sensor
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_controller_calibrate_food_sensor(void);

/**
 * Reset daily feeding counter
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_controller_reset_daily_counter(void);

/**
 * Update feeding configuration
 * 
 * @param config New feeding configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_controller_update_config(const feeding_config_t *config);

#ifdef __cplusplus
}
#endif
