#!/usr/bin/env python3
"""
ESP32 Cat Feeder Bluetooth Configuration Tool

This tool helps you configure your ESP32 Cat Feeder via Bluetooth.
It provides a simple command-line interface to set up:
- WiFi credentials
- Motor type selection
- Optional features

Requirements:
- Python 3.6+
- pybluez: pip install pybluez
- On Windows: Also install Microsoft Visual C++ Build Tools

Usage:
python bluetooth-config-tool.py
"""

import sys
import time

try:
    import bluetooth
except ImportError:
    print("Error: pybluez not installed")
    print("Install with: pip install pybluez")
    print("On Windows, you may also need Microsoft Visual C++ Build Tools")
    sys.exit(1)

class ESP32ConfigTool:
    def __init__(self):
        self.socket = None
        self.device_address = None
        
    def scan_for_devices(self):
        """Scan for ESP32 Cat Feeder devices"""
        print("Scanning for ESP32 Cat Feeder devices...")
        print("Make sure your ESP32 is powered on and Bluetooth is enabled.")
        
        devices = bluetooth.discover_devices(duration=10, lookup_names=True)
        
        esp32_devices = []
        for addr, name in devices:
            if "CatFeeder" in name or "ESP32" in name:
                esp32_devices.append((addr, name))
                
        if not esp32_devices:
            print("No ESP32 Cat Feeder devices found.")
            print("Make sure your device is:")
            print("- Powered on")
            print("- Bluetooth enabled")
            print("- LED blinking fast (Bluetooth ready)")
            return None
            
        if len(esp32_devices) == 1:
            return esp32_devices[0]
            
        # Multiple devices found, let user choose
        print(f"\nFound {len(esp32_devices)} devices:")
        for i, (addr, name) in enumerate(esp32_devices):
            print(f"{i+1}. {name} ({addr})")
            
        while True:
            try:
                choice = int(input("Select device (1-{}): ".format(len(esp32_devices))))
                if 1 <= choice <= len(esp32_devices):
                    return esp32_devices[choice-1]
                else:
                    print("Invalid choice. Please try again.")
            except ValueError:
                print("Please enter a number.")
                
    def connect(self, address):
        """Connect to ESP32 device"""
        try:
            print(f"Connecting to {address}...")
            self.socket = bluetooth.BluetoothSocket(bluetooth.RFCOMM)
            self.socket.connect((address, 1))  # SPP usually uses channel 1
            print("Connected successfully!")
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
            
    def send_command(self, command):
        """Send command to ESP32 and get response"""
        try:
            self.socket.send(command + "\n")
            time.sleep(0.5)  # Give device time to respond
            response = self.socket.recv(1024).decode('utf-8').strip()
            return response
        except Exception as e:
            print(f"Communication error: {e}")
            return None
            
    def get_status(self):
        """Get current device status"""
        print("\nGetting device status...")
        response = self.send_command("GET_STATUS")
        if response:
            print(f"Status: {response}")
        return response
        
    def configure_wifi(self):
        """Configure WiFi settings"""
        print("\n=== WiFi Configuration ===")
        ssid = input("Enter WiFi SSID: ").strip()
        if not ssid:
            print("SSID cannot be empty")
            return False
            
        password = input("Enter WiFi Password (leave empty for open network): ").strip()
        
        command = f"WIFI:{ssid},{password}"
        response = self.send_command(command)
        
        if response and "WIFI_OK" in response:
            print("✓ WiFi configuration sent successfully")
            return True
        else:
            print("✗ Failed to configure WiFi")
            return False
            
    def configure_motor(self):
        """Configure motor type"""
        print("\n=== Motor Configuration ===")
        print("Select motor type:")
        print("0. Stepper Motor (precise, recommended)")
        print("1. DC Motor with Time Control (simple)")
        print("2. DC Motor with Sensor Control (adaptive)")
        
        while True:
            try:
                choice = int(input("Enter choice (0-2): "))
                if 0 <= choice <= 2:
                    break
                else:
                    print("Please enter 0, 1, or 2")
            except ValueError:
                print("Please enter a number")
                
        command = f"MOTOR:{choice}"
        response = self.send_command(command)
        
        if response and "MOTOR_OK" in response:
            motor_types = ["Stepper Motor", "DC Motor (Time)", "DC Motor (Sensor)"]
            print(f"✓ Motor type set to: {motor_types[choice]}")
            return True
        else:
            print("✗ Failed to configure motor")
            return False
            
    def configure_features(self):
        """Configure optional features"""
        print("\n=== Optional Features Configuration ===")
        
        features = []
        
        # Food level sensor
        choice = input("Enable food level sensor? (y/n): ").lower().strip()
        features.append("1" if choice == "y" else "0")
        
        # LED indicators
        choice = input("Enable LED status indicators? (y/n): ").lower().strip()
        features.append("1" if choice == "y" else "0")
        
        # Buzzer
        choice = input("Enable buzzer notifications? (y/n): ").lower().strip()
        features.append("1" if choice == "y" else "0")
        
        # Manual button
        choice = input("Enable manual feed button? (y/n): ").lower().strip()
        features.append("1" if choice == "y" else "0")
        
        command = f"FEATURES:{','.join(features)}"
        response = self.send_command(command)
        
        if response and "FEATURES_OK" in response:
            print("✓ Features configured successfully")
            return True
        else:
            print("✗ Failed to configure features")
            return False
            
    def save_configuration(self):
        """Save configuration to device"""
        print("\nSaving configuration...")
        response = self.send_command("SAVE_CONFIG")
        
        if response and "CONFIG_OK" in response:
            print("✓ Configuration saved successfully!")
            print("The device will now connect to WiFi.")
            print("Watch the LED - it should change to slow blink (connecting) then solid (connected).")
            return True
        else:
            print("✗ Failed to save configuration")
            return False
            
    def disconnect(self):
        """Disconnect from device"""
        if self.socket:
            self.socket.close()
            print("Disconnected from device")
            
    def run_configuration(self):
        """Run the complete configuration process"""
        print("ESP32 Cat Feeder Configuration Tool")
        print("=" * 40)
        
        # Scan for devices
        device = self.scan_for_devices()
        if not device:
            return False
            
        address, name = device
        print(f"\nFound device: {name} ({address})")
        
        # Connect
        if not self.connect(address):
            return False
            
        try:
            # Get current status
            self.get_status()
            
            # Configuration menu
            while True:
                print("\n=== Configuration Menu ===")
                print("1. Configure WiFi")
                print("2. Configure Motor Type")
                print("3. Configure Optional Features")
                print("4. Get Status")
                print("5. Save Configuration & Exit")
                print("6. Exit without saving")
                
                choice = input("Enter choice (1-6): ").strip()
                
                if choice == "1":
                    self.configure_wifi()
                elif choice == "2":
                    self.configure_motor()
                elif choice == "3":
                    self.configure_features()
                elif choice == "4":
                    self.get_status()
                elif choice == "5":
                    if self.save_configuration():
                        print("\nConfiguration complete!")
                        print("You can now access the web interface at the ESP32's IP address.")
                        break
                elif choice == "6":
                    print("Exiting without saving...")
                    break
                else:
                    print("Invalid choice. Please try again.")
                    
        finally:
            self.disconnect()
            
        return True

def main():
    """Main function"""
    tool = ESP32ConfigTool()
    
    try:
        tool.run_configuration()
    except KeyboardInterrupt:
        print("\nConfiguration cancelled by user")
        tool.disconnect()
    except Exception as e:
        print(f"Unexpected error: {e}")
        tool.disconnect()

if __name__ == "__main__":
    main()
