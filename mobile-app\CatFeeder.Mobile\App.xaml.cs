﻿using CatFeeder.Core.Services;
using CatFeeder.Mobile.Views;

namespace CatFeeder.Mobile;

public partial class App : Application
{
    private ILoggingService? _loggingService;

    public App()
    {
        try
        {
            InitializeComponent();

            // Set up global exception handling
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
        }
        catch (Exception ex)
        {
            // Critical failure during app initialization
            System.Diagnostics.Debug.WriteLine($"CRITICAL: App initialization failed: {ex}");
        }
    }

    protected override Window CreateWindow(IActivationState? activationState)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("CreateWindow: Starting");

            // Create a simple startup page without any service dependencies
            var startupPage = new ContentPage
            {
                Title = "Studio 311 LP - Smart Cat Feeder",
                BackgroundColor = Colors.White,
                Content = new StackLayout
                {
                    VerticalOptions = LayoutOptions.Center,
                    HorizontalOptions = LayoutOptions.Center,
                    Children =
                    {
                        new Label
                        {
                            Text = "Studio 311 LP",
                            FontSize = 24,
                            FontAttributes = FontAttributes.Bold,
                            HorizontalOptions = LayoutOptions.Center,
                            TextColor = Colors.Black
                        },
                        new Label
                        {
                            Text = "Smart Cat Feeder",
                            FontSize = 18,
                            HorizontalOptions = LayoutOptions.Center,
                            TextColor = Colors.Gray,
                            Margin = new Thickness(0, 0, 0, 20)
                        },
                        new Label
                        {
                            Text = "Initializing...",
                            FontSize = 16,
                            HorizontalOptions = LayoutOptions.Center,
                            TextColor = Colors.Blue
                        }
                    }
                }
            };

            System.Diagnostics.Debug.WriteLine("CreateWindow: Startup page created successfully");

            // Initialize services after window is created
            _ = Task.Run(async () =>
            {
                await Task.Delay(1000); // Give the UI time to render
                try
                {
                    _loggingService = Handler?.MauiContext?.Services?.GetService<ILoggingService>();
                    _loggingService?.LogInfo("Application started successfully");
                    System.Diagnostics.Debug.WriteLine("Services initialized successfully");

                    // Navigate to main app shell after services are ready
                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        try
                        {
                            MainPage = new AppShell();
                            System.Diagnostics.Debug.WriteLine("Navigated to AppShell");
                        }
                        catch (Exception navEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"Navigation to AppShell failed: {navEx}");
                        }
                    });
                }
                catch (Exception serviceEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Service initialization failed: {serviceEx}");
                }
            });

            return new Window(startupPage);
        }
        catch (Exception ex)
        {
            // Log the error
            System.Diagnostics.Debug.WriteLine($"CreateWindow: CRITICAL FAILURE: {ex}");

            // Last resort - create the most basic page possible
            return new Window(new ContentPage
            {
                Title = "Critical Error",
                BackgroundColor = Colors.Red,
                Content = new Label
                {
                    Text = "CRITICAL ERROR\nStudio 311 LP\nSmart Cat Feeder\n\nApp failed to start.\nPlease contact support.",
                    HorizontalOptions = LayoutOptions.Center,
                    VerticalOptions = LayoutOptions.Center,
                    TextColor = Colors.White,
                    FontSize = 16,
                    HorizontalTextAlignment = TextAlignment.Center
                }
            });
        }
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            var exception = e.ExceptionObject as Exception;
            System.Diagnostics.Debug.WriteLine($"UNHANDLED EXCEPTION: {exception}");

            // Switch to crash safe mode
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    MainPage = new CrashSafePage(exception);
                }
                catch
                {
                    // Last resort - minimal error page
                    MainPage = new ContentPage
                    {
                        Title = "Critical Error",
                        Content = new Label
                        {
                            Text = "Studio 311 LP - Smart Cat Feeder\n\nCritical error occurred.\nPlease restart the app.",
                            HorizontalOptions = LayoutOptions.Center,
                            VerticalOptions = LayoutOptions.Center,
                            TextColor = Colors.Red,
                            FontSize = 16
                        }
                    };
                }
            });
        }
        catch
        {
            // Can't even handle the exception handler failure
            System.Diagnostics.Debug.WriteLine("CRITICAL: Exception handler failed");
        }
    }

    private void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"UNOBSERVED TASK EXCEPTION: {e.Exception}");
            e.SetObserved(); // Prevent app termination

            // Switch to crash safe mode
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    MainPage = new CrashSafePage(e.Exception);
                }
                catch
                {
                    // Ignore if we can't even show the crash page
                }
            });
        }
        catch
        {
            // Can't handle task exception
            System.Diagnostics.Debug.WriteLine("CRITICAL: Task exception handler failed");
        }
    }
}
