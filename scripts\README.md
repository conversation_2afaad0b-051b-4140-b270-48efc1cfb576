# Studio 311 LP - Smart Cat Feeder Build & Deploy Scripts

Automated build and deployment scripts for both mobile app and ESP32 firmware.

## 📱 Mobile App Scripts

### PowerShell Script: `build-deploy-mobile.ps1`

**Basic Usage:**
```powershell
# Build and deploy to connected Android device
.\build-deploy-mobile.ps1

# Build and deploy with app startup
.\build-deploy-mobile.ps1 -StartApp

# Build Release version and increment version
.\build-deploy-mobile.ps1 -Configuration Release -IncrementVersion

# Build APK only (no deployment)
.\build-deploy-mobile.ps1 -SkipDeploy

# Build, deploy, start app, and show logs
.\build-deploy-mobile.ps1 -StartApp -ShowLogs
```

**Parameters:**
- `-Configuration` : Debug or Release (default: Debug)
- `-IncrementVersion` : Increment version before build
- `-SkipDeploy` : Build APK only, don't deploy to device
- `-StartApp` : Start app after deployment
- `-ShowLogs` : Show real-time app logs after deployment

### CMD Wrapper: `build-mobile.cmd`

**Usage:**
```cmd
REM Simple build and deploy
build-mobile.cmd

REM With parameters
build-mobile.cmd -StartApp -ShowLogs
```

## 🔧 ESP32 Firmware Scripts

### PowerShell Script: `build-deploy-firmware.ps1`

**Basic Usage:**
```powershell
# Build and deploy to ESP32
.\build-deploy-firmware.ps1

# Build and deploy with serial monitor
.\build-deploy-firmware.ps1 -Monitor

# Build for specific ESP32 variant
.\build-deploy-firmware.ps1 -Target esp32s3

# Build only (no deployment)
.\build-deploy-firmware.ps1 -SkipDeploy

# Erase flash and deploy fresh firmware
.\build-deploy-firmware.ps1 -Erase

# Open menuconfig before building
.\build-deploy-firmware.ps1 -MenuConfig
```

**Parameters:**
- `-Port` : Serial port (default: auto-detect)
- `-Target` : ESP32 variant (esp32, esp32s2, esp32s3, esp32c3)
- `-IncrementVersion` : Increment firmware version before build
- `-SkipDeploy` : Build only, don't flash to device
- `-Monitor` : Start serial monitor after deployment
- `-Erase` : Erase ESP32 flash before deployment
- `-MenuConfig` : Open ESP-IDF menuconfig

### CMD Wrapper: `build-firmware.cmd`

**Usage:**
```cmd
REM Simple build and deploy
build-firmware.cmd

REM With parameters
build-firmware.cmd -Monitor -Target esp32s3
```

## 🚀 Quick Start Examples

### Deploy Mobile App to Device
```powershell
# Connect Android device with USB debugging enabled
.\build-deploy-mobile.ps1 -StartApp
```

### Deploy ESP32 Firmware
```powershell
# Connect ESP32 via USB
.\build-deploy-firmware.ps1 -Monitor
```

### Full Development Cycle
```powershell
# 1. Build and deploy firmware with monitoring
.\build-deploy-firmware.ps1 -Monitor

# 2. In another terminal, build and deploy mobile app
.\build-deploy-mobile.ps1 -StartApp -ShowLogs

# 3. Test Bluetooth communication between ESP32 and mobile app
```

## 📋 Prerequisites

### Mobile App
- ✅ .NET 9 SDK installed
- ✅ Android SDK and ADB in PATH
- ✅ Android device connected with USB debugging enabled

### ESP32 Firmware
- ✅ ESP-IDF installed and configured
- ✅ IDF_PATH environment variable set
- ✅ ESP32 device connected via USB

## 🔧 Troubleshooting

### Mobile App Issues
- **No device detected**: Enable USB debugging on Android device
- **Build fails**: Ensure .NET 9 SDK is installed
- **Deployment fails**: Check ADB connection and device permissions

### ESP32 Firmware Issues
- **ESP-IDF not found**: Install ESP-IDF and set IDF_PATH
- **Port not found**: Specify port manually with `-Port COM3`
- **Build fails**: Run `.\build-deploy-firmware.ps1 -MenuConfig` to configure

## 📊 Version Management

Both scripts support automatic version incrementing:

```powershell
# Increment mobile app version
.\build-deploy-mobile.ps1 -IncrementVersion

# Increment firmware version
.\build-deploy-firmware.ps1 -IncrementVersion
```

Version numbers are managed by `increment-version.ps1` script and follow semantic versioning.

## 🎯 Studio 311 LP Integration

All scripts are configured for Studio 311 LP branding:
- Mobile app package: `com.studio311lp.catfeeder`
- Company name: Studio 311 LP
- Version tracking and display
- Consistent branding across mobile and firmware
