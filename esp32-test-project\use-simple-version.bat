@echo off
echo ========================================
echo Switching to Simple Test Version
echo ========================================
echo.

echo This will replace main.c with the simple version that:
echo - Tests basic ESP32 functionality
echo - Uses hardcoded WiFi credentials
echo - Skips Bluetooth (avoids compilation issues)
echo - Provides web interface for testing
echo.

pause

echo Backing up original main.c...
if exist main\main.c (
    copy main\main.c main\main_bluetooth.c
    echo Original saved as main_bluetooth.c
)

echo Copying simple version...
copy main\main_simple.c main\main.c

echo.
echo ========================================
echo Simple Version Activated!
echo ========================================
echo.
echo IMPORTANT: Edit main\main.c and change these lines:
echo   #define WIFI_SSID "YourWiFiName"
echo   #define WIFI_PASS "YourWiFiPassword"
echo.
echo Then build and flash:
echo   idf.py build
echo   idf.py -p COM3 flash monitor
echo.
pause
