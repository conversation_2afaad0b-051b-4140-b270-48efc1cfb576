using System.Collections.ObjectModel;
using System.Reflection;
using CatFeeder.Core.Services;
using CatFeeder.Models;

namespace CatFeeder.Mobile.Views;

public partial class MainPage : ContentPage
{
    private readonly IBluetoothService? _bluetoothService;
    private readonly IFeedingHistoryService? _feedingHistoryService;
    private readonly IVersionService? _versionService;
    
    public string ConnectionStatus { get; private set; } = "Disconnected";
    public Color ConnectionStatusColor { get; private set; } = Colors.Red;
    public string LastFeedingTime { get; private set; } = "Never";
    public uint FeedingsToday { get; private set; } = 0;
    public double FoodLevel { get; private set; } = 0;
    public bool IsConnected => _bluetoothService?.ConnectionStatus == BluetoothConnectionStatus.Connected;
    public string ActivityLog { get; private set; } = "";

    // Version information properties
    public string MobileAppVersion { get; private set; } = "";
    public string ESP32Version { get; private set; } = "Unknown";
    public string VersionStatus { get; private set; } = "Unknown";
    public Color VersionStatusColor { get; private set; } = Colors.Gray;

    public ObservableCollection<FeedingEntry> RecentFeedings { get; } = new();

    public MainPage(IBluetoothService bluetoothService, IFeedingHistoryService feedingHistoryService, IVersionService versionService)
    {
        InitializeComponent();

        _bluetoothService = bluetoothService;
        _feedingHistoryService = feedingHistoryService;
        _versionService = versionService;
        
        // Subscribe to events
        if (_bluetoothService != null)
            _bluetoothService.ConnectionStatusChanged += OnConnectionStatusChanged;
            
        if (_feedingHistoryService != null)
            _feedingHistoryService.HistoryUpdated += OnFeedingHistoryUpdated;
        
        BindingContext = this;

        // Initialize mobile app version
        InitializeMobileAppVersion();

        InitializeAsync();
    }
    
    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        
        // Unsubscribe from events to prevent memory leaks
        if (_bluetoothService != null)
            _bluetoothService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            
        if (_feedingHistoryService != null)
            _feedingHistoryService.HistoryUpdated -= OnFeedingHistoryUpdated;
    }
    
    private async void InitializeAsync()
    {
        try {
            if (_feedingHistoryService != null)
                await _feedingHistoryService.InitializeAsync().ConfigureAwait(false);
            
            UpdateUI();
            LogActivity("App started - Ready to connect to Cat Feeder");
        
            // Load and display recent feeding statistics
            await UpdateFeedingStatsAsync().ConfigureAwait(false);
        }
        catch (Exception ex) {
            LogActivity($"Initialization error: {ex.Message}");
        }
    }
    
    private async void OnManualFeedClicked(object? sender, EventArgs e)
    {
        if (_bluetoothService == null)
        {
            await DisplayAlert("Error", "Bluetooth service not available", "OK").ConfigureAwait(false);
            return;
        }
        
        try
        {
            LogActivity("Executing manual feeding...");
            
            // Default portion size from settings
            uint portionSize = 200; // Get from settings
            
            var command = new DeviceCommand { Command = "MANUAL_FEED", Parameters = portionSize.ToString() };
            var response = await _bluetoothService.SendCommandAsync(command).ConfigureAwait(false);
            
            if (response != null && response.Success)
            {
                LogActivity("Manual feeding completed successfully!");
                
                // Sync feeding history after successful feeding
                await SyncFeedingHistoryAsync().ConfigureAwait(false);
                
                // Update feeding statistics display
                await UpdateFeedingStatsAsync().ConfigureAwait(false);
                
                await DisplayAlert("Success", "Cat has been fed!", "OK").ConfigureAwait(false);
            }
            else
            {
                string errorMessage = response?.ErrorMessage ?? "Unknown error";
                LogActivity($"Feeding failed: {errorMessage}");
                await DisplayAlert("Error", $"Feeding failed: {errorMessage}", "OK").ConfigureAwait(false);
            }
        }
        catch (Exception ex)
        {
            LogActivity($"Error during manual feeding: {ex.Message}");
            await DisplayAlert("Error", $"Feeding failed: {ex.Message}", "OK").ConfigureAwait(false);
        }
    }
    
    private async Task SyncFeedingHistoryAsync()
    {
        if (_bluetoothService == null || _feedingHistoryService == null)
            return;
            
        try
        {
            if (_bluetoothService.ConnectionStatus != BluetoothConnectionStatus.Connected)
                return;
            
            LogActivity("Syncing feeding history...");
            
            var device = _bluetoothService.ConnectedDevice;
            if (device == null || string.IsNullOrEmpty(device.Address))
            {
                LogActivity("Cannot sync: Device ID is unknown");
                return;
            }
            
            var syncResponse = await _feedingHistoryService.SyncWithDeviceAsync(_bluetoothService, device.Address).ConfigureAwait(false);
            
            if (syncResponse != null)
            {
                LogActivity($"Synced {syncResponse.EntryCount} new feeding entries");
            }
            else
            {
                LogActivity("Sync completed but no response data received");
            }
            
            // Update UI with latest history
            await UpdateFeedingStatsAsync().ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            LogActivity($"Sync failed: {ex.Message}");
        }
    }
    
    private async Task UpdateFeedingStatsAsync()
    {
        if (_bluetoothService == null)
            return;
            
        try
        {
            // Get feeding stats from the service
            var stats = await _bluetoothService.GetFeedingStatsAsync().ConfigureAwait(false);
            
            if (stats != null)
            {
                FeedingsToday = stats.FeedingsToday;
                
                if (stats.LastFeedingTime == DateTime.MinValue)
                {
                    LastFeedingTime = "Never";
                }
                else
                {
                    LastFeedingTime = stats.LastFeedingTime.ToString("g");
                }
                
                // Update food level if available
                // The ESP32 firmware uses food_level_percent in feeding_controller_status_t
                // Make sure we're using the correct property name from the device response
                if (stats.FoodLevelPercent > 0)
                {
                    FoodLevel = stats.FoodLevelPercent / 100.0;
                    OnPropertyChanged(nameof(FoodLevel));
                }
            }
            else
            {
                // Default values if stats couldn't be retrieved
                FeedingsToday = 0;
                LastFeedingTime = "Unknown";
            }
            
            // Update recent feedings list
            RecentFeedings.Clear();
            var recentEntries = await _bluetoothService.GetRecentFeedingsAsync(5).ConfigureAwait(false);
            
            if (recentEntries != null)
            {
                foreach (var entry in recentEntries)
                {
                    if (entry != null)
                    {
                        RecentFeedings.Add(entry);
                    }
                }
            }
            
            OnPropertyChanged(nameof(FeedingsToday));
            OnPropertyChanged(nameof(LastFeedingTime));
            OnPropertyChanged(nameof(RecentFeedings));
        }
        catch (Exception ex)
        {
            LogActivity($"Error updating feeding stats: {ex.Message}");
        }
    }
    
    private void OnConnectionStatusChanged(object? sender, BluetoothEventArgs e)
    {
        if (e == null)
            return;
        
        ConnectionStatus = e.Status == BluetoothConnectionStatus.Connected ? "Connected" : "Disconnected";
        ConnectionStatusColor = e.Status == BluetoothConnectionStatus.Connected ? Colors.Green : Colors.Red;
        
        OnPropertyChanged(nameof(ConnectionStatus));
        OnPropertyChanged(nameof(ConnectionStatusColor));
        OnPropertyChanged(nameof(IsConnected));
        
        if (e.Status == BluetoothConnectionStatus.Connected)
        {
            LogActivity($"Connected to device: {e.DeviceName ?? "Unknown"}");
            // Use ConfigureAwait(false) to avoid deadlocks
            _ = Task.Run(SyncFeedingHistoryAsync);
            _ = Task.Run(UpdateESP32VersionAsync);
        }
        else
        {
            LogActivity("Disconnected from device");
            ESP32Version = "Not Connected";
            VersionStatus = "Cannot Check";
            VersionStatusColor = Colors.Gray;
            UpdateVersionUI();
        }
    }
    
    private void OnFeedingHistoryUpdated(object? sender, EventArgs e)
    {
        // Use ConfigureAwait(false) to avoid deadlocks
        _ = Task.Run(UpdateFeedingStatsAsync);
    }
    
    private void LogActivity(string message)
    {
        if (string.IsNullOrEmpty(message))
            return;
        
        ActivityLog = $"[{DateTime.Now:HH:mm:ss}] {message}\n{ActivityLog}";
        
        // Trim log if it gets too long
        if (ActivityLog.Length > 5000)
        {
            int cutIndex = ActivityLog.IndexOf('\n', 2500);
            if (cutIndex > 0)
            {
                ActivityLog = ActivityLog[..cutIndex];
            }
        }
        
        OnPropertyChanged(nameof(ActivityLog));
    }
    
    private void UpdateUI()
    {
        OnPropertyChanged(nameof(ConnectionStatus));
        OnPropertyChanged(nameof(ConnectionStatusColor));
        OnPropertyChanged(nameof(LastFeedingTime));
        OnPropertyChanged(nameof(FeedingsToday));
        OnPropertyChanged(nameof(FoodLevel));
        OnPropertyChanged(nameof(IsConnected));
        OnPropertyChanged(nameof(MobileAppVersion));
        OnPropertyChanged(nameof(ESP32Version));
        OnPropertyChanged(nameof(VersionStatus));
        OnPropertyChanged(nameof(VersionStatusColor));
    }

    private void InitializeMobileAppVersion()
    {
        try
        {
            if (_versionService != null)
            {
                MobileAppVersion = _versionService.GetMobileAppVersion();
            }
            else
            {
                // Fallback to assembly version if service not available
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                if (version != null)
                {
                    MobileAppVersion = $"{version.Major}.{version.Minor}.{version.Build}.{version.Revision}";
                }
                else
                {
                    MobileAppVersion = "1.0.0.1"; // Fallback version
                }
            }
        }
        catch
        {
            MobileAppVersion = "1.0.0.1"; // Fallback version
        }
    }

    private async Task UpdateESP32VersionAsync()
    {
        if (_bluetoothService == null || _bluetoothService.ConnectionStatus != BluetoothConnectionStatus.Connected)
        {
            ESP32Version = "Not Connected";
            VersionStatus = "Cannot Check";
            VersionStatusColor = Colors.Gray;
            UpdateVersionUI();
            return;
        }

        try
        {
            LogActivity("Checking ESP32 firmware version...");

            var command = new DeviceCommand { Command = "GET_VERSION", Parameters = "" };
            var response = await _bluetoothService.SendCommandAsync(command).ConfigureAwait(false);

            if (response != null && response.Success && !string.IsNullOrEmpty(response.Data))
            {
                // Parse version response - expected format: "VERSION:{json}"
                if (response.Data.StartsWith("VERSION:"))
                {
                    var jsonData = response.Data.Substring(8); // Remove "VERSION:" prefix
                    try
                    {
                        // Simple JSON parsing for version field
                        var versionStart = jsonData.IndexOf("\"version\":\"") + 11;
                        var versionEnd = jsonData.IndexOf("\"", versionStart);
                        if (versionStart > 10 && versionEnd > versionStart)
                        {
                            ESP32Version = jsonData.Substring(versionStart, versionEnd - versionStart);
                        }
                        else
                        {
                            ESP32Version = "Parse Error";
                        }
                    }
                    catch
                    {
                        ESP32Version = "Parse Error";
                    }
                }
                else
                {
                    ESP32Version = response.Data;
                }

                // Compare versions
                CompareVersions();
                LogActivity($"ESP32 firmware version: {ESP32Version}");
            }
            else
            {
                ESP32Version = "Command Failed";
                VersionStatus = "Check Failed";
                VersionStatusColor = Colors.Orange;
                LogActivity("Failed to get ESP32 version");
            }
        }
        catch (Exception ex)
        {
            ESP32Version = "Error";
            VersionStatus = "Check Error";
            VersionStatusColor = Colors.Red;
            LogActivity($"Error checking ESP32 version: {ex.Message}");
        }

        UpdateVersionUI();
    }

    private void CompareVersions()
    {
        if (ESP32Version == "Not Connected" || ESP32Version == "Error" || ESP32Version == "Command Failed")
        {
            VersionStatus = "Cannot Compare";
            VersionStatusColor = Colors.Gray;
            return;
        }

        if (_versionService != null && _versionService.AreVersionsCompatible(MobileAppVersion, ESP32Version))
        {
            VersionStatus = "✓ Synchronized";
            VersionStatusColor = Colors.Green;
        }
        else if (MobileAppVersion == ESP32Version)
        {
            VersionStatus = "✓ Synchronized";
            VersionStatusColor = Colors.Green;
        }
        else
        {
            VersionStatus = "⚠ Mismatch";
            VersionStatusColor = Colors.Orange;
        }
    }

    private void UpdateVersionUI()
    {
        OnPropertyChanged(nameof(ESP32Version));
        OnPropertyChanged(nameof(VersionStatus));
        OnPropertyChanged(nameof(VersionStatusColor));
    }
}









