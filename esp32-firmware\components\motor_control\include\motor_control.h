/**
 * Motor Control Component
 * 
 * Provides unified interface for both stepper and DC motors
 * Supports flexible configuration for different motor types
 */

#pragma once

#include "esp_err.h"
#include "driver/gpio.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Motor types supported
 */
typedef enum {
    MOTOR_TYPE_STEPPER,     ///< Stepper motor with precise step control
    MOTOR_TYPE_DC           ///< DC motor with PWM control
} motor_type_t;

/**
 * Stepper motor configuration
 */
typedef struct {
    gpio_num_t step_pin;        ///< Step signal pin
    gpio_num_t dir_pin;         ///< Direction control pin
    gpio_num_t enable_pin;      ///< Enable pin (optional, set to GPIO_NUM_NC if not used)
    uint32_t steps_per_revolution; ///< Steps per full revolution
    uint32_t max_speed_rpm;     ///< Maximum speed in RPM
    uint32_t acceleration;      ///< Acceleration in steps/sec²
} stepper_config_t;

/**
 * DC motor configuration
 */
typedef struct {
    gpio_num_t pwm_pin;         ///< PWM control pin
    gpio_num_t dir_pin;         ///< Direction control pin (optional)
    gpio_num_t enable_pin;      ///< Enable pin (optional, set to GPIO_NUM_NC if not used)
    uint32_t pwm_frequency;     ///< PWM frequency in Hz
    uint8_t max_duty_cycle;     ///< Maximum duty cycle (0-100%)
} dc_config_t;

/**
 * Motor status information
 */
typedef struct {
    bool is_ready;              ///< Motor is ready for operation
    motor_type_t type;          ///< Current motor type
    bool is_running;            ///< Motor is currently running
    uint32_t current_position;  ///< Current position (steps for stepper, time for DC)
    esp_err_t last_error;       ///< Last error code
} motor_status_t;



/**
 * Initialize motor control system
 *
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_init(void);

/**
 * Configure stepper motor
 *
 * @param config Stepper motor configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_configure_stepper(const stepper_config_t *config);

/**
 * Configure DC motor
 *
 * @param config DC motor configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_configure_dc(const dc_config_t *config);

/**
 * Set motor type
 *
 * @param motor_type Type of motor to use
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_set_type(motor_type_t motor_type);

/**
 * Get motor status
 *
 * @param status Pointer to status structure to fill
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_get_status(motor_status_t *status);

/**
 * Start motor rotation
 *
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_start(void);

/**
 * Stop motor rotation
 *
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_stop(void);

/**
 * Feed specified portion size
 *
 * @param portion_size Portion size (arbitrary units)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t motor_control_feed(uint8_t portion_size);



#ifdef __cplusplus
}
#endif
