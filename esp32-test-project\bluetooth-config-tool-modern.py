#!/usr/bin/env python3
"""
ESP32 Cat Feeder Bluetooth Configuration Tool (Modern Version)

This tool uses bleak library which works better on modern Windows systems.

Requirements:
- Python 3.7+
- bleak: pip install bleak

Usage:
python bluetooth-config-tool-modern.py
"""

import asyncio
import sys
import time

try:
    from bleak import BleakScanner, BleakClient
except ImportError:
    print("Error: bleak not installed")
    print("Install with: pip install bleak")
    sys.exit(1)

class ESP32ConfigToolModern:
    def __init__(self):
        self.client = None
        self.device_address = None
        self.write_characteristic = None
        self.notify_characteristic = None
        self.response_data = ""
        
    async def scan_for_devices(self):
        """Scan for ESP32 Cat Feeder devices"""
        print("Scanning for ESP32 Cat Feeder devices...")
        print("Make sure your ESP32 is powered on and Bluetooth is enabled.")
        
        devices = await BleakScanner.discover(timeout=10.0)
        
        esp32_devices = []
        for device in devices:
            if device.name and ("CatFeeder" in device.name or "ESP32" in device.name):
                esp32_devices.append(device)
                
        if not esp32_devices:
            print("No ESP32 Cat Feeder devices found.")
            print("Note: This tool looks for BLE devices. Your ESP32 might be using Classic Bluetooth.")
            print("Try using a Bluetooth terminal app on your phone instead.")
            return None
            
        if len(esp32_devices) == 1:
            return esp32_devices[0]
            
        # Multiple devices found, let user choose
        print(f"\nFound {len(esp32_devices)} devices:")
        for i, device in enumerate(esp32_devices):
            print(f"{i+1}. {device.name} ({device.address})")
            
        while True:
            try:
                choice = int(input("Select device (1-{}): ".format(len(esp32_devices))))
                if 1 <= choice <= len(esp32_devices):
                    return esp32_devices[choice-1]
                else:
                    print("Invalid choice. Please try again.")
            except ValueError:
                print("Please enter a number.")
                
    def notification_handler(self, sender, data):
        """Handle notifications from ESP32"""
        try:
            self.response_data += data.decode('utf-8')
        except:
            pass
            
    async def connect(self, device):
        """Connect to ESP32 device"""
        try:
            print(f"Connecting to {device.name} ({device.address})...")
            self.client = BleakClient(device.address)
            await self.client.connect()
            print("Connected successfully!")
            
            # Find UART service characteristics
            services = await self.client.get_services()
            for service in services:
                for char in service.characteristics:
                    if "write" in char.properties:
                        self.write_characteristic = char.uuid
                    if "notify" in char.properties:
                        self.notify_characteristic = char.uuid
                        
            if self.notify_characteristic:
                await self.client.start_notify(self.notify_characteristic, self.notification_handler)
                
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False
            
    async def send_command(self, command):
        """Send command to ESP32 and get response"""
        try:
            if not self.write_characteristic:
                print("No write characteristic found")
                return None
                
            self.response_data = ""
            await self.client.write_gatt_char(self.write_characteristic, (command + "\n").encode())
            
            # Wait for response
            for _ in range(10):  # Wait up to 5 seconds
                await asyncio.sleep(0.5)
                if self.response_data:
                    break
                    
            return self.response_data.strip()
        except Exception as e:
            print(f"Communication error: {e}")
            return None
            
    async def disconnect(self):
        """Disconnect from device"""
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            print("Disconnected from device")

def print_manual_instructions():
    """Print manual configuration instructions"""
    print("\n" + "="*60)
    print("MANUAL CONFIGURATION INSTRUCTIONS")
    print("="*60)
    print()
    print("Since the Python tool has compatibility issues, you can configure")
    print("your ESP32 manually using any Bluetooth terminal app:")
    print()
    print("📱 RECOMMENDED APPS:")
    print("   • Android: 'Bluetooth Terminal' or 'Serial Bluetooth Terminal'")
    print("   • iOS: 'Bluetooth Terminal' or 'LightBlue Explorer'")
    print("   • Windows: 'Bluetooth LE Explorer' (Microsoft Store)")
    print()
    print("🔧 CONFIGURATION STEPS:")
    print("   1. Make sure ESP32 LED is blinking fast (Bluetooth ready)")
    print("   2. Pair with device 'CatFeeder_ESP32' in Bluetooth settings")
    print("   3. Open Bluetooth terminal app")
    print("   4. Connect to 'CatFeeder_ESP32'")
    print("   5. Send these commands:")
    print()
    print("📝 CONFIGURATION COMMANDS:")
    print("   GET_STATUS                    # Check current status")
    print("   WIFI:YourSSID,YourPassword   # Set WiFi (replace with your network)")
    print("   MOTOR:0                      # Set motor type:")
    print("                                #   0 = Stepper motor (recommended)")
    print("                                #   1 = DC motor (time-based)")
    print("                                #   2 = DC motor (sensor-based)")
    print("   FEATURES:1,1,0,1            # Set optional features:")
    print("                                #   Position 1: Food sensor (0/1)")
    print("                                #   Position 2: LED indicators (0/1)")
    print("                                #   Position 3: Buzzer (0/1)")
    print("                                #   Position 4: Manual button (0/1)")
    print("   SAVE_CONFIG                 # Save and apply configuration")
    print()
    print("💡 EXAMPLE CONFIGURATION:")
    print("   WIFI:MyNetwork,MyPassword")
    print("   MOTOR:0")
    print("   FEATURES:1,1,0,1")
    print("   SAVE_CONFIG")
    print()
    print("🔍 AFTER CONFIGURATION:")
    print("   • LED should change to slow blink (WiFi connecting)")
    print("   • Then solid ON (WiFi connected)")
    print("   • Check serial monitor for IP address")
    print("   • Open browser to ESP32's IP for web interface")
    print()
    print("="*60)

async def main():
    """Main function"""
    print("ESP32 Cat Feeder Configuration Tool (Modern)")
    print("=" * 50)
    
    tool = ESP32ConfigToolModern()
    
    try:
        # Try to scan for devices
        device = await tool.scan_for_devices()
        if not device:
            print_manual_instructions()
            return
            
        # Try to connect
        if not await tool.connect(device):
            print_manual_instructions()
            return
            
        print("BLE connection established!")
        print("Note: This uses BLE. If commands don't work, use manual method.")
        
        # Simple command interface
        while True:
            print("\nEnter command (or 'help' for commands, 'quit' to exit):")
            command = input("> ").strip()
            
            if command.lower() == 'quit':
                break
            elif command.lower() == 'help':
                print("Available commands:")
                print("  GET_STATUS")
                print("  WIFI:SSID,PASSWORD")
                print("  MOTOR:0|1|2")
                print("  FEATURES:1,0,1,0")
                print("  SAVE_CONFIG")
            else:
                response = await tool.send_command(command)
                if response:
                    print(f"Response: {response}")
                else:
                    print("No response received")
                    
    except KeyboardInterrupt:
        print("\nConfiguration cancelled by user")
    except Exception as e:
        print(f"Error: {e}")
        print_manual_instructions()
    finally:
        await tool.disconnect()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"Failed to run: {e}")
        print_manual_instructions()
