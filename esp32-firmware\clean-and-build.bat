@echo off
echo === Studio 311 LP - CatFeeder Firmware Build ===

echo Incrementing firmware version...
cd ..
powershell -ExecutionPolicy Bypass -File "increment-version.ps1" -Component firmware
if %ERRORLEVEL% neq 0 (
    echo Failed to increment version!
    pause
    exit /b 1
)

echo Updating version header...
powershell -ExecutionPolicy Bypass -File "update-version-header.ps1"
if %ERRORLEVEL% neq 0 (
    echo Failed to update version header!
    pause
    exit /b 1
)

cd esp32-firmware

echo Cleaning build directory...
if exist build (
    rd /s /q build
    echo Build directory removed.
) else (
    echo Build directory does not exist.
)

echo Setting up ESP-IDF...
call "C:\Espressif\frameworks\esp-idf-v5.4.1\export.bat"

echo Setting target to ESP32...
idf.py set-target esp32

echo Building firmware...
idf.py build

echo Flashing to COM3...
idf.py -p COM3 flash

echo Done!
pause
