# Master build script for CatFeeder project
# Studio 311 LP - CatFeeder Complete Build System
# Builds both ESP32 firmware and mobile app with synchronized versions

param(
    [string]$Configuration = "Release",
    [switch]$SkipFirmware = $false,
    [switch]$SkipMobile = $false,
    [switch]$FlashFirmware = $true,
    [switch]$DeployMobile = $false,
    [string]$ComPort = "COM3"
)

$ErrorActionPreference = "Stop"

Write-Host "=== Studio 311 LP - CatFeeder Complete Build System ===" -ForegroundColor Cyan
Write-Host "Building firmware and mobile app with synchronized versions" -ForegroundColor Yellow

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

try {
    # Step 1: Increment version for both components
    Write-Host "`n=== Step 1: Version Management ===" -ForegroundColor Magenta
    Write-Host "Incrementing synchronized version numbers..." -ForegroundColor Yellow
    
    & powershell -ExecutionPolicy Bypass -File "increment-version.ps1" -Component both
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to increment version!"
        exit 1
    }
    
    # Show current version
    $versionPath = Join-Path $ScriptDir "version.json"
    if (Test-Path $versionPath) {
        $versionData = Get-Content $versionPath | ConvertFrom-Json
        Write-Host "Building version: $($versionData.version)" -ForegroundColor Green
        Write-Host "Company: $($versionData.company)" -ForegroundColor Gray
    }

    # Step 2: Build ESP32 Firmware
    if (-not $SkipFirmware) {
        Write-Host "`n=== Step 2: ESP32 Firmware Build ===" -ForegroundColor Magenta
        
        # Update version header
        Write-Host "Updating firmware version header..." -ForegroundColor Yellow
        & powershell -ExecutionPolicy Bypass -File "update-version-header.ps1"
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to update version header!"
            exit 1
        }
        
        # Build firmware
        Set-Location "esp32-firmware"
        
        Write-Host "Cleaning firmware build directory..." -ForegroundColor Gray
        if (Test-Path "build") {
            Remove-Item "build" -Recurse -Force
        }
        
        Write-Host "Setting up ESP-IDF environment..." -ForegroundColor Yellow
        & cmd /c "call `"C:\Espressif\frameworks\esp-idf-v5.4.1\export.bat`" && idf.py set-target esp32"
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Failed to set ESP32 target!"
            exit 1
        }
        
        Write-Host "Building ESP32 firmware..." -ForegroundColor Green
        & cmd /c "call `"C:\Espressif\frameworks\esp-idf-v5.4.1\export.bat`" && idf.py build"
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Firmware build failed!"
            exit 1
        }
        
        if ($FlashFirmware) {
            Write-Host "Flashing firmware to $ComPort..." -ForegroundColor Green
            & cmd /c "call `"C:\Espressif\frameworks\esp-idf-v5.4.1\export.bat`" && idf.py -p $ComPort flash"
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Firmware flash failed, but continuing with mobile app build..."
            } else {
                Write-Host "Firmware flashed successfully!" -ForegroundColor Green
            }
        }
        
        Set-Location $ScriptDir
    } else {
        Write-Host "`n=== Step 2: ESP32 Firmware Build (SKIPPED) ===" -ForegroundColor DarkGray
    }

    # Step 3: Build Mobile App
    if (-not $SkipMobile) {
        Write-Host "`n=== Step 3: Mobile App Build ===" -ForegroundColor Magenta
        
        Set-Location "mobile-app"
        
        Write-Host "Building mobile app..." -ForegroundColor Green
        & powershell -ExecutionPolicy Bypass -File "build-and-deploy.ps1" -Configuration $Configuration -Platform android -Deploy:$DeployMobile -SkipVersionIncrement
        if ($LASTEXITCODE -ne 0) {
            Write-Error "Mobile app build failed!"
            exit 1
        }
        
        Set-Location $ScriptDir
    } else {
        Write-Host "`n=== Step 3: Mobile App Build (SKIPPED) ===" -ForegroundColor DarkGray
    }

    # Step 4: Summary
    Write-Host "`n=== Build Summary ===" -ForegroundColor Magenta
    if (Test-Path $versionPath) {
        $versionData = Get-Content $versionPath | ConvertFrom-Json
        Write-Host "✓ Version: $($versionData.version)" -ForegroundColor Green
        Write-Host "✓ Company: $($versionData.company)" -ForegroundColor Green
        
        if (-not $SkipFirmware) {
            Write-Host "✓ ESP32 Firmware: Built and " -NoNewline -ForegroundColor Green
            if ($FlashFirmware) {
                Write-Host "flashed to $ComPort" -ForegroundColor Green
            } else {
                Write-Host "ready for flashing" -ForegroundColor Yellow
            }
        }
        
        if (-not $SkipMobile) {
            Write-Host "✓ Mobile App: Built for Android " -NoNewline -ForegroundColor Green
            if ($DeployMobile) {
                Write-Host "and deployed" -ForegroundColor Green
            } else {
                Write-Host "and ready for deployment" -ForegroundColor Yellow
            }
        }
    }
    
    Write-Host "`nBuild completed successfully! Both firmware and mobile app have synchronized version numbers." -ForegroundColor Green
    
    if (-not $SkipFirmware -and -not $SkipMobile) {
        Write-Host "`nNext steps:" -ForegroundColor Cyan
        Write-Host "1. Connect to the ESP32 device via Bluetooth in the mobile app" -ForegroundColor White
        Write-Host "2. Use GET_VERSION command to verify firmware version matches mobile app" -ForegroundColor White
        Write-Host "3. Test manual feeding and other functionality" -ForegroundColor White
    }
}
catch {
    Write-Error "Build failed with error: $($_.Exception.Message)"
    exit 1
}
finally {
    # Return to original directory
    Set-Location $ScriptDir
}
