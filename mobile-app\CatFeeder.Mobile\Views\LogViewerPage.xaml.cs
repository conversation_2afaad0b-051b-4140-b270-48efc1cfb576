using CatFeeder.Core.Services;

namespace CatFeeder.Mobile.Views
{
    public partial class LogViewerPage : ContentPage
    {
        private readonly ILoggingService _loggingService;

        public LogViewerPage(ILoggingService loggingService)
        {
            InitializeComponent();
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));

            Loaded += OnPageLoaded;
        }

        private async void OnPageLoaded(object? sender, EventArgs e)
        {
            await LoadLogsAsync();
        }

        private async Task LoadLogsAsync()
        {
            try
            {
                LogContentLabel.Text = "Loading logs...";
                var logContent = await _loggingService.GetLogContentsAsync(2000);
                LogContentLabel.Text = string.IsNullOrEmpty(logContent) ? "No logs available." : logContent;
            }
            catch (Exception ex)
            {
                LogContentLabel.Text = $"Failed to load logs: {ex.Message}";
            }
        }

        private async void OnRefreshClicked(object sender, EventArgs e)
        {
            await LoadLogsAsync();
        }

        private async void OnClearLogsClicked(object sender, EventArgs e)
        {
            bool confirm = await DisplayAlert("Clear Logs",
                "Are you sure you want to clear all logs? This action cannot be undone.",
                "Yes, Clear Logs", "Cancel");

            if (confirm)
            {
                try
                {
                    await _loggingService.ClearLogsAsync();
                    await LoadLogsAsync();
                    await DisplayAlert("Success", "Logs have been cleared.", "OK");
                }
                catch (Exception ex)
                {
                    await DisplayAlert("Error", $"Failed to clear logs: {ex.Message}", "OK");
                }
            }
        }

        private async void OnShareLogsClicked(object sender, EventArgs e)
        {
            try
            {
                var logFilePath = await _loggingService.GetLogFilePathAsync();

                if (!File.Exists(logFilePath))
                {
                    await DisplayAlert("Error", "Log file does not exist.", "OK");
                    return;
                }

                await Share.RequestAsync(new ShareFileRequest
                {
                    Title = "Share Cat Feeder Logs",
                    File = new ShareFile(logFilePath)
                });
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to share logs: {ex.Message}", "OK");
            }
        }

        private async void OnCloseClicked(object sender, EventArgs e)
        {
            await Navigation.PopAsync();
        }
    }
}