/**
 * ESP32-WROOM-32 Test Project
 * 
 * Features:
 * - Built-in LED blinking patterns for status indication
 * - Bluetooth configuration interface
 * - WiFi connection with web status page
 * - Motor type and optional features configuration via Bluetooth
 * 
 * LED Patterns:
 * - Fast blink (200ms): Bluetooth ready for configuration
 * - Slow blink (1000ms): WiFi connecting
 * - Solid ON: WiFi connected, web server running
 * - Double blink: Error state
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_http_server.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "driver/gpio.h"
#include "esp_timer.h"

// Bluetooth includes
#include "esp_bt.h"
#include "esp_bt_main.h"
#include "esp_gap_bt_api.h"
#include "esp_spp_api.h"

static const char *TAG = "ESP32_TEST";

// ESP-WROOM-32 built-in LED pin (GPIO 2)
#define LED_PIN GPIO_NUM_2

// Event group for synchronization
static EventGroupHandle_t status_event_group;

// Event bits
#define BT_READY_BIT        BIT0
#define WIFI_CONNECTED_BIT  BIT1
#define CONFIG_COMPLETE_BIT BIT2
#define ERROR_BIT           BIT3

// LED control
static esp_timer_handle_t led_timer;
static bool led_state = false;
static int led_pattern = 0; // 0=off, 1=fast, 2=slow, 3=solid, 4=double

// Configuration structure
typedef struct {
    char wifi_ssid[32];
    char wifi_password[64];
    int motor_type; // 0=stepper, 1=dc_time, 2=dc_sensor
    bool enable_food_sensor;
    bool enable_led_indicators;
    bool enable_buzzer;
    bool enable_manual_button;
    bool config_complete;
} device_config_t;

static device_config_t device_config = {0};
static httpd_handle_t server = NULL;

/**
 * LED control timer callback
 */
static void led_timer_callback(void* arg)
{
    switch (led_pattern) {
        case 0: // Off
            gpio_set_level(LED_PIN, 0);
            break;
        case 1: // Fast blink (200ms) - BT ready
            led_state = !led_state;
            gpio_set_level(LED_PIN, led_state);
            break;
        case 2: // Slow blink (1000ms) - WiFi connecting
            led_state = !led_state;
            gpio_set_level(LED_PIN, led_state);
            break;
        case 3: // Solid ON - WiFi connected
            gpio_set_level(LED_PIN, 1);
            break;
        case 4: // Double blink - Error
            static int blink_count = 0;
            if (blink_count < 4) {
                led_state = !led_state;
                gpio_set_level(LED_PIN, led_state);
                blink_count++;
            } else {
                gpio_set_level(LED_PIN, 0);
                blink_count = 0;
                vTaskDelay(pdMS_TO_TICKS(1000)); // Pause between double blinks
            }
            break;
    }
}

/**
 * Set LED pattern
 */
static void set_led_pattern(int pattern)
{
    led_pattern = pattern;
    
    // Stop current timer
    if (led_timer) {
        esp_timer_stop(led_timer);
    }
    
    // Start timer with appropriate interval
    uint64_t interval_us;
    switch (pattern) {
        case 1: interval_us = 200000; break;  // 200ms
        case 2: interval_us = 1000000; break; // 1000ms
        case 3: interval_us = 100000; break;  // 100ms (for solid, just set once)
        case 4: interval_us = 150000; break;  // 150ms
        default: return; // Pattern 0 (off) doesn't need timer
    }
    
    if (pattern > 0) {
        esp_timer_start_periodic(led_timer, interval_us);
    }
}

/**
 * Initialize LED
 */
static void init_led(void)
{
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << LED_PIN),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    gpio_config(&io_conf);
    
    // Create LED timer
    esp_timer_create_args_t timer_args = {
        .callback = led_timer_callback,
        .name = "led_timer"
    };
    esp_timer_create(&timer_args, &led_timer);
    
    ESP_LOGI(TAG, "LED initialized on GPIO %d", LED_PIN);
}

/**
 * Save configuration to NVS
 */
static esp_err_t save_config(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) return err;
    
    err = nvs_set_blob(nvs_handle, "device_config", &device_config, sizeof(device_config));
    if (err == ESP_OK) {
        err = nvs_commit(nvs_handle);
    }
    
    nvs_close(nvs_handle);
    return err;
}

/**
 * Load configuration from NVS
 */
static esp_err_t load_config(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t err = nvs_open("config", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) return err;

    size_t required_size = sizeof(device_config);
    err = nvs_get_blob(nvs_handle, "device_config", &device_config, &required_size);

    nvs_close(nvs_handle);
    return err;
}

/**
 * Bluetooth SPP callback
 */
static void esp_spp_cb(esp_spp_cb_event_t event, esp_spp_cb_param_t *param)
{
    switch (event) {
        case ESP_SPP_INIT_EVT:
            ESP_LOGI(TAG, "ESP_SPP_INIT_EVT");
            esp_spp_start_srv(ESP_SPP_SEC_AUTHENTICATE, ESP_SPP_ROLE_SLAVE, 0, "CatFeeder_Config");
            break;

        case ESP_SPP_DISCOVERY_COMP_EVT:
            ESP_LOGI(TAG, "ESP_SPP_DISCOVERY_COMP_EVT");
            break;

        case ESP_SPP_OPEN_EVT:
            ESP_LOGI(TAG, "ESP_SPP_OPEN_EVT");
            break;

        case ESP_SPP_CLOSE_EVT:
            ESP_LOGI(TAG, "ESP_SPP_CLOSE_EVT");
            break;

        case ESP_SPP_START_EVT:
            ESP_LOGI(TAG, "ESP_SPP_START_EVT");
            xEventGroupSetBits(status_event_group, BT_READY_BIT);
            set_led_pattern(1); // Fast blink - BT ready
            break;

        case ESP_SPP_DATA_IND_EVT:
            ESP_LOGI(TAG, "ESP_SPP_DATA_IND_EVT len=%d", param->data_ind.len);

            // Process received configuration data
            char *data = (char*)param->data_ind.data;
            data[param->data_ind.len] = '\0'; // Null terminate

            ESP_LOGI(TAG, "Received: %s", data);

            // Parse JSON-like configuration
            if (strstr(data, "GET_STATUS")) {
                // Send current status
                char response[512];
                snprintf(response, sizeof(response),
                    "STATUS:{"
                    "\"wifi_connected\":%s,"
                    "\"config_complete\":%s,"
                    "\"motor_type\":%d,"
                    "\"food_sensor\":%s,"
                    "\"led_indicators\":%s,"
                    "\"buzzer\":%s,"
                    "\"manual_button\":%s"
                    "}\n",
                    (xEventGroupGetBits(status_event_group) & WIFI_CONNECTED_BIT) ? "true" : "false",
                    device_config.config_complete ? "true" : "false",
                    device_config.motor_type,
                    device_config.enable_food_sensor ? "true" : "false",
                    device_config.enable_led_indicators ? "true" : "false",
                    device_config.enable_buzzer ? "true" : "false",
                    device_config.enable_manual_button ? "true" : "false"
                );
                esp_spp_write(param->data_ind.handle, strlen(response), (uint8_t*)response);
            }
            else if (strstr(data, "WIFI:")) {
                // Parse WiFi configuration: WIFI:SSID,PASSWORD
                char *wifi_data = strstr(data, "WIFI:") + 5;
                char *comma = strchr(wifi_data, ',');
                if (comma) {
                    *comma = '\0';
                    strncpy(device_config.wifi_ssid, wifi_data, sizeof(device_config.wifi_ssid) - 1);
                    strncpy(device_config.wifi_password, comma + 1, sizeof(device_config.wifi_password) - 1);
                    ESP_LOGI(TAG, "WiFi config: SSID=%s", device_config.wifi_ssid);
                    esp_spp_write(param->data_ind.handle, 8, (uint8_t*)"WIFI_OK\n");
                }
            }
            else if (strstr(data, "MOTOR:")) {
                // Parse motor type: MOTOR:0 (stepper), MOTOR:1 (dc_time), MOTOR:2 (dc_sensor)
                device_config.motor_type = atoi(strstr(data, "MOTOR:") + 6);
                ESP_LOGI(TAG, "Motor type set to: %d", device_config.motor_type);
                esp_spp_write(param->data_ind.handle, 9, (uint8_t*)"MOTOR_OK\n");
            }
            else if (strstr(data, "FEATURES:")) {
                // Parse features: FEATURES:1,0,1,0 (food_sensor,led,buzzer,button)
                char *features = strstr(data, "FEATURES:") + 9;
                device_config.enable_food_sensor = (features[0] == '1');
                device_config.enable_led_indicators = (features[2] == '1');
                device_config.enable_buzzer = (features[4] == '1');
                device_config.enable_manual_button = (features[6] == '1');
                ESP_LOGI(TAG, "Features: sensor=%d, led=%d, buzzer=%d, button=%d",
                    device_config.enable_food_sensor, device_config.enable_led_indicators,
                    device_config.enable_buzzer, device_config.enable_manual_button);
                esp_spp_write(param->data_ind.handle, 12, (uint8_t*)"FEATURES_OK\n");
            }
            else if (strstr(data, "SAVE_CONFIG")) {
                // Save configuration and mark as complete
                device_config.config_complete = true;
                save_config();
                xEventGroupSetBits(status_event_group, CONFIG_COMPLETE_BIT);
                ESP_LOGI(TAG, "Configuration saved");
                esp_spp_write(param->data_ind.handle, 10, (uint8_t*)"CONFIG_OK\n");
            }
            break;

        default:
            break;
    }
}

/**
 * Bluetooth GAP callback
 */
static void esp_bt_gap_cb(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param)
{
    switch (event) {
        case ESP_BT_GAP_AUTH_CMPL_EVT:
            if (param->auth_cmpl.stat == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "authentication success: %s", param->auth_cmpl.device_name);
            } else {
                ESP_LOGE(TAG, "authentication failed, status:%d", param->auth_cmpl.stat);
            }
            break;
        default:
            break;
    }
}

/**
 * WiFi event handler
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        ESP_LOGI(TAG, "WiFi disconnected, trying to reconnect...");
        set_led_pattern(2); // Slow blink - connecting
        esp_wifi_connect();
        xEventGroupClearBits(status_event_group, WIFI_CONNECTED_BIT);
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        xEventGroupSetBits(status_event_group, WIFI_CONNECTED_BIT);
        set_led_pattern(3); // Solid ON - connected
    }
}

/**
 * HTTP GET handler for status page
 */
static esp_err_t status_get_handler(httpd_req_t *req)
{
    const char* html_template =
        "<!DOCTYPE html>"
        "<html><head><title>ESP32 Cat Feeder Test</title>"
        "<meta name='viewport' content='width=device-width, initial-scale=1'>"
        "<style>"
        "body{font-family:Arial;margin:20px;background:#f0f0f0}"
        ".container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1)}"
        ".status{padding:10px;margin:10px 0;border-radius:5px}"
        ".ok{background:#d4edda;color:#155724;border:1px solid #c3e6cb}"
        ".warning{background:#fff3cd;color:#856404;border:1px solid #ffeaa7}"
        ".error{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb}"
        "h1{color:#333;text-align:center}"
        "h2{color:#666;border-bottom:2px solid #007bff;padding-bottom:5px}"
        ".config-item{margin:10px 0;padding:8px;background:#f8f9fa;border-left:4px solid #007bff}"
        "button{background:#007bff;color:white;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;margin:5px}"
        "button:hover{background:#0056b3}"
        "</style></head><body>"
        "<div class='container'>"
        "<h1>🐱 ESP32 Cat Feeder Test</h1>"
        "<h2>Device Status</h2>"
        "<div class='status %s'>WiFi: %s</div>"
        "<div class='status %s'>Bluetooth: %s</div>"
        "<div class='status %s'>Configuration: %s</div>"
        "<h2>Current Configuration</h2>"
        "<div class='config-item'><strong>Motor Type:</strong> %s</div>"
        "<div class='config-item'><strong>Food Level Sensor:</strong> %s</div>"
        "<div class='config-item'><strong>LED Indicators:</strong> %s</div>"
        "<div class='config-item'><strong>Buzzer:</strong> %s</div>"
        "<div class='config-item'><strong>Manual Button:</strong> %s</div>"
        "<h2>System Information</h2>"
        "<div class='config-item'><strong>Chip Model:</strong> ESP32-WROOM-32</div>"
        "<div class='config-item'><strong>Free Heap:</strong> %d bytes</div>"
        "<div class='config-item'><strong>Uptime:</strong> %lld seconds</div>"
        "<h2>Actions</h2>"
        "<button onclick='location.reload()'>Refresh Status</button>"
        "<button onclick='testLED()'>Test LED</button>"
        "<script>"
        "function testLED(){fetch('/test_led').then(()=>alert('LED test completed!'))}"
        "</script>"
        "</div></body></html>";

    char html_response[2048];

    // Get system info
    uint32_t free_heap = esp_get_free_heap_size();
    int64_t uptime = esp_timer_get_time() / 1000000;

    // Status strings
    bool wifi_connected = (xEventGroupGetBits(status_event_group) & WIFI_CONNECTED_BIT) != 0;
    bool bt_ready = (xEventGroupGetBits(status_event_group) & BT_READY_BIT) != 0;

    const char* motor_types[] = {"Stepper Motor", "DC Motor (Time)", "DC Motor (Sensor)"};

    snprintf(html_response, sizeof(html_response), html_template,
        wifi_connected ? "ok" : "error",
        wifi_connected ? "Connected" : "Disconnected",
        bt_ready ? "ok" : "warning",
        bt_ready ? "Ready for Configuration" : "Not Ready",
        device_config.config_complete ? "ok" : "warning",
        device_config.config_complete ? "Complete" : "Needs Configuration",
        motor_types[device_config.motor_type % 3],
        device_config.enable_food_sensor ? "Enabled" : "Disabled",
        device_config.enable_led_indicators ? "Enabled" : "Disabled",
        device_config.enable_buzzer ? "Enabled" : "Disabled",
        device_config.enable_manual_button ? "Enabled" : "Disabled",
        free_heap,
        uptime
    );

    httpd_resp_set_type(req, "text/html");
    httpd_resp_send(req, html_response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

/**
 * HTTP GET handler for LED test
 */
static esp_err_t test_led_handler(httpd_req_t *req)
{
    ESP_LOGI(TAG, "LED test requested");

    // Save current pattern
    int original_pattern = led_pattern;

    // Test sequence: fast blink for 2 seconds
    set_led_pattern(1);
    vTaskDelay(pdMS_TO_TICKS(2000));

    // Restore original pattern
    set_led_pattern(original_pattern);

    httpd_resp_send(req, "LED test completed", HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

/**
 * Start HTTP server
 */
static httpd_handle_t start_webserver(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.server_port = 80;

    ESP_LOGI(TAG, "Starting HTTP server on port %d", config.server_port);

    if (httpd_start(&server, &config) == ESP_OK) {
        // Register URI handlers
        httpd_uri_t status_uri = {
            .uri = "/",
            .method = HTTP_GET,
            .handler = status_get_handler,
            .user_ctx = NULL
        };
        httpd_register_uri_handler(server, &status_uri);

        httpd_uri_t test_led_uri = {
            .uri = "/test_led",
            .method = HTTP_GET,
            .handler = test_led_handler,
            .user_ctx = NULL
        };
        httpd_register_uri_handler(server, &test_led_uri);

        return server;
    }

    ESP_LOGE(TAG, "Failed to start HTTP server");
    return NULL;
}

/**
 * Initialize WiFi
 */
static void init_wifi(void)
{
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();

    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL));

    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "WiFi initialized");
}

/**
 * Connect to WiFi
 */
static void connect_wifi(void)
{
    if (strlen(device_config.wifi_ssid) == 0) {
        ESP_LOGW(TAG, "No WiFi SSID configured");
        return;
    }

    wifi_config_t wifi_config = {0};
    strncpy((char*)wifi_config.sta.ssid, device_config.wifi_ssid, sizeof(wifi_config.sta.ssid) - 1);
    strncpy((char*)wifi_config.sta.password, device_config.wifi_password, sizeof(wifi_config.sta.password) - 1);

    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_LOGI(TAG, "Connecting to WiFi SSID: %s", device_config.wifi_ssid);

    set_led_pattern(2); // Slow blink - connecting
    ESP_ERROR_CHECK(esp_wifi_connect());
}

/**
 * Initialize Bluetooth
 */
static void init_bluetooth(void)
{
    ESP_ERROR_CHECK(esp_bt_controller_mem_release(ESP_BT_MODE_BLE));

    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_bt_controller_init(&bt_cfg));
    ESP_ERROR_CHECK(esp_bt_controller_enable(ESP_BT_MODE_CLASSIC_BT));
    ESP_ERROR_CHECK(esp_bluedroid_init());
    ESP_ERROR_CHECK(esp_bluedroid_enable());

    ESP_ERROR_CHECK(esp_bt_gap_register_callback(esp_bt_gap_cb));
    ESP_ERROR_CHECK(esp_spp_register_callback(esp_spp_cb));
    ESP_ERROR_CHECK(esp_spp_init(ESP_SPP_MODE_CB));

    // Set device name
    ESP_ERROR_CHECK(esp_bt_dev_set_device_name("CatFeeder_ESP32"));

    // Set discoverable and connectable
    ESP_ERROR_CHECK(esp_bt_gap_set_scan_mode(ESP_BT_CONNECTABLE, ESP_BT_GENERAL_DISCOVERABLE));

    ESP_LOGI(TAG, "Bluetooth initialized - Device name: CatFeeder_ESP32");
}

/**
 * Initialize NVS
 */
static void init_nvs(void)
{
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    ESP_LOGI(TAG, "NVS initialized");
}

/**
 * Main application task
 */
static void app_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Application task started");

    // Wait for Bluetooth to be ready
    xEventGroupWaitBits(status_event_group, BT_READY_BIT, pdFALSE, pdFALSE, portMAX_DELAY);
    ESP_LOGI(TAG, "Bluetooth ready - waiting for configuration...");

    // Main application loop
    while (1) {
        EventBits_t bits = xEventGroupGetBits(status_event_group);

        // Check if configuration is complete and we should connect to WiFi
        if ((bits & CONFIG_COMPLETE_BIT) && !(bits & WIFI_CONNECTED_BIT)) {
            if (strlen(device_config.wifi_ssid) > 0) {
                ESP_LOGI(TAG, "Configuration complete, connecting to WiFi...");
                connect_wifi();
            }
        }

        // Start web server when WiFi is connected
        if ((bits & WIFI_CONNECTED_BIT) && server == NULL) {
            ESP_LOGI(TAG, "Starting web server...");
            server = start_webserver();
            if (server) {
                ESP_LOGI(TAG, "Web server started successfully");
                ESP_LOGI(TAG, "Open your browser and go to the ESP32's IP address");
            }
        }

        // Status logging every 30 seconds
        static int log_counter = 0;
        if (++log_counter >= 30) {
            log_counter = 0;
            ESP_LOGI(TAG, "Status - BT:%s WiFi:%s Config:%s Server:%s",
                (bits & BT_READY_BIT) ? "OK" : "NO",
                (bits & WIFI_CONNECTED_BIT) ? "OK" : "NO",
                device_config.config_complete ? "OK" : "NO",
                server ? "OK" : "NO"
            );
        }

        vTaskDelay(pdMS_TO_TICKS(1000)); // 1 second delay
    }
}

/**
 * Application entry point
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32-WROOM-32 Cat Feeder Test v1.0");
    ESP_LOGI(TAG, "Built on %s %s", __DATE__, __TIME__);

    // Create event group
    status_event_group = xEventGroupCreate();
    if (status_event_group == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return;
    }

    // Initialize components
    init_nvs();
    init_led();

    // Load saved configuration
    esp_err_t ret = load_config();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Configuration loaded from NVS");
        if (device_config.config_complete) {
            xEventGroupSetBits(status_event_group, CONFIG_COMPLETE_BIT);
        }
    } else {
        ESP_LOGI(TAG, "No saved configuration found, using defaults");
        // Set default configuration
        strcpy(device_config.wifi_ssid, "");
        strcpy(device_config.wifi_password, "");
        device_config.motor_type = 0; // Stepper
        device_config.enable_food_sensor = false;
        device_config.enable_led_indicators = true;
        device_config.enable_buzzer = false;
        device_config.enable_manual_button = true;
        device_config.config_complete = false;
    }

    // Initialize Bluetooth for configuration
    init_bluetooth();

    // Initialize WiFi (but don't connect yet)
    init_wifi();

    // Create main application task
    xTaskCreate(app_task, "app_task", 4096, NULL, 5, NULL);

    ESP_LOGI(TAG, "=== ESP32 Cat Feeder Test Ready ===");
    ESP_LOGI(TAG, "LED Patterns:");
    ESP_LOGI(TAG, "  Fast blink (200ms) = Bluetooth ready for config");
    ESP_LOGI(TAG, "  Slow blink (1000ms) = WiFi connecting");
    ESP_LOGI(TAG, "  Solid ON = WiFi connected, web server running");
    ESP_LOGI(TAG, "  Double blink = Error state");
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "Bluetooth Configuration Commands:");
    ESP_LOGI(TAG, "  GET_STATUS - Get current status");
    ESP_LOGI(TAG, "  WIFI:SSID,PASSWORD - Set WiFi credentials");
    ESP_LOGI(TAG, "  MOTOR:0|1|2 - Set motor type (0=stepper, 1=dc_time, 2=dc_sensor)");
    ESP_LOGI(TAG, "  FEATURES:1,0,1,0 - Set features (food_sensor,led,buzzer,button)");
    ESP_LOGI(TAG, "  SAVE_CONFIG - Save configuration and connect to WiFi");
    ESP_LOGI(TAG, "");
    ESP_LOGI(TAG, "Connect via Bluetooth to device: CatFeeder_ESP32");
}
