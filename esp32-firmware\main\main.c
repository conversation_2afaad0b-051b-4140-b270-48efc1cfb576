/**
 * ESP32 Cat Feeder - Main Application
 *
 * This is the main entry point and orchestrator for the ESP32 Cat Feeder firmware.
 * It initializes all subsystems, manages their lifecycle, and coordinates their operation.
 *
 * System Architecture:
 * - Motor Control: Supports stepper motors and DC motors (time-based or sensor-based)
 * - WiFi Manager: Handles WiFi connection and network management
 * - Bluetooth Manager: Provides SPP communication with mobile apps
 * - Web Server: HTTP interface for browser-based control
 * - Scheduler: Manages automatic feeding schedules
 * - Feeding Controller: Coordinates feeding operations and safety checks
 * - Feeding History: Tracks and synchronizes feeding events
 * - Configuration: Persistent storage of device settings
 *
 * Initialization Sequence:
 * 1. Initialize NVS (Non-Volatile Storage) for persistent data
 * 2. Initialize event loop for inter-component communication
 * 3. Load device configuration from NVS
 * 4. Initialize all subsystems (motor, WiFi, Bluetooth, etc.)
 * 5. Start network services (web server)
 * 6. Start feeding scheduler
 * 7. Enter main monitoring loop
 *
 * External Libraries Used:
 * - freertos/: Real-time operating system for task management and synchronization
 * - esp_system.h: System-level functions (restart, chip info, etc.)
 * - esp_wifi.h: WiFi stack management and connection handling
 * - esp_event.h: Event loop system for inter-component communication
 * - nvs_flash.h: Non-volatile storage for persistent configuration
 * - esp_timer.h: High-resolution timers for precise timing
 */

#include <stdio.h>                    // Standard C I/O functions
#include <string.h>                   // Standard C string manipulation
#include "freertos/FreeRTOS.h"        // FreeRTOS core functions and types
#include "freertos/task.h"            // Task creation and management
#include "freertos/event_groups.h"    // Event groups for synchronization between tasks
#include "esp_system.h"               // System functions (restart, chip info, heap info)
#include "esp_wifi.h"                 // WiFi stack management and API
#include "esp_event.h"                // Event loop system for component communication
#include "esp_log.h"                  // ESP32 logging system
#include "nvs_flash.h"                // Non-volatile storage in flash memory
#include "esp_timer.h"                // High-resolution timer API
#include "driver/gpio.h"              // GPIO control for LED

// Project-specific component headers
#include "motor_control.h"            // Motor control abstraction layer
#include "wifi_manager.h"             // WiFi connection management
#include "bluetooth_manager.h"        // Bluetooth SPP communication
#include "web_server.h"               // HTTP server for web interface
#include "scheduler.h"                // Feeding schedule management
#include "config.h"                   // Configuration system
#include "app_config.h"               // Application-specific configuration
#include "feeding_controller.h"       // Feeding operation coordination
#include "bt_commands.h"              // Bluetooth command handlers

// Logging tag for main application
static const char *TAG = "CAT_FEEDER_MAIN";

// ESP-WROOM-32 built-in LED pin (GPIO 2)
#define LED_PIN GPIO_NUM_2

// ============================================================================
// GLOBAL SYNCHRONIZATION AND STATE MANAGEMENT
// ============================================================================

/**
 * Event group for coordinating initialization and operation of all subsystems
 *
 * Event groups allow multiple tasks to wait for specific conditions to be met
 * before proceeding. This ensures proper initialization order and prevents
 * race conditions between components.
 *
 * FreeRTOS Event Group Functions:
 * - xEventGroupCreate(): Create a new event group
 * - xEventGroupSetBits(): Set one or more event bits (signal completion)
 * - xEventGroupWaitBits(): Wait for specific bits to be set
 * - xEventGroupClearBits(): Clear specific event bits
 */
static EventGroupHandle_t app_event_group;

// LED control
static esp_timer_handle_t led_timer;
static bool led_state = false;
static int led_pattern = 0; // 0=off, 1=fast, 2=slow, 3=solid, 4=double

/**
 * Event bit definitions for system state tracking
 *
 * Each bit represents the completion of a specific initialization phase.
 * Tasks can wait for multiple bits to ensure all dependencies are ready.
 *
 * BIT0-BIT23 are available for use (24 bits total in FreeRTOS event groups)
 */
#define WIFI_CONNECTED_BIT      BIT0    // WiFi connection established
#define CONFIG_LOADED_BIT       BIT1    // Device configuration loaded from NVS
#define MOTOR_READY_BIT         BIT2    // Motor control system initialized
#define SCHEDULER_READY_BIT     BIT3    // Feeding scheduler initialized

// ============================================================================
// LED STATUS CONTROL FUNCTIONS
// ============================================================================

/**
 * LED control timer callback
 *
 * LED Patterns:
 * 0 = OFF - LED is off
 * 1 = Fast blink (200ms) - Starting up / BT ready
 * 2 = Slow blink (1000ms) - WiFi connecting
 * 3 = Solid ON - WiFi connected
 * 4 = Double blink - Error
 */
static void led_timer_callback(void* arg)
{
    switch (led_pattern) {
        case 0: // Off
            gpio_set_level(LED_PIN, 0);
            break;
        case 1: // Fast blink (200ms) - Starting up
            led_state = !led_state;
            gpio_set_level(LED_PIN, led_state);
            break;
        case 2: // Slow blink (1000ms) - WiFi connecting
            led_state = !led_state;
            gpio_set_level(LED_PIN, led_state);
            break;
        case 3: // Solid ON - WiFi connected
            gpio_set_level(LED_PIN, 1);
            break;
        case 4: // Double blink - Error
            static int blink_count = 0;
            if (blink_count < 4) {
                led_state = !led_state;
                gpio_set_level(LED_PIN, led_state);
                blink_count++;
            } else {
                gpio_set_level(LED_PIN, 0);
                blink_count = 0;
                vTaskDelay(pdMS_TO_TICKS(1000)); // Pause between double blinks
            }
            break;
    }
}

/**
 * Set LED pattern
 */
static void set_led_pattern(int pattern)
{
    led_pattern = pattern;

    // Stop current timer
    if (led_timer) {
        esp_timer_stop(led_timer);
    }

    // Start timer with appropriate interval
    uint64_t interval_us;
    switch (pattern) {
        case 1: interval_us = 200000; break;  // 200ms
        case 2: interval_us = 1000000; break; // 1000ms
        case 3: interval_us = 100000; break;  // 100ms (for solid, just set once)
        case 4: interval_us = 150000; break;  // 150ms
        default: return; // Pattern 0 (off) doesn't need timer
    }

    if (pattern > 0) {
        esp_timer_start_periodic(led_timer, interval_us);
    }
}

/**
 * Initialize LED
 */
static esp_err_t init_led(void)
{
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << LED_PIN),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure LED GPIO: %s", esp_err_to_name(ret));
        return ret;
    }

    // Create LED timer
    esp_timer_create_args_t timer_args = {
        .callback = led_timer_callback,
        .name = "led_timer"
    };
    ret = esp_timer_create(&timer_args, &led_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create LED timer: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "LED initialized on GPIO %d", LED_PIN);

    // Start with fast blink to indicate startup
    set_led_pattern(1);

    return ESP_OK;
}

// ============================================================================
// SYSTEM INITIALIZATION FUNCTIONS
// ============================================================================

/**
 * Initialize NVS (Non-Volatile Storage) system
 *
 * NVS is ESP32's key-value storage system in flash memory for persistent data.
 * It's used to store device configuration, WiFi credentials, feeding history, etc.
 *
 * NVS handles flash wear leveling and provides atomic operations for data integrity.
 *
 * @return ESP_OK on success, ESP_ERR_* on failure
 *
 * External functions used:
 * - nvs_flash_init(): Initialize NVS partition in flash memory
 *   Returns ESP_OK if successful
 *   Returns ESP_ERR_NVS_NO_FREE_PAGES if flash is full
 *   Returns ESP_ERR_NVS_NEW_VERSION_FOUND if NVS version changed
 * - nvs_flash_erase(): Erase entire NVS partition (factory reset)
 *   Used when NVS is corrupted or version incompatible
 * - ESP_ERROR_CHECK(expr): Abort program if expression returns error
 */
static esp_err_t init_nvs(void)
{
    // Attempt to initialize NVS partition
    esp_err_t ret = nvs_flash_init();

    // Handle special cases that require NVS partition erasure
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        // NVS partition is full or version incompatible - erase and retry
        ESP_ERROR_CHECK(nvs_flash_erase());  // Erase entire NVS partition
        ret = nvs_flash_init();              // Reinitialize after erasure
    }

    return ret;
}

/**
 * WiFi event handler
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        ESP_LOGI(TAG, "WiFi disconnected, trying to reconnect...");
        esp_wifi_connect();
        xEventGroupClearBits(app_event_group, WIFI_CONNECTED_BIT);
        set_led_pattern(2); // Slow blink - WiFi connecting
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        xEventGroupSetBits(app_event_group, WIFI_CONNECTED_BIT);
        set_led_pattern(3); // Solid ON - WiFi connected
    }
}

/**
 * Initialize system components
 */
static esp_err_t init_system_components(void)
{
    esp_err_t ret = ESP_OK;

    // Initialize event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Register WiFi event handler
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL));

    // Initialize configuration system
    ret = config_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize configuration: %s", esp_err_to_name(ret));
        return ret;
    }
    xEventGroupSetBits(app_event_group, CONFIG_LOADED_BIT);

    // Load application configuration
    static app_config_t app_config;  // Use static to avoid stack corruption
    ret = app_config_load(&app_config);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to load app config, using defaults");
        app_config_set_defaults(&app_config);
        app_config_save(&app_config);
    }

    // Initialize motor control (API changed - no parameters needed)
    ret = motor_control_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize motor control: %s", esp_err_to_name(ret));
        return ret;
    }
    xEventGroupSetBits(app_event_group, MOTOR_READY_BIT);

    // Initialize feeding controller
    ret = feeding_controller_init(&app_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize feeding controller: %s", esp_err_to_name(ret));
        return ret;
    }

    // Initialize scheduler
    ret = scheduler_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize scheduler: %s", esp_err_to_name(ret));
        return ret;
    }
    xEventGroupSetBits(app_event_group, SCHEDULER_READY_BIT);

    // Initialize WiFi manager
    set_led_pattern(2); // Slow blink - WiFi connecting
    ret = wifi_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi manager: %s", esp_err_to_name(ret));
        set_led_pattern(4); // Double blink - Error
        return ret;
    }

    // Start WiFi connection using configured credentials
    ESP_LOGI(TAG, "Starting WiFi connection...");
    ESP_LOGI(TAG, "WiFi SSID: '%s'", app_config.wifi_config.ssid);
    ESP_LOGI(TAG, "WiFi Password: '%s'", app_config.wifi_config.password);
    ret = wifi_manager_connect(app_config.wifi_config.ssid, app_config.wifi_config.password);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start WiFi connection: %s", esp_err_to_name(ret));
        set_led_pattern(4); // Double blink - Error
        return ret;
    }

    // Initialize Bluetooth manager
    ret = bluetooth_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth manager: %s", esp_err_to_name(ret));
        set_led_pattern(4); // Double blink - Error
        return ret;
    }

    // Start Bluetooth service (temporarily disabled for testing)
    ESP_LOGI(TAG, "Skipping Bluetooth initialization for testing");
    /*
    ret = bluetooth_manager_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start Bluetooth service: %s", esp_err_to_name(ret));
        return ret;
    }

    // Initialize Bluetooth commands
    ret = bt_commands_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth commands: %s", esp_err_to_name(ret));
        return ret;
    }
    */

    return ESP_OK;
}

/**
 * Start network services
 */
static esp_err_t start_network_services(void)
{
    esp_err_t ret = ESP_OK;

    // Wait for WiFi connection
    EventBits_t bits = xEventGroupWaitBits(app_event_group,
                                          WIFI_CONNECTED_BIT,
                                          pdFALSE,
                                          pdFALSE,
                                          portMAX_DELAY);

    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "WiFi connected, starting web server...");
        
        // Start web server
        ret = web_server_start();
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to start web server: %s", esp_err_to_name(ret));
            return ret;
        }
        
        ESP_LOGI(TAG, "Web server started successfully");
    }

    return ret;
}

/**
 * Main application task
 */
static void app_main_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Cat Feeder application starting...");

    // Wait for all components to be ready
    EventBits_t bits = xEventGroupWaitBits(app_event_group,
                                          CONFIG_LOADED_BIT | MOTOR_READY_BIT | SCHEDULER_READY_BIT,
                                          pdFALSE,
                                          pdTRUE,
                                          portMAX_DELAY);

    if ((bits & (CONFIG_LOADED_BIT | MOTOR_READY_BIT | SCHEDULER_READY_BIT)) == 
        (CONFIG_LOADED_BIT | MOTOR_READY_BIT | SCHEDULER_READY_BIT)) {
        
        ESP_LOGI(TAG, "All components initialized successfully");
        
        // Start network services
        start_network_services();
        
        // Start scheduler
        scheduler_start();
        
        ESP_LOGI(TAG, "Cat Feeder system is ready!");
        
        // Main application loop
        while (1) {
            // System status monitoring
            feeding_controller_status_t status;
            feeding_controller_get_status(&status);
            
            // Log system status every 30 seconds
            ESP_LOGI(TAG, "System Status - Feedings today: %u, Last feeding: %lld, Motor status: %s",
                    (unsigned int)status.feedings_today,
                    status.last_feeding_time,
                    status.motor_ready ? "Ready" : "Busy");
            
            vTaskDelay(pdMS_TO_TICKS(30000)); // 30 seconds
        }
    } else {
        ESP_LOGE(TAG, "Failed to initialize all components");
    }

    vTaskDelete(NULL);
}

/**
 * Application entry point
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32 Cat Feeder Firmware v1.0");
    ESP_LOGI(TAG, "Built on %s %s", __DATE__, __TIME__);

    // Create event group
    app_event_group = xEventGroupCreate();
    if (app_event_group == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return;
    }

    // Initialize LED first for status indication
    esp_err_t ret = init_led();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize LED: %s", esp_err_to_name(ret));
        return;
    }

    // Initialize NVS
    ESP_ERROR_CHECK(init_nvs());

    // Initialize system components
    ret = init_system_components();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize system components: %s", esp_err_to_name(ret));
        return;
    }

    // Create main application task
    xTaskCreate(app_main_task, "app_main_task", 4096, NULL, 5, NULL);

    ESP_LOGI(TAG, "Application initialization complete");
}
