#include "web_server.h"
#include "esp_http_server.h"
#include "esp_log.h"
#include "version.h"
#include <stdio.h>

static const char *TAG = "web_server";
static httpd_handle_t server = NULL;

static esp_err_t root_get_handler(httpd_req_t *req)
{
    const char* resp_str = "<!DOCTYPE html>"
                          "<html><head><title>Cat Feeder ESP32</title></head>"
                          "<body><h1>Cat Feeder Control</h1>"
                          "<p>Device is running and ready!</p>"
                          "<p>Use the mobile app to control the feeder.</p>"
                          "</body></html>";
    httpd_resp_send(req, resp_str, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static esp_err_t status_get_handler(httpd_req_t *req)
{
    char resp_str[256];
    snprintf(resp_str, sizeof(resp_str),
        "{"
        "\"status\":\"ok\","
        "\"device\":\"CatFeeder_ESP32\","
        "\"firmware_version\":\"%s\","
        "\"version_major\":%d,"
        "\"version_minor\":%d,"
        "\"version_patch\":%d,"
        "\"version_build\":%d"
        "}",
        get_version_string(),
        get_version_major(),
        get_version_minor(),
        get_version_patch(),
        get_version_build()
    );

    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, resp_str, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static const httpd_uri_t root = {
    .uri       = "/",
    .method    = HTTP_GET,
    .handler   = root_get_handler,
    .user_ctx  = NULL
};

static const httpd_uri_t status = {
    .uri       = "/status",
    .method    = HTTP_GET,
    .handler   = status_get_handler,
    .user_ctx  = NULL
};

esp_err_t web_server_start(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.lru_purge_enable = true;

    ESP_LOGI(TAG, "Starting server on port: '%d'", config.server_port);
    if (httpd_start(&server, &config) == ESP_OK) {
        ESP_LOGI(TAG, "Registering URI handlers");
        httpd_register_uri_handler(server, &root);
        httpd_register_uri_handler(server, &status);
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Error starting server!");
    return ESP_FAIL;
}

esp_err_t web_server_stop(void)
{
    if (server) {
        httpd_stop(server);
        server = NULL;
    }
    return ESP_OK;
}
