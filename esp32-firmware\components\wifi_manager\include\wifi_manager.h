/**
 * WiFi Manager Component
 * 
 * Manages WiFi connectivity and configuration
 */

#pragma once

#include "esp_err.h"
#include "esp_wifi.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * WiFi connection status
 */
typedef enum {
    WIFI_STATUS_DISCONNECTED,       ///< Not connected
    WIFI_STATUS_CONNECTING,         ///< Attempting to connect
    WIFI_STATUS_CONNECTED,          ///< Connected successfully
    WIFI_STATUS_FAILED,             ///< Connection failed
    WIFI_STATUS_AP_MODE             ///< Access Point mode
} wifi_status_t;

/**
 * WiFi configuration
 */
typedef struct {
    char ssid[32];                  ///< Network SSID
    char password[64];              ///< Network password
    bool auto_connect;              ///< Auto-connect on startup
    uint32_t timeout_ms;            ///< Connection timeout
} wifi_config_data_t;

/**
 * WiFi status information
 */
typedef struct {
    wifi_status_t status;           ///< Current status
    char connected_ssid[32];        ///< Currently connected SSID
    int8_t rssi;                    ///< Signal strength
    uint32_t ip_address;            ///< IP address (network byte order)
    uint32_t gateway;               ///< Gateway address
    uint32_t netmask;               ///< Network mask
} wifi_status_info_t;

/**
 * Initialize WiFi manager
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_init(void);

/**
 * Deinitialize WiFi manager
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_deinit(void);

/**
 * Connect to WiFi network
 * 
 * @param ssid Network SSID
 * @param password Network password
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_connect(const char *ssid, const char *password);

/**
 * Disconnect from WiFi network
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_disconnect(void);

/**
 * Start Access Point mode
 * 
 * @param ssid AP SSID
 * @param password AP password (NULL for open network)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_start_ap(const char *ssid, const char *password);

/**
 * Stop Access Point mode
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_stop_ap(void);

/**
 * Get WiFi status
 * 
 * @param status Pointer to store status information
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_get_status(wifi_status_info_t *status);

/**
 * Scan for available networks
 * 
 * @param ap_records Array to store scan results
 * @param max_records Maximum number of records
 * @param num_found Pointer to store number of networks found
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_scan(wifi_ap_record_t *ap_records, uint16_t max_records, uint16_t *num_found);

/**
 * Save WiFi configuration
 * 
 * @param config Configuration to save
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_save_config(const wifi_config_data_t *config);

/**
 * Load WiFi configuration
 * 
 * @param config Pointer to store configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_load_config(wifi_config_data_t *config);

/**
 * Check if WiFi is connected
 * 
 * @return true if connected, false otherwise
 */
bool wifi_manager_is_connected(void);

/**
 * Get IP address as string
 * 
 * @param ip_str Buffer to store IP string (minimum 16 bytes)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t wifi_manager_get_ip_string(char *ip_str);

#ifdef __cplusplus
}
#endif
