using CatFeeder.Core.Services;
using Microsoft.Extensions.Logging;
using System.Text;

namespace CatFeeder.Mobile.Views
{
    public partial class StartupPage : ContentPage
    {
        private readonly IPermissionService _permissionService;
        private bool _hasNavigated = false;

        public StartupPage()
        {
            InitializeComponent();

            // Set version information
            SetVersionInfo();

            // Create a basic permission service for now
            _permissionService = new PermissionService();

            // Start the initialization process
            _ = InitializeAppAsync();
        }

        private void SetVersionInfo()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                var appVersion = AppInfo.VersionString;
                var buildNumber = AppInfo.BuildString;

                VersionLabel.Text = $"Studio 311 LP - v{appVersion} (Build {buildNumber})";
            }
            catch (Exception)
            {
                VersionLabel.Text = "Studio 311 LP - Version Info Unavailable";
            }
        }

        private async Task InitializeAppAsync()
        {
            try
            {
                await Task.Delay(1000); // Show loading for a moment

                LoadingLabel.Text = "Checking permissions...";
                await Task.Delay(500);

                await CheckAndRequestPermissionsAsync();
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"Initialization failed: {ex.Message}");
            }
        }

        private async Task CheckAndRequestPermissionsAsync()
        {
            try
            {
                // For now, simulate permission checking since we don't have platform-specific implementation
                LoadingLabel.Text = "Checking Bluetooth permissions...";
                await Task.Delay(500);

                LoadingLabel.Text = "Checking location permissions...";
                await Task.Delay(500);

                // Show permission section
                LoadingSection.IsVisible = false;
                PermissionSection.IsVisible = true;

                // Add permission status items
                AddPermissionStatusItem("Bluetooth", "Required for ESP32 connection", true);
                AddPermissionStatusItem("Location", "Required for device scanning", true);
                AddPermissionStatusItem("Network Access", "For WiFi status checking", true);

                // For demo purposes, show success after a delay
                await Task.Delay(1000);
                await ShowSuccessAsync();
            }
            catch (Exception ex)
            {
                await ShowErrorAsync($"Permission check failed: {ex.Message}");
            }
        }

        private void AddPermissionStatusItem(string name, string description, bool granted)
        {
            var container = new StackLayout
            {
                Orientation = StackOrientation.Horizontal,
                Spacing = 10
            };

            var statusIcon = new Label
            {
                Text = granted ? "✅" : "❌",
                FontSize = 16,
                VerticalOptions = LayoutOptions.Center
            };

            var textContainer = new StackLayout
            {
                Spacing = 2,
                HorizontalOptions = LayoutOptions.Fill
            };

            var nameLabel = new Label
            {
                Text = name,
                FontSize = 14,
                FontAttributes = FontAttributes.Bold,
                TextColor = granted ? Color.FromArgb("#4CAF50") : Color.FromArgb("#FF6B6B")
            };

            var descLabel = new Label
            {
                Text = description,
                FontSize = 12,
                TextColor = Color.FromArgb("#666")
            };

            textContainer.Children.Add(nameLabel);
            textContainer.Children.Add(descLabel);

            container.Children.Add(statusIcon);
            container.Children.Add(textContainer);

            PermissionList.Children.Add(container);
        }

        private async Task ShowSuccessAsync()
        {
            PermissionSection.IsVisible = false;
            SuccessSection.IsVisible = true;
            ContinueButton.IsVisible = true;

            // Auto-continue after 3 seconds
            await Task.Delay(3000);
            if (!_hasNavigated)
            {
                await NavigateToMainAppAsync();
            }
        }

        private Task ShowErrorAsync(string message)
        {
            LoadingSection.IsVisible = false;
            ErrorSection.IsVisible = true;
            ErrorLabel.Text = message;

            RequestPermissionsButton.IsVisible = true;
            OpenSettingsButton.IsVisible = true;
            RetryButton.IsVisible = true;

            return Task.CompletedTask;
        }

        private async void OnRequestPermissionsClicked(object sender, EventArgs e)
        {
            try
            {
                // Reset UI
                ErrorSection.IsVisible = false;
                RequestPermissionsButton.IsVisible = false;
                OpenSettingsButton.IsVisible = false;
                RetryButton.IsVisible = false;

                LoadingSection.IsVisible = true;
                LoadingLabel.Text = "Requesting permissions...";

                // Simulate permission request
                await Task.Delay(2000);

                await CheckAndRequestPermissionsAsync();
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to request permissions: {ex.Message}", "OK");
            }
        }

        private async void OnOpenSettingsClicked(object sender, EventArgs e)
        {
            try
            {
                bool opened = await _permissionService.OpenAppSettingsAsync();
                if (!opened)
                {
                    await DisplayAlert("Settings",
                        "Please manually open Settings > Apps > Smart Cat Feeder > Permissions and enable Bluetooth and Location permissions.",
                        "OK");
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to open settings: {ex.Message}", "OK");
            }
        }

        private async void OnContinueClicked(object sender, EventArgs e)
        {
            await NavigateToMainAppAsync();
        }

        private async void OnRetryClicked(object sender, EventArgs e)
        {
            // Reset UI and check again
            ErrorSection.IsVisible = false;
            PermissionSection.IsVisible = false;
            SuccessSection.IsVisible = false;
            RequestPermissionsButton.IsVisible = false;
            OpenSettingsButton.IsVisible = false;
            RetryButton.IsVisible = false;
            ContinueButton.IsVisible = false;

            PermissionList.Children.Clear();

            LoadingSection.IsVisible = true;
            LoadingLabel.Text = "Checking permissions...";

            await Task.Delay(500);
            await CheckAndRequestPermissionsAsync();
        }

        private async Task NavigateToMainAppAsync()
        {
            if (_hasNavigated) return;

            _hasNavigated = true;

            try
            {
                // Navigate to the main app page
                await Shell.Current.GoToAsync("//MainPage");
            }
            catch (Exception ex)
            {
                // Log the navigation error
                Microsoft.Extensions.Logging.ILogger logger =
                    Application.Current?.Handler?.MauiContext?.Services?.GetService<Microsoft.Extensions.Logging.ILogger<StartupPage>>()
                    ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<StartupPage>.Instance;

                logger.LogError(ex, "Failed to navigate to MainPage: {Message}", ex.Message);

                // If navigation fails, try creating a new main page
                if (Application.Current != null)
                {
                    Application.Current.MainPage = new AppShell();
                }

                // Also log to file for troubleshooting
                await LogToFileAsync("NAVIGATION_ERROR", $"Failed to navigate to MainPage: {ex.Message}\n{ex.StackTrace}");
            }
        }

        private async Task LogToFileAsync(string level, string message)
        {
            try
            {
                // Try to get the logging service
                var loggingService = Application.Current?.Handler?.MauiContext?.Services?.GetService<ILoggingService>();

                if (loggingService != null)
                {
                    switch (level)
                    {
                        case "DEBUG":
                            loggingService.LogDebug(message);
                            break;
                        case "INFO":
                            loggingService.LogInfo(message);
                            break;
                        case "WARNING":
                            loggingService.LogWarning(message);
                            break;
                        case "ERROR":
                            loggingService.LogError(message);
                            break;
                        case "CRITICAL":
                            loggingService.LogCritical(message);
                            break;
                        default:
                            loggingService.LogInfo(message);
                            break;
                    }
                    return;
                }

                // Fallback to direct file logging if service is not available
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
                var logDirectory = Path.Combine(appDataPath, "Studio311LP", "CatFeeder", "Logs");
                var logFilePath = Path.Combine(logDirectory, "catfeeder_log.txt");

                // Create directory if it doesn't exist
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // Create log file if it doesn't exist
                if (!File.Exists(logFilePath))
                {
                    using var stream = File.Create(logFilePath);
                    var header = Encoding.UTF8.GetBytes($"=== STUDIO 311 LP - SMART CAT FEEDER LOG ===\r\nStarted: {DateTime.Now:yyyy-MM-dd HH:mm:ss}\r\n\r\n");
                    await stream.WriteAsync(header.AsMemory());
                }

                // Append log entry
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var logEntry = $"[{timestamp}] [{level}] {message}\r\n";
                await File.AppendAllTextAsync(logFilePath, logEntry);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to log to file: {ex.Message}");
            }
        }
    }
}


