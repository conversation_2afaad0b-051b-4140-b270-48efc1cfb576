using System.Collections.ObjectModel;
using CatFeeder.Core.Services;
using CatFeeder.Models;

namespace CatFeeder.Mobile.Views;

public partial class BluetoothTestPage : ContentPage
{
    private readonly IBluetoothService _bluetoothService;
    private BluetoothDeviceInfo? _selectedDevice;
    
    public ObservableCollection<BluetoothDeviceInfo> DiscoveredDevices { get; } = new();

    public BluetoothTestPage()
    {
        InitializeComponent();
        
        // For now, use the mock implementation
        _bluetoothService = new BluetoothService();
        
        // Subscribe to events
        _bluetoothService.ConnectionStatusChanged += OnConnectionStatusChanged;
        _bluetoothService.CommandResponseReceived += OnCommandResponseReceived;
        
        BindingContext = this;
        
        UpdateUI();
    }

    private async void OnScanClicked(object sender, EventArgs e)
    {
        try
        {
            ScanButton.IsEnabled = false;
            ScanButton.Text = "Scanning...";
            
            LogMessage("Starting device scan...");
            
            var devices = await _bluetoothService.ScanForDevicesAsync(TimeSpan.FromSeconds(10));
            
            DiscoveredDevices.Clear();
            foreach (var device in devices)
            {
                DiscoveredDevices.Add(device);
                LogMessage($"Found device: {device.Name} ({device.Address})");
            }
            
            LogMessage($"Scan completed. Found {devices.Count} device(s).");
        }
        catch (Exception ex)
        {
            LogMessage($"Scan failed: {ex.Message}");
            await DisplayAlert("Error", $"Failed to scan for devices: {ex.Message}", "OK");
        }
        finally
        {
            ScanButton.IsEnabled = true;
            ScanButton.Text = "Scan for Devices";
        }
    }

    private void OnDeviceSelected(object sender, SelectionChangedEventArgs e)
    {
        if (e.CurrentSelection.FirstOrDefault() is BluetoothDeviceInfo device)
        {
            _selectedDevice = device;
            DeviceLabel.Text = $"Selected: {device.Name}";
            ConnectButton.IsEnabled = true;
            LogMessage($"Selected device: {device.Name} ({device.Address})");
        }
    }

    private async void OnConnectClicked(object sender, EventArgs e)
    {
        if (_selectedDevice == null)
        {
            await DisplayAlert("Error", "Please select a device first", "OK");
            return;
        }

        try
        {
            ConnectButton.IsEnabled = false;
            ConnectButton.Text = "Connecting...";
            
            LogMessage($"Connecting to {_selectedDevice.Name}...");
            
            var pin = string.IsNullOrWhiteSpace(PinEntry.Text) ? "1234" : PinEntry.Text;
            var success = await _bluetoothService.ConnectAsync(_selectedDevice, pin);
            
            if (success)
            {
                LogMessage("Connected successfully!");
                await DisplayAlert("Success", "Connected to device", "OK");
            }
            else
            {
                LogMessage("Connection failed!");
                await DisplayAlert("Error", "Failed to connect to device", "OK");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Connection error: {ex.Message}");
            await DisplayAlert("Error", $"Connection failed: {ex.Message}", "OK");
        }
        finally
        {
            ConnectButton.Text = "Connect";
            UpdateUI();
        }
    }

    private async void OnDisconnectClicked(object sender, EventArgs e)
    {
        try
        {
            DisconnectButton.IsEnabled = false;
            LogMessage("Disconnecting...");
            
            var success = await _bluetoothService.DisconnectAsync();
            
            if (success)
            {
                LogMessage("Disconnected successfully!");
            }
            else
            {
                LogMessage("Disconnection failed!");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Disconnection error: {ex.Message}");
            await DisplayAlert("Error", $"Disconnection failed: {ex.Message}", "OK");
        }
        finally
        {
            UpdateUI();
        }
    }

    private async void OnPingClicked(object sender, EventArgs e)
    {
        try
        {
            PingButton.IsEnabled = false;
            LogMessage("Sending PING command...");
            
            var success = await _bluetoothService.PingAsync();
            
            if (success)
            {
                LogMessage("PING successful - device is responding!");
            }
            else
            {
                LogMessage("PING failed - device not responding");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"PING error: {ex.Message}");
        }
        finally
        {
            PingButton.IsEnabled = true;
        }
    }

    private async void OnGetStatusClicked(object sender, EventArgs e)
    {
        try
        {
            StatusButton.IsEnabled = false;
            LogMessage("Getting device status...");
            
            var status = await _bluetoothService.GetStatusAsync();
            
            LogMessage($"Device Status:");
            LogMessage($"  WiFi: {(status.ConnectedSsid != "" ? "Connected" : "Disconnected")}");
            LogMessage($"  Motor Ready: {status.MotorReady}");
            LogMessage($"  Feedings Today: {status.FeedingsToday}");
            LogMessage($"  Food Level: {status.FoodLevelPercent:F1}%");
        }
        catch (Exception ex)
        {
            LogMessage($"Get status error: {ex.Message}");
        }
        finally
        {
            StatusButton.IsEnabled = true;
        }
    }

    private async void OnGetInfoClicked(object sender, EventArgs e)
    {
        try
        {
            InfoButton.IsEnabled = false;
            LogMessage("Getting device info...");
            
            var info = await _bluetoothService.GetDeviceInfoAsync();
            
            LogMessage($"Device Info:");
            LogMessage($"  Chip: {info.ChipModel}");
            LogMessage($"  Firmware: {info.FirmwareVersion}");
            LogMessage($"  Free Heap: {info.FreeHeap:N0} bytes");
            LogMessage($"  Uptime: {info.Uptime}");
        }
        catch (Exception ex)
        {
            LogMessage($"Get info error: {ex.Message}");
        }
        finally
        {
            InfoButton.IsEnabled = true;
        }
    }

    private async void OnTestLedClicked(object sender, EventArgs e)
    {
        try
        {
            TestLedButton.IsEnabled = false;
            LogMessage("Testing LED...");
            
            var command = new DeviceCommand { Command = "TEST_LED" };
            var response = await _bluetoothService.SendCommandAsync(command);
            
            if (response.Success)
            {
                LogMessage("LED test completed successfully!");
            }
            else
            {
                LogMessage($"LED test failed: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"LED test error: {ex.Message}");
        }
        finally
        {
            TestLedButton.IsEnabled = true;
        }
    }

    private async void OnSendCommandClicked(object sender, EventArgs e)
    {
        var commandText = CustomCommandEntry.Text?.Trim();
        if (string.IsNullOrEmpty(commandText))
        {
            await DisplayAlert("Error", "Please enter a command", "OK");
            return;
        }

        try
        {
            SendCommandButton.IsEnabled = false;
            LogMessage($"Sending custom command: {commandText}");
            
            // Parse command and parameters
            var parts = commandText.Split(':', 2);
            var command = new DeviceCommand 
            { 
                Command = parts[0],
                Parameters = parts.Length > 1 ? parts[1] : ""
            };
            
            var response = await _bluetoothService.SendCommandAsync(command);
            
            if (response.Success)
            {
                LogMessage($"Response: {response.Response}");
            }
            else
            {
                LogMessage($"Command failed: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Command error: {ex.Message}");
        }
        finally
        {
            SendCommandButton.IsEnabled = true;
        }
    }

    private void OnClearLogClicked(object sender, EventArgs e)
    {
        LogLabel.Text = "Log cleared.\n";
    }

    private void OnConnectionStatusChanged(object? sender, BluetoothEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            LogMessage($"Connection status: {e.Status} - {e.Message}");
            UpdateUI();
        });
    }

    private void OnCommandResponseReceived(object? sender, BluetoothCommandEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            LogMessage($"Command response: {e.Command} -> {e.Response} ({e.Duration.TotalMilliseconds:F0}ms)");
        });
    }

    private void UpdateUI()
    {
        var isConnected = _bluetoothService.ConnectionStatus == BluetoothConnectionStatus.Connected;
        
        StatusLabel.Text = _bluetoothService.ConnectionStatus.ToString();
        
        if (_bluetoothService.ConnectedDevice != null)
        {
            DeviceLabel.Text = $"Connected: {_bluetoothService.ConnectedDevice.Name}";
        }
        else if (_selectedDevice != null)
        {
            DeviceLabel.Text = $"Selected: {_selectedDevice.Name}";
        }
        else
        {
            DeviceLabel.Text = "No device selected";
        }
        
        ConnectButton.IsEnabled = !isConnected && _selectedDevice != null;
        DisconnectButton.IsEnabled = isConnected;
        
        PingButton.IsEnabled = isConnected;
        StatusButton.IsEnabled = isConnected;
        InfoButton.IsEnabled = isConnected;
        TestLedButton.IsEnabled = isConnected;
        SendCommandButton.IsEnabled = isConnected;
    }

    private void LogMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        LogLabel.Text += $"[{timestamp}] {message}\n";
    }
}
