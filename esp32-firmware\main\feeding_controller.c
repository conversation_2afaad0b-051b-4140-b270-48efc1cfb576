/**
 * @file feeding_controller.c
 * @brief Feeding controller implementation for CatFeeder ESP32
 * <AUTHOR> 311 LP
 * 
 * This module manages the feeding process, including motor control,
 * portion measurement, and feeding history tracking.
 */

#include "feeding_controller.h"
#include "motor_control.h"
#include "feeding_history.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char* TAG = "feeding_controller";

// Global feeding controller state
static feeding_controller_status_t g_feeding_status = {
    .motor_ready = true,
    .last_feeding_time = 0,
    .feedings_today = 0,
    .food_level_percent = 100.0f,
    .last_result = FEEDING_RESULT_SUCCESS,
    .manual_button_pressed = false
};

/**
 * Initialize the feeding controller
 */
esp_err_t feeding_controller_init(const app_config_t *config)
{
    ESP_LOGI(TAG, "Initializing feeding controller...");
    
    // Initialize motor control
    esp_err_t ret = motor_control_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize motor control: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // Initialize feeding history
    ret = feeding_history_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize feeding history: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "Feeding controller initialized successfully");
    return ESP_OK;
}

/**
 * Execute a feeding operation
 */
esp_err_t feeding_controller_feed(uint32_t portion_size_grams)
{
    if (!g_feeding_status.motor_ready) {
        ESP_LOGW(TAG, "Motor not ready for feeding");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Starting feeding operation: %" PRIu32 " grams", portion_size_grams);

    g_feeding_status.motor_ready = false;
    
    esp_err_t ret = ESP_OK;
    
    // Calculate feeding duration based on portion size
    // Assuming 1 gram per 100ms for DC motor (adjust as needed)
    uint32_t feeding_duration_ms = portion_size_grams * 100;
    
    // Start motor
    ret = motor_control_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start motor: %s", esp_err_to_name(ret));
        g_feeding_status.motor_ready = true;
        g_feeding_status.last_result = FEEDING_RESULT_MOTOR_ERROR;
        return ret;
    }

    // Wait for feeding duration
    vTaskDelay(pdMS_TO_TICKS(feeding_duration_ms));

    // Stop motor
    ret = motor_control_stop();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop motor: %s", esp_err_to_name(ret));
        g_feeding_status.last_result = FEEDING_RESULT_MOTOR_ERROR;
    } else {
        g_feeding_status.last_result = FEEDING_RESULT_SUCCESS;
    }

    // Update feeding status
    g_feeding_status.motor_ready = true;
    g_feeding_status.last_feeding_time = esp_timer_get_time() / 1000000; // Convert to seconds
    g_feeding_status.feedings_today++;
    
    // Record feeding in history
    feeding_entry_t entry = {
        .id = 0, // Will be assigned by feeding_history_add_entry
        .timestamp = g_feeding_status.last_feeding_time,
        .portion_size = portion_size_grams,
        .actual_portion = portion_size_grams,
        .result = g_feeding_status.last_result,
        .trigger = FEEDING_TRIGGER_MANUAL,
        .duration_ms = feeding_duration_ms,
        .food_level_before = g_feeding_status.food_level_percent,
        .food_level_after = g_feeding_status.food_level_percent,
        .user = "Manual",
        .notes = ""
    };
    
    esp_err_t history_ret = feeding_history_add_entry(&entry);
    if (history_ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to record feeding in history: %s", esp_err_to_name(history_ret));
    }
    
    ESP_LOGI(TAG, "Feeding operation completed successfully");
    return ret;
}

/**
 * Get current feeding controller status
 */
esp_err_t feeding_controller_get_status(feeding_controller_status_t* status)
{
    if (status == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    *status = g_feeding_status;
    return ESP_OK;
}

/**
 * Set motor type
 */
esp_err_t feeding_controller_set_motor_type(motor_type_t motor_type)
{
    if (!g_feeding_status.motor_ready) {
        ESP_LOGW(TAG, "Cannot change motor type while motor is busy");
        return ESP_ERR_INVALID_STATE;
    }
    
    // Configure motor control for the new type
    esp_err_t ret = motor_control_set_type(motor_type);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set motor type: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "Motor type set to: %s", 
             motor_type == MOTOR_TYPE_STEPPER ? "Stepper" : "DC");
    
    return ESP_OK;
}

/**
 * Update food level (from sensor or manual input)
 */
esp_err_t feeding_controller_set_food_level(uint8_t level_percent)
{
    if (level_percent > 100) {
        return ESP_ERR_INVALID_ARG;
    }
    
    g_feeding_status.food_level_percent = level_percent;
    ESP_LOGI(TAG, "Food level updated to: %d%%", level_percent);
    
    return ESP_OK;
}

/**
 * Check if feeding is currently in progress
 */
bool feeding_controller_is_feeding(void)
{
    return !g_feeding_status.motor_ready;
}
