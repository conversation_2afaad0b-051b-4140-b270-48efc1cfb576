#include "config.h"
#include "nvs_flash.h"
#include "nvs.h"
#include "esp_log.h"
#include <string.h>

static const char *TAG = "config";
static const char *STORAGE_NAMESPACE = "catfeeder";

esp_err_t config_init(void)
{
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    ESP_LOGI(TAG, "Config storage initialized");
    return ESP_OK;
}

esp_err_t config_set_motor_type(motor_type_t motor_type)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    err = nvs_open(STORAGE_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) return err;

    err = nvs_set_u8(nvs_handle, "motor_type", (uint8_t)motor_type);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    
    ESP_LOGI(TAG, "Motor type set to: %d", motor_type);
    return err;
}

motor_type_t config_get_motor_type(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;
    uint8_t motor_type = MOTOR_TYPE_STEPPER; // default

    err = nvs_open(STORAGE_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) return motor_type;

    size_t required_size = sizeof(uint8_t);
    err = nvs_get_u8(nvs_handle, "motor_type", &motor_type);
    nvs_close(nvs_handle);

    return (motor_type_t)motor_type;
}

esp_err_t config_set_wifi_credentials(const char* ssid, const char* password)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    err = nvs_open(STORAGE_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) return err;

    err = nvs_set_str(nvs_handle, "wifi_ssid", ssid);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_set_str(nvs_handle, "wifi_pass", password);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_commit(nvs_handle);
    nvs_close(nvs_handle);
    
    ESP_LOGI(TAG, "WiFi credentials saved for SSID: %s", ssid);
    return err;
}

esp_err_t config_get_wifi_credentials(char* ssid, size_t ssid_len, char* password, size_t pass_len)
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    err = nvs_open(STORAGE_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) return err;

    err = nvs_get_str(nvs_handle, "wifi_ssid", ssid, &ssid_len);
    if (err != ESP_OK) {
        nvs_close(nvs_handle);
        return err;
    }

    err = nvs_get_str(nvs_handle, "wifi_pass", password, &pass_len);
    nvs_close(nvs_handle);

    return err;
}
