# PowerShell script to build and deploy CatFeeder Mobile App
# Studio 311 LP - CatFeeder Mobile App Build System

param(
    [string]$Configuration = "Release",
    [string]$Platform = "android",
    [switch]$Deploy = $false,
    [switch]$SkipVersionIncrement = $false
)

$ErrorActionPreference = "Stop"

Write-Host "=== Studio 311 LP - CatFeeder Mobile App Build ===" -ForegroundColor Cyan

# Get script directory and project root
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

if (-not $SkipVersionIncrement) {
    Write-Host "Incrementing mobile app version..." -ForegroundColor Yellow
    Set-Location $ProjectRoot
    
    & powershell -ExecutionPolicy Bypass -File "increment-version.ps1" -Component mobile_app
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to increment version!"
        exit 1
    }
}

# Return to mobile app directory
Set-Location $ScriptDir

Write-Host "Building mobile app for $Platform in $Configuration mode..." -ForegroundColor Yellow

try {
    # Clean previous builds
    Write-Host "Cleaning previous builds..." -ForegroundColor Gray
    if (Test-Path "CatFeeder.Mobile/bin") {
        Remove-Item "CatFeeder.Mobile/bin" -Recurse -Force
    }
    if (Test-Path "CatFeeder.Mobile/obj") {
        Remove-Item "CatFeeder.Mobile/obj" -Recurse -Force
    }

    # Build the application
    switch ($Platform.ToLower()) {
        "android" {
            Write-Host "Building Android APK..." -ForegroundColor Green
            dotnet build CatFeeder.Mobile/CatFeeder.Mobile.csproj -c $Configuration -f net9.0-android
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Creating Android package..." -ForegroundColor Green
                dotnet publish CatFeeder.Mobile/CatFeeder.Mobile.csproj -c $Configuration -f net9.0-android
                
                if ($Deploy -and $LASTEXITCODE -eq 0) {
                    Write-Host "Deploying to Android device..." -ForegroundColor Green
                    # Find the APK file
                    $apkPath = Get-ChildItem -Path "CatFeeder.Mobile/bin/$Configuration/net9.0-android/publish" -Filter "*.apk" | Select-Object -First 1
                    if ($apkPath) {
                        Write-Host "Found APK: $($apkPath.FullName)" -ForegroundColor Gray
                        
                        # Install using ADB
                        adb install -r $apkPath.FullName
                        if ($LASTEXITCODE -eq 0) {
                            Write-Host "App deployed successfully!" -ForegroundColor Green
                        } else {
                            Write-Warning "ADB install failed. Try deploying manually."
                        }
                    } else {
                        Write-Warning "APK file not found in publish directory."
                    }
                }
            }
        }
        "ios" {
            Write-Host "Building iOS app..." -ForegroundColor Green
            dotnet build CatFeeder.Mobile/CatFeeder.Mobile.csproj -c $Configuration -f net9.0-ios
        }
        "maccatalyst" {
            Write-Host "Building macOS Catalyst app..." -ForegroundColor Green
            dotnet build CatFeeder.Mobile/CatFeeder.Mobile.csproj -c $Configuration -f net9.0-maccatalyst
        }
        "windows" {
            Write-Host "Building Windows app..." -ForegroundColor Green
            dotnet build CatFeeder.Mobile/CatFeeder.Mobile.csproj -c $Configuration -f net9.0-windows10.0.19041.0
        }
        default {
            Write-Error "Unsupported platform: $Platform. Use android, ios, maccatalyst, or windows."
            exit 1
        }
    }

    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build completed successfully!" -ForegroundColor Green
        
        # Show version information
        $versionPath = Join-Path $ProjectRoot "version.json"
        if (Test-Path $versionPath) {
            $versionData = Get-Content $versionPath | ConvertFrom-Json
            Write-Host "Built version: $($versionData.version)" -ForegroundColor Cyan
        }
    } else {
        Write-Error "Build failed with exit code $LASTEXITCODE"
        exit $LASTEXITCODE
    }
}
catch {
    Write-Error "Build failed with error: $($_.Exception.Message)"
    exit 1
}

Write-Host "Mobile app build process completed!" -ForegroundColor Green
