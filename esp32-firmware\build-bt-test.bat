@echo off
echo ========================================
echo ESP32 Cat Feeder - Bluetooth Test Build
echo ========================================
echo.

:: Check if ESP-IDF is available
idf.py --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: ESP-IDF not found in PATH
    echo Please run this script from ESP-IDF Command Prompt
    pause
    exit /b 1
)

echo ESP-IDF found, building Bluetooth test firmware...
echo.

:: Set target to ESP32
echo [1/4] Setting target to ESP32...
idf.py set-target esp32
if %errorLevel% neq 0 (
    echo ERROR: Failed to set target
    pause
    exit /b 1
)

:: Build the project
echo.
echo [2/4] Building Bluetooth test firmware...
idf.py build
if %errorLevel% neq 0 (
    echo ERROR: Build failed
    echo.
    echo Common issues:
    echo - Missing Bluetooth configuration in sdkconfig
    echo - Component dependencies not found
    echo - Syntax errors in source files
    pause
    exit /b 1
)

:: Check if COM port is provided
if "%1"=="" (
    echo.
    echo [3/4] Build successful! Ready to flash.
    echo.
    echo To flash to your ESP32:
    echo   build-bt-test.bat COM3
    echo.
    echo Or manually:
    echo   idf.py -p COM_PORT flash monitor
    echo.
    pause
    exit /b 0
)

:: Flash and monitor
echo.
echo [3/4] Flashing to %1...
idf.py -p %1 flash
if %errorLevel% neq 0 (
    echo ERROR: Flash failed
    echo Check if:
    echo - ESP32 is connected to %1
    echo - No other programs are using the port
    echo - ESP32 is in download mode (hold BOOT button while pressing RESET)
    pause
    exit /b 1
)

echo.
echo [4/4] Starting monitor...
echo.
echo ========================================
echo Bluetooth Test Firmware Ready!
echo ========================================
echo.
echo Device Name: CatFeeder_ESP32
echo Bluetooth PIN: 1234
echo.
echo Available Commands:
echo - PING
echo - GET_STATUS  
echo - GET_DEVICE_INFO
echo - ECHO:message
echo - SET_DEVICE_NAME:name
echo - GET_TIME
echo - TEST_LED
echo.
echo Press Ctrl+] to exit monitor
echo.
idf.py -p %1 monitor
