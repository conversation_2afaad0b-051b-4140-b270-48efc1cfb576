<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CatFeeder.Mobile.Views.LogViewerPage"
             Title="Log Viewer">
    <Grid RowDefinitions="Auto,*,Auto">
        <StackLayout Grid.Row="0" Orientation="Horizontal" Padding="10" Spacing="10">
            <Button Text="Refresh" Clicked="OnRefreshClicked" HorizontalOptions="Start" />
            <Button Text="Clear Logs" Clicked="OnClearLogsClicked" HorizontalOptions="Start" />
            <Button Text="Share Logs" Clicked="OnShareLogsClicked" HorizontalOptions="Start" />
        </StackLayout>
        
        <ScrollView Grid.Row="1" Padding="10">
            <StackLayout>
                <Label Text="Application Logs" FontSize="16" FontAttributes="Bold" Margin="0,0,0,10" />
                <Frame BackgroundColor="#f5f5f5" Padding="10" BorderColor="#ddd">
                    <Label x:Name="LogContentLabel" 
                           Text="Loading logs..." 
                           FontFamily="Courier" 
                           FontSize="12" 
                           LineBreakMode="WordWrap" />
                </Frame>
            </StackLayout>
        </ScrollView>
        
        <StackLayout Grid.Row="2" Orientation="Horizontal" Padding="10" HorizontalOptions="End">
            <Button Text="Close" Clicked="OnCloseClicked" />
        </StackLayout>
    </Grid>
</ContentPage>