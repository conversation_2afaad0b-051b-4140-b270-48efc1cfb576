/**
 * @file version.c
 * @brief Version information implementation for CatFeeder ESP32 Firmware
 * <AUTHOR> 311 LP
 */

#include "version.h"
#include <stdio.h>
#include <string.h>

// Static buffer for full version info
static char full_version_buffer[256];

const char* get_version_string(void) {
    return VERSION_STRING;
}

const char* get_full_version_info(void) {
    snprintf(full_version_buffer, sizeof(full_version_buffer),
             "%s v%s (%s %s) - %s",
             PRODUCT_NAME,
             VERSION_STRING,
             BUILD_DATE,
             BUILD_TIME,
             COMPANY_NAME);
    return full_version_buffer;
}

uint32_t get_version_int(void) {
    return VERSION_INT;
}
