<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="CatFeeder.Mobile.Views.BluetoothTestPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:models="clr-namespace:CatFeeder.Models;assembly=CatFeeder.Models"
             xmlns:views="clr-namespace:CatFeeder.Mobile.Views"
             Title="Bluetooth Test">
    
    <ScrollView>
        <VerticalStackLayout Spacing="20" Padding="20">
            
            <!-- Header -->
            <Label Text="🐱 Cat Feeder Bluetooth Test" 
                   FontSize="24" 
                   FontAttributes="Bold"
                   HorizontalOptions="Center" />
            
            <!-- Connection Status -->
            <Frame BackgroundColor="LightBlue" Padding="15">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Connection Status" FontAttributes="Bold" />
                    <Label x:Name="StatusLabel" Text="Disconnected" />
                    <Label x:Name="DeviceLabel" Text="No device selected" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- Device Discovery -->
            <Frame BackgroundColor="LightGreen" Padding="15">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Device Discovery" FontAttributes="Bold" />
                    <Button x:Name="ScanButton" 
                            Text="Scan for Devices" 
                            Clicked="OnScanClicked" />
                    <CollectionView x:Name="DevicesCollectionView" 
                                    ItemsSource="{Binding DiscoveredDevices}"
                                    SelectionMode="Single"
                                    SelectionChanged="OnDeviceSelected">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:BluetoothDeviceInfo">
                                <Grid Padding="10" RowDefinitions="Auto,Auto">
                                    <Label Grid.Row="0"
                                           Text="{Binding Name}"
                                           FontAttributes="Bold" />
                                    <Label Grid.Row="1"
                                           Text="{Binding Address}"
                                           FontSize="12"
                                           TextColor="Gray" />
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Connection Controls -->
            <Frame BackgroundColor="LightYellow" Padding="15">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Connection Controls" FontAttributes="Bold" />
                    <Entry x:Name="PinEntry" 
                           Placeholder="Enter PIN (default: 1234)" 
                           Text="1234" />
                    <Button x:Name="ConnectButton" 
                            Text="Connect" 
                            Clicked="OnConnectClicked" 
                            IsEnabled="False" />
                    <Button x:Name="DisconnectButton" 
                            Text="Disconnect" 
                            Clicked="OnDisconnectClicked" 
                            IsEnabled="False" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- Command Testing -->
            <Frame BackgroundColor="LightCoral" Padding="15">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Command Testing" FontAttributes="Bold" />
                    
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <Button Grid.Column="0" 
                                x:Name="PingButton" 
                                Text="PING" 
                                Clicked="OnPingClicked" 
                                IsEnabled="False" />
                        <Button Grid.Column="1" 
                                x:Name="StatusButton" 
                                Text="GET STATUS" 
                                Clicked="OnGetStatusClicked" 
                                IsEnabled="False" />
                    </Grid>
                    
                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <Button Grid.Column="0" 
                                x:Name="InfoButton" 
                                Text="GET INFO" 
                                Clicked="OnGetInfoClicked" 
                                IsEnabled="False" />
                        <Button Grid.Column="1" 
                                x:Name="TestLedButton" 
                                Text="TEST LED" 
                                Clicked="OnTestLedClicked" 
                                IsEnabled="False" />
                    </Grid>
                    
                    <!-- Custom Command -->
                    <Entry x:Name="CustomCommandEntry" 
                           Placeholder="Enter custom command (e.g., ECHO:Hello)" />
                    <Button x:Name="SendCommandButton" 
                            Text="Send Custom Command" 
                            Clicked="OnSendCommandClicked" 
                            IsEnabled="False" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- Response Log -->
            <Frame BackgroundColor="LightGray" Padding="15">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Response Log" FontAttributes="Bold" />
                    <Button Text="Clear Log" Clicked="OnClearLogClicked" />
                    <ScrollView HeightRequest="200">
                        <Label x:Name="LogLabel" 
                               Text="Ready for testing..." 
                               FontFamily="Courier" 
                               FontSize="12" />
                    </ScrollView>
                </VerticalStackLayout>
            </Frame>
            
        </VerticalStackLayout>
    </ScrollView>
    
</ContentPage>
