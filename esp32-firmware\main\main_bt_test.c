/**
 * ESP32 Cat Feeder - Bluetooth Test Version
 * 
 * Enhanced version with Bluetooth communication for mobile app testing
 */

#include <stdio.h>
#include <string.h>
#include <inttypes.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_http_server.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "driver/gpio.h"
#include "esp_timer.h"

// Project includes
#include "bluetooth_manager.h"

static const char *TAG = "CAT_FEEDER_BT_TEST";

// ESP-WROOM-32 built-in LED pin (GPIO 2)
#define LED_PIN GPIO_NUM_2

// WiFi credentials - CHANGE THESE TO YOUR NETWORK
#define WIFI_SSID "TomTom"
#define WIFI_PASS "your_password_here"

// Event group for synchronization
static EventGroupHandle_t status_event_group;

// Event bits
#define WIFI_CONNECTED_BIT  BIT0
#define BT_READY_BIT        BIT1
#define ERROR_BIT           BIT2

// LED control
static esp_timer_handle_t led_timer;
static bool led_state = false;
static int led_pattern = 0; // 0=off, 1=fast, 2=slow, 3=solid, 4=double

static httpd_handle_t server = NULL;

// Forward declarations
esp_err_t bt_commands_basic_init(void);

/**
 * LED control timer callback
 */
static void led_timer_callback(void* arg)
{
    switch (led_pattern) {
        case 0: // Off
            gpio_set_level(LED_PIN, 0);
            break;
        case 1: // Fast blink (200ms) - BT ready
            led_state = !led_state;
            gpio_set_level(LED_PIN, led_state);
            break;
        case 2: // Slow blink (1000ms) - WiFi connecting
            led_state = !led_state;
            gpio_set_level(LED_PIN, led_state);
            break;
        case 3: // Solid ON - WiFi connected
            gpio_set_level(LED_PIN, 1);
            break;
        case 4: // Double blink - Error
            static int blink_count = 0;
            if (blink_count < 4) {
                led_state = !led_state;
                gpio_set_level(LED_PIN, led_state);
                blink_count++;
            } else {
                gpio_set_level(LED_PIN, 0);
                blink_count = 0;
                vTaskDelay(pdMS_TO_TICKS(1000));
            }
            break;
    }
}

/**
 * Set LED pattern
 */
static void set_led_pattern(int pattern)
{
    led_pattern = pattern;
    
    if (led_timer) {
        esp_timer_stop(led_timer);
    }
    
    uint64_t interval_us;
    switch (pattern) {
        case 1: interval_us = 200000; break;  // 200ms
        case 2: interval_us = 1000000; break; // 1000ms
        case 3: interval_us = 100000; break;  // 100ms
        case 4: interval_us = 150000; break;  // 150ms
        default: return;
    }
    
    if (pattern > 0) {
        esp_timer_start_periodic(led_timer, interval_us);
    }
}

/**
 * Initialize LED
 */
static void init_led(void)
{
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << LED_PIN),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    gpio_config(&io_conf);
    
    esp_timer_create_args_t timer_args = {
        .callback = led_timer_callback,
        .name = "led_timer"
    };
    esp_timer_create(&timer_args, &led_timer);
    
    ESP_LOGI(TAG, "LED initialized on GPIO %d", LED_PIN);
}

/**
 * Bluetooth connection callback
 */
static void bt_connection_callback(esp_bd_addr_t remote_addr, bool connected)
{
    if (connected) {
        ESP_LOGI(TAG, "Bluetooth device connected");
        set_led_pattern(1); // Fast blink when BT connected
    } else {
        ESP_LOGI(TAG, "Bluetooth device disconnected");
        // Check if WiFi is connected to determine LED pattern
        if (xEventGroupGetBits(status_event_group) & WIFI_CONNECTED_BIT) {
            set_led_pattern(3); // Solid if WiFi connected
        } else {
            set_led_pattern(1); // Fast blink if only BT ready
        }
    }
}

/**
 * WiFi event handler
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        ESP_LOGI(TAG, "WiFi disconnected, trying to reconnect...");
        set_led_pattern(2); // Slow blink - connecting
        esp_wifi_connect();
        xEventGroupClearBits(status_event_group, WIFI_CONNECTED_BIT);
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        xEventGroupSetBits(status_event_group, WIFI_CONNECTED_BIT);
        set_led_pattern(3); // Solid ON - connected
    }
}

/**
 * HTTP GET handler for status page
 */
static esp_err_t status_get_handler(httpd_req_t *req)
{
    const char* html_template = 
        "<!DOCTYPE html>"
        "<html><head><title>ESP32 Cat Feeder - Bluetooth Test</title>"
        "<meta name='viewport' content='width=device-width, initial-scale=1'>"
        "<style>"
        "body{font-family:Arial;margin:20px;background:#f0f0f0}"
        ".container{max-width:600px;margin:0 auto;background:white;padding:20px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1)}"
        ".status{padding:10px;margin:10px 0;border-radius:5px}"
        ".ok{background:#d4edda;color:#155724;border:1px solid #c3e6cb}"
        ".warning{background:#fff3cd;color:#856404;border:1px solid #ffeaa7}"
        ".error{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb}"
        "h1{color:#333;text-align:center}"
        "h2{color:#666;border-bottom:2px solid #007bff;padding-bottom:5px}"
        ".config-item{margin:10px 0;padding:8px;background:#f8f9fa;border-left:4px solid #007bff}"
        "button{background:#007bff;color:white;border:none;padding:10px 20px;border-radius:5px;cursor:pointer;margin:5px}"
        "button:hover{background:#0056b3}"
        "</style></head><body>"
        "<div class='container'>"
        "<h1>🐱 ESP32 Cat Feeder - Bluetooth Test</h1>"
        "<h2>Connection Status</h2>"
        "<div class='status %s'>WiFi: %s</div>"
        "<div class='status %s'>Bluetooth: %s</div>"
        "<h2>System Information</h2>"
        "<div class='config-item'><strong>Device:</strong> ESP32-WROOM-32</div>"
        "<div class='config-item'><strong>Firmware:</strong> Bluetooth Test v1.0</div>"
        "<div class='config-item'><strong>Free Heap:</strong> %" PRIu32 " bytes</div>"
        "<div class='config-item'><strong>Uptime:</strong> %lld seconds</div>"
        "<div class='config-item'><strong>BT Device Name:</strong> CatFeeder_ESP32</div>"
        "<div class='config-item'><strong>BT PIN:</strong> 1234</div>"
        "<h2>Bluetooth Commands for Testing</h2>"
        "<div class='config-item'>"
        "<strong>Available Commands:</strong><br>"
        "• PING - Test connectivity<br>"
        "• GET_STATUS - Get device status<br>"
        "• GET_DEVICE_INFO - Get device information<br>"
        "• ECHO:message - Echo back message<br>"
        "• SET_DEVICE_NAME:name - Set device name<br>"
        "• GET_TIME - Get current time<br>"
        "• TEST_LED - Test LED functionality"
        "</div>"
        "<h2>Mobile App Testing</h2>"
        "<div class='config-item'>"
        "<strong>✅ Ready for Mobile App Testing!</strong><br>"
        "1. Pair with 'CatFeeder_ESP32' (PIN: 1234)<br>"
        "2. Connect via Bluetooth terminal or MAUI app<br>"
        "3. Send commands and verify responses<br>"
        "4. Test all basic communication features"
        "</div>"
        "</div></body></html>";

    char html_response[2048];
    
    uint32_t free_heap = esp_get_free_heap_size();
    int64_t uptime = esp_timer_get_time() / 1000000;
    
    bool wifi_connected = (xEventGroupGetBits(status_event_group) & WIFI_CONNECTED_BIT) != 0;
    bool bt_ready = (xEventGroupGetBits(status_event_group) & BT_READY_BIT) != 0;
    
    snprintf(html_response, sizeof(html_response), html_template,
        wifi_connected ? "ok" : "error",
        wifi_connected ? "Connected" : "Disconnected",
        bt_ready ? "ok" : "warning",
        bt_ready ? "Ready for Connection" : "Not Ready",
        free_heap,
        uptime
    );
    
    httpd_resp_set_type(req, "text/html");
    httpd_resp_send(req, html_response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

/**
 * Start HTTP server
 */
static httpd_handle_t start_webserver(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.server_port = 80;
    
    ESP_LOGI(TAG, "Starting HTTP server on port %d", config.server_port);
    
    if (httpd_start(&server, &config) == ESP_OK) {
        httpd_uri_t status_uri = {
            .uri = "/",
            .method = HTTP_GET,
            .handler = status_get_handler,
            .user_ctx = NULL
        };
        httpd_register_uri_handler(server, &status_uri);
        return server;
    }
    
    ESP_LOGE(TAG, "Failed to start HTTP server");
    return NULL;
}

/**
 * Initialize WiFi
 */
static void init_wifi(void)
{
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();
    
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL));
    
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_start());
    
    ESP_LOGI(TAG, "WiFi initialized");
}

/**
 * Connect to WiFi
 */
static void connect_wifi(void)
{
    wifi_config_t wifi_config = {
        .sta = {
            .ssid = WIFI_SSID,
            .password = WIFI_PASS,
        },
    };
    
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_LOGI(TAG, "Connecting to WiFi SSID: %s", WIFI_SSID);
    
    set_led_pattern(2); // Slow blink - connecting
    ESP_ERROR_CHECK(esp_wifi_connect());
}

/**
 * Initialize NVS
 */
static void init_nvs(void)
{
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    ESP_LOGI(TAG, "NVS initialized");
}

/**
 * Main application task
 */
static void app_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Application task started");
    
    // Start with fast blink to show BT is ready
    set_led_pattern(1);
    
    // Wait for Bluetooth to be ready
    xEventGroupWaitBits(status_event_group, BT_READY_BIT, pdFALSE, pdFALSE, portMAX_DELAY);
    ESP_LOGI(TAG, "Bluetooth ready for connections");
    
    // Connect to WiFi
    connect_wifi();
    
    // Wait for WiFi connection
    xEventGroupWaitBits(status_event_group, WIFI_CONNECTED_BIT, pdFALSE, pdFALSE, portMAX_DELAY);
    
    // Start web server
    ESP_LOGI(TAG, "Starting web server...");
    server = start_webserver();
    if (server) {
        ESP_LOGI(TAG, "Web server started successfully");
    }
    
    // Main application loop
    while (1) {
        EventBits_t bits = xEventGroupGetBits(status_event_group);
        
        static int log_counter = 0;
        if (++log_counter >= 30) {
            log_counter = 0;
            ESP_LOGI(TAG, "Status - WiFi:%s BT:%s Free Heap:%" PRIu32,
                (bits & WIFI_CONNECTED_BIT) ? "OK" : "NO",
                (bits & BT_READY_BIT) ? "OK" : "NO",
                esp_get_free_heap_size()
            );
        }
        
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

/**
 * Application entry point
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32 Cat Feeder - Bluetooth Test v1.0");
    ESP_LOGI(TAG, "Built on %s %s", __DATE__, __TIME__);
    
    // Create event group
    status_event_group = xEventGroupCreate();
    if (status_event_group == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return;
    }
    
    // Initialize components
    init_nvs();
    init_led();
    init_wifi();
    
    // Initialize Bluetooth
    ESP_LOGI(TAG, "Initializing Bluetooth...");
    ESP_ERROR_CHECK(bluetooth_manager_init());
    ESP_ERROR_CHECK(bluetooth_manager_set_connection_callback(bt_connection_callback));
    ESP_ERROR_CHECK(bluetooth_manager_start());
    
    // Initialize basic Bluetooth commands
    ESP_ERROR_CHECK(bt_commands_basic_init());
    
    xEventGroupSetBits(status_event_group, BT_READY_BIT);
    
    // Create main application task
    xTaskCreate(app_task, "app_task", 8192, NULL, 5, NULL);
    
    ESP_LOGI(TAG, "=== ESP32 Bluetooth Test Ready ===");
    ESP_LOGI(TAG, "Device Name: CatFeeder_ESP32");
    ESP_LOGI(TAG, "Bluetooth PIN: 1234");
    ESP_LOGI(TAG, "Ready for mobile app testing!");
}
