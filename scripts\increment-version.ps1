# Smart Cat Feeder - Version Management Script
# Studio 311 LP

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("mobile", "firmware", "both")]
    [string]$Target,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("patch", "minor", "major")]
    [string]$Type = "patch"
)

$ErrorActionPreference = "Stop"

# Get script directory and project root
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$VersionFile = Join-Path $ProjectRoot "version.json"

Write-Host "🔧 Studio 311 LP - Smart Cat Feeder Version Manager" -ForegroundColor Cyan
Write-Host "Target: $Target | Type: $Type" -ForegroundColor Yellow

# Read current version
if (-not (Test-Path $VersionFile)) {
    Write-Error "Version file not found: $VersionFile"
    exit 1
}

$version = Get-Content $VersionFile | ConvertFrom-Json
$timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")

# Function to increment version numbers
function Increment-Version {
    param($current, $type)
    
    $parts = $current.version.Split('.')
    $major = [int]$parts[0]
    $minor = [int]$parts[1] 
    $patch = [int]$parts[2]
    
    switch ($type) {
        "major" { 
            $major++; $minor = 0; $patch = 0 
        }
        "minor" { 
            $minor++; $patch = 0 
        }
        "patch" { 
            $patch++ 
        }
    }
    
    $current.version = "$major.$minor.$patch"
    $current.build_number++
    $current.last_build = $timestamp
    
    return $current
}

# Update versions based on target
switch ($Target) {
    "mobile" {
        Write-Host "Mobile: Incrementing mobile app version..." -ForegroundColor Green
        $version.mobile_app = Increment-Version $version.mobile_app $Type
    }
    "firmware" {
        Write-Host "Firmware: Incrementing firmware version..." -ForegroundColor Green
        $version.firmware = Increment-Version $version.firmware $Type
    }
    "both" {
        Write-Host "Both: Incrementing both mobile and firmware versions..." -ForegroundColor Green
        $version.mobile_app = Increment-Version $version.mobile_app $Type
        $version.firmware = Increment-Version $version.firmware $Type
    }
}

# Update global version to highest
$mobileVer = $version.mobile_app.version.Split('.') | ForEach-Object { [int]$_ }
$firmwareVer = $version.firmware.version.Split('.') | ForEach-Object { [int]$_ }

$globalMajor = [Math]::Max($mobileVer[0], $firmwareVer[0])
$globalMinor = [Math]::Max($mobileVer[1], $firmwareVer[1])
$globalPatch = [Math]::Max($mobileVer[2], $firmwareVer[2])

$version.major = $globalMajor
$version.minor = $globalMinor
$version.patch = $globalPatch
$version.build++
$version.version = "$globalMajor.$globalMinor.$globalPatch.$($version.build)"

# Save updated version
$version | ConvertTo-Json -Depth 10 | Set-Content $VersionFile

Write-Host "✅ Version updated successfully!" -ForegroundColor Green
Write-Host "Global Version: $($version.version)" -ForegroundColor Cyan
Write-Host "Mobile App: $($version.mobile_app.version) (Build $($version.mobile_app.build_number))" -ForegroundColor Cyan
Write-Host "Firmware: $($version.firmware.version) (Build $($version.firmware.build_number))" -ForegroundColor Cyan

# Update mobile app project file
if ($Target -eq "mobile" -or $Target -eq "both") {
    $mobileProject = Join-Path $ProjectRoot "mobile-app\CatFeeder.Mobile\CatFeeder.Mobile.csproj"
    if (Test-Path $mobileProject) {
        Write-Host "Mobile: Updating mobile project file..." -ForegroundColor Yellow

        $content = Get-Content $mobileProject -Raw
        $content = $content -replace '<ApplicationDisplayVersion>[\d\.]+</ApplicationDisplayVersion>', "<ApplicationDisplayVersion>$($version.mobile_app.version)</ApplicationDisplayVersion>"
        $content = $content -replace '<ApplicationVersion>\d+</ApplicationVersion>', "<ApplicationVersion>$($version.mobile_app.build_number)</ApplicationVersion>"

        Set-Content $mobileProject $content
        Write-Host "Success: Mobile project updated!" -ForegroundColor Green
    }
}

# Update firmware version header
if ($Target -eq "firmware" -or $Target -eq "both") {
    $firmwareHeader = Join-Path $ProjectRoot "esp32-firmware\main\version.h"
    if (Test-Path $firmwareHeader) {
        Write-Host "Firmware: Updating firmware version header..." -ForegroundColor Yellow

        $headerContent = @"
#ifndef VERSION_H
#define VERSION_H

// Studio 311 LP - Smart Cat Feeder
// Auto-generated version file - DO NOT EDIT MANUALLY

#define FIRMWARE_VERSION_MAJOR $($version.firmware.version.Split('.')[0])
#define FIRMWARE_VERSION_MINOR $($version.firmware.version.Split('.')[1])
#define FIRMWARE_VERSION_PATCH $($version.firmware.version.Split('.')[2])
#define FIRMWARE_BUILD_NUMBER $($version.firmware.build_number)

#define FIRMWARE_VERSION_STRING "$($version.firmware.version)"
#define FIRMWARE_BUILD_STRING "$($version.firmware.build_number)"
#define FIRMWARE_FULL_VERSION "$($version.firmware.version).$($version.firmware.build_number)"

#define COMPANY_NAME "Studio 311 LP"
#define PRODUCT_NAME "Smart Cat Feeder"

#define BUILD_TIMESTAMP "$timestamp"

#endif // VERSION_H
"@

        Set-Content $firmwareHeader $headerContent
        Write-Host "Success: Firmware header updated!" -ForegroundColor Green
    }
}

Write-Host "Complete: Version management complete!" -ForegroundColor Magenta
