#pragma once

#include "esp_err.h"
#include <stdint.h>
#include <stddef.h>
#include "motor_control.h"  // For motor_type_t definition

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize configuration storage
 * @return ESP_OK on success
 */
esp_err_t config_init(void);

/**
 * @brief Set motor type configuration
 * @param motor_type Type of motor to use
 * @return ESP_OK on success
 */
esp_err_t config_set_motor_type(motor_type_t motor_type);

/**
 * @brief Get motor type configuration
 * @return Current motor type setting
 */
motor_type_t config_get_motor_type(void);

/**
 * @brief Save WiFi credentials
 * @param ssid WiFi network name
 * @param password WiFi password
 * @return ESP_OK on success
 */
esp_err_t config_set_wifi_credentials(const char* ssid, const char* password);

/**
 * @brief Get saved WiFi credentials
 * @param ssid Buffer for SSID (output)
 * @param ssid_len Size of SSID buffer
 * @param password Buffer for password (output)
 * @param pass_len Size of password buffer
 * @return ESP_OK on success
 */
esp_err_t config_get_wifi_credentials(char* ssid, size_t ssid_len, char* password, size_t pass_len);

#ifdef __cplusplus
}
#endif
