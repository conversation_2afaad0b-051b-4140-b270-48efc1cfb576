<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:models="clr-namespace:CatFeeder.Models;assembly=CatFeeder.Models"
             xmlns:views="clr-namespace:CatFeeder.Mobile.Views"
             x:Class="CatFeeder.Mobile.Views.SettingsPage"
             Title="Settings">
    <ScrollView>
        <VerticalStackLayout Spacing="20" Padding="30">
            <!-- Connection Settings -->
            <Label Text="Connection Settings" FontSize="20" FontAttributes="Bold"/>
            <Frame BorderColor="LightGray" CornerRadius="10" Padding="15">
                <VerticalStackLayout Spacing="15">
                    <!-- Bluetooth Connection -->
                    <Label Text="Bluetooth Device" FontAttributes="Bold"/>
                    <Grid ColumnDefinitions="*,Auto" ColumnSpacing="10">
                        <Picker Grid.Column="0"
                                ItemsSource="{Binding DiscoveredDevices}"
                                SelectedItem="{Binding SelectedDevice}">
                            <Picker.ItemDisplayBinding>
                                <Binding Path="Name" />
                            </Picker.ItemDisplayBinding>
                        </Picker>
                        <Button Grid.Column="1" 
                                Text="Scan" 
                                Clicked="OnScanClicked"/>
                    </Grid>
                    <Button Text="{Binding ConnectionButtonText}" 
                            Clicked="OnConnectClicked"
                            IsEnabled="{Binding CanConnect}"/>
                            
                    <!-- WiFi Settings (via Bluetooth) -->
                    <Label Text="WiFi Settings" FontAttributes="Bold" Margin="0,10,0,0"/>
                    <Label Text="Configure WiFi (requires Bluetooth connection)" FontSize="12"/>
                    <Entry Placeholder="WiFi SSID" Text="{Binding WifiSsid}"/>
                    <Entry Placeholder="WiFi Password" Text="{Binding WifiPassword}" IsPassword="True"/>
                    <Button Text="Configure WiFi" 
                            Clicked="OnConfigureWifiClicked"
                            IsEnabled="{Binding IsBluetoothConnected}"/>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Feeding Settings -->
            <Label Text="Feeding Settings" FontSize="20" FontAttributes="Bold"/>
            <Frame BorderColor="LightGray" CornerRadius="10" Padding="15">
                <VerticalStackLayout Spacing="15">
                    <Label Text="Portion Size"/>
                    <Slider Minimum="50" Maximum="2000" Value="{Binding DefaultPortionSize}"/>
                    <Label Text="{Binding DefaultPortionSizeText}"/>
                    
                    <Label Text="Feeding Schedule" Margin="0,10,0,0"/>
                    <Grid ColumnDefinitions="*,*,Auto" RowDefinitions="Auto,Auto" ColumnSpacing="10">
                        <Label Grid.Row="0" Grid.Column="0" Text="Interval (hours)"/>
                        <Label Grid.Row="0" Grid.Column="1" Text="Doses"/>
                        <Entry Grid.Row="1" Grid.Column="0" Text="{Binding FeedingInterval}" Keyboard="Numeric"/>
                        <Entry Grid.Row="1" Grid.Column="1" Text="{Binding FeedingDoses}" Keyboard="Numeric"/>
                        <Button Grid.Row="1" Grid.Column="2" Text="Set" Clicked="OnSetScheduleClicked"/>
                    </Grid>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Optional Features -->
            <Label Text="Optional Features" FontSize="20" FontAttributes="Bold"/>
            <Frame BorderColor="LightGray" CornerRadius="10" Padding="15">
                <VerticalStackLayout Spacing="15">
                    <CheckBox IsChecked="{Binding EnableFoodSensor}" />
                    <Label Text="Enable Food Level Sensor" VerticalOptions="Center"/>
                    
                    <CheckBox IsChecked="{Binding EnableLedIndicators}" />
                    <Label Text="Enable LED Indicators" VerticalOptions="Center"/>
                    
                    <CheckBox IsChecked="{Binding EnableBuzzer}" />
                    <Label Text="Enable Buzzer" VerticalOptions="Center"/>
                    
                    <CheckBox IsChecked="{Binding EnableManualButton}" />
                    <Label Text="Enable Manual Feed Button" VerticalOptions="Center"/>
                    
                    <Button Text="Save Features Configuration" 
                            Clicked="OnSaveFeaturesClicked"
                            IsEnabled="{Binding IsBluetoothConnected}"/>
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>