/**
 * Application Configuration
 * 
 * Defines configuration structures and default values for the cat feeder
 */

#pragma once

#include "motor_control.h"
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Optional features configuration
 */
typedef struct {
    bool enable_food_level_sensor;  ///< Enable food level monitoring
    bool enable_led_indicators;     ///< Enable LED status indicators
    bool enable_buzzer;              ///< Enable buzzer notifications
    bool enable_manual_button;      ///< Enable manual feed button
} optional_features_t;

/**
 * LED configuration
 */
typedef struct {
    int power_led_pin;              ///< Power/status LED pin (-1 if disabled)
    int wifi_led_pin;               ///< WiFi status LED pin (-1 if disabled)
    int feeding_led_pin;            ///< Feeding activity LED pin (-1 if disabled)
    int error_led_pin;              ///< Error status LED pin (-1 if disabled)
} led_config_t;

/**
 * Sensor configuration
 */
typedef struct {
    int food_level_sensor_dout_pin; ///< HX711 DOUT pin (-1 if disabled)
    int food_level_sensor_sck_pin;  ///< HX711 SCK pin (-1 if disabled)
    float food_level_calibration;   ///< Calibration factor for load cell
    float food_level_offset;        ///< Zero offset for load cell
    int manual_button_pin;          ///< Manual feed button pin (-1 if disabled)
    int buzzer_pin;                 ///< Buzzer pin (-1 if disabled)
} sensor_config_t;

/**
 * Feeding configuration
 */
typedef struct {
    uint32_t default_portion_steps; ///< Default portion size in motor steps/time
    uint32_t min_portion_steps;     ///< Minimum portion size
    uint32_t max_portion_steps;     ///< Maximum portion size
    uint32_t feeding_speed;         ///< Feeding speed (Hz for stepper, duty cycle for DC)
    uint32_t max_feedings_per_day;  ///< Maximum feedings allowed per day
    uint32_t min_interval_minutes;  ///< Minimum interval between feedings (minutes)
} feeding_config_t;

/**
 * WiFi configuration
 */
typedef struct {
    char ssid[32];                  ///< WiFi SSID
    char password[64];              ///< WiFi password
    bool auto_connect;              ///< Auto-connect on startup
    uint32_t connection_timeout_ms; ///< Connection timeout
} app_wifi_config_t;

/**
 * Bluetooth configuration
 */
typedef struct {
    char device_name[32];           ///< Bluetooth device name
    bool enable_on_startup;         ///< Enable Bluetooth on startup
    bool allow_config_via_bt;       ///< Allow configuration via Bluetooth
} bluetooth_config_t;

/**
 * Web server configuration
 */
typedef struct {
    uint16_t port;                  ///< HTTP server port
    bool enable_cors;               ///< Enable CORS headers
    char api_key[64];               ///< API key for authentication (optional)
} web_server_config_t;

/**
 * Time configuration
 */
typedef struct {
    char ntp_server[64];            ///< NTP server address
    char timezone[32];              ///< Timezone string
    bool auto_sync_enabled;         ///< Enable automatic time sync
    uint32_t sync_interval_hours;   ///< Sync interval in hours
} time_config_t;

/**
 * Security configuration
 */
typedef struct {
    bool require_authentication;    ///< Require user authentication
    bool allow_guest_access;        ///< Allow guest access
    uint32_t session_timeout_minutes; ///< Session timeout
    char bt_pin[8];                 ///< Bluetooth PIN code
    char device_id[16];             ///< Unique device identifier
} security_config_t;

/**
 * Complete application configuration
 */
typedef struct {
    motor_type_t motor_type;        ///< Motor type selection
    stepper_config_t stepper_config; ///< Stepper motor configuration
    dc_config_t dc_config;          ///< DC motor configuration
    optional_features_t features;   ///< Optional features
    led_config_t led_config;        ///< LED configuration
    sensor_config_t sensor_config;  ///< Sensor configuration
    feeding_config_t feeding_config; ///< Feeding configuration
    app_wifi_config_t wifi_config;  ///< WiFi configuration
    bluetooth_config_t bt_config;   ///< Bluetooth configuration
    web_server_config_t web_config; ///< Web server configuration
    time_config_t time_config;      ///< Time synchronization configuration
    security_config_t security_config; ///< Security configuration
    uint32_t config_version;        ///< Configuration version for migration
    bool factory_reset_flag;        ///< Factory reset flag
} app_config_t;

/**
 * Load application configuration from NVS
 * 
 * @param config Pointer to configuration structure
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t app_config_load(void);

/**
 * Save application configuration to NVS
 *
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t app_config_save(void);

/**
 * Set default configuration values
 *
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t app_config_set_defaults(void);

/**
 * Validate configuration
 * 
 * @param config Configuration to validate
 * @return ESP_OK if valid, error code otherwise
 */
esp_err_t app_config_validate(const app_config_t *config);

/**
 * Reset configuration to factory defaults
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t app_config_factory_reset(void);

/**
 * Get configuration as JSON string
 * 
 * @param config Configuration structure
 * @param json_buffer Buffer to store JSON string
 * @param buffer_size Size of the buffer
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t app_config_to_json(const app_config_t *config, char *json_buffer, size_t buffer_size);

/**
 * Load configuration from JSON string
 * 
 * @param json_string JSON configuration string
 * @param config Configuration structure to populate
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t app_config_from_json(const char *json_string, app_config_t *config);

#ifdef __cplusplus
}
#endif
