# Development Environment Setup Guide

This guide will help you set up the complete development environment for the ESP32 Cat Feeder project.

## Prerequisites

- Windows 10/11
- Administrator privileges for installations
- Stable internet connection

## Step 1: Install ESP-IDF (ESP32 Development Framework)

### Option A: Using ESP-IDF Tools Installer (Recommended)

1. **Download the ESP-IDF Tools Installer**
   - Go to: https://dl.espressif.com/dl/esp-idf/
   - Download the latest **Online Installer** (v2.3.4 or newer)
   - File size: ~4.35 MB

2. **Run the Installer**
   - Run as Administrator
   - Select "ESP-IDF v5.4.1" (latest stable version)
   - Choose installation directory: `C:\Espressif` (recommended)
   - Select "Install ESP-IDF Tools"
   - Select "Install ESP-IDF Python Environment"

3. **Installation Components**
   The installer will download and install:
   - Embedded Python
   - Cross-compilers for ESP32
   - OpenOCD (debugging)
   - CMake and Ninja build tools
   - ESP-IDF framework

4. **Verify Installation**
   - At the end, check "Run ESP-IDF Command Prompt"
   - In the command prompt, run: `idf.py --version`
   - You should see ESP-IDF version information

### Option B: Manual Installation (Advanced Users)

If you prefer manual installation, follow the official guide:
https://docs.espressif.com/projects/esp-idf/en/stable/esp32/get-started/windows-setup.html

## Step 2: Install VS Code with ESP-IDF Extension

1. **Install Visual Studio Code**
   - Download from: https://code.visualstudio.com/
   - Install with default settings

2. **Install ESP-IDF Extension**
   - Open VS Code
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "ESP-IDF"
   - Install the official "ESP-IDF" extension by Espressif

3. **Configure ESP-IDF Extension**
   - Press `Ctrl+Shift+P` and type "ESP-IDF: Configure ESP-IDF Extension"
   - Select "Use existing setup" if you used the installer
   - Point to your ESP-IDF installation directory (usually `C:\Espressif\esp-idf`)

## Step 3: Install .NET 8 SDK and MAUI

1. **Download .NET 8 SDK**
   - Go to: https://dotnet.microsoft.com/en-us/download/dotnet/8.0
   - Download ".NET 8.0 SDK" (not just runtime)
   - Install with default settings

2. **Install MAUI Workload**
   - Open Command Prompt as Administrator
   - Run: `dotnet workload install maui`
   - This will download and install MAUI templates and tools

3. **Verify .NET Installation**
   ```bash
   dotnet --version
   dotnet workload list
   ```
   - You should see .NET 8.x.x version
   - MAUI should be listed in workloads

## Step 4: Install Visual Studio 2022 (Optional but Recommended for MAUI)

1. **Download Visual Studio 2022**
   - Go to: https://visualstudio.microsoft.com/downloads/
   - Download "Visual Studio 2022 Community" (free)

2. **Select Workloads During Installation**
   - ✅ ".NET Multi-platform App UI development" (MAUI)
   - ✅ "Mobile development with .NET" (Xamarin)
   - ✅ "ASP.NET and web development" (optional)

3. **Individual Components** (if needed)
   - Android SDK setup (for Android deployment)
   - iOS development tools (if you have a Mac for iOS development)

## Step 5: Install Git (if not already installed)

1. **Download Git for Windows**
   - Go to: https://git-scm.com/download/win
   - Download and install with default settings

2. **Configure Git** (optional)
   ```bash
   git config --global user.name "Your Name"
   git config --global user.email "<EMAIL>"
   ```

## Step 6: Verify Complete Setup

### Test ESP-IDF Setup

1. **Open ESP-IDF Command Prompt**
   - Start Menu → ESP-IDF → ESP-IDF Command Prompt

2. **Create Test Project**
   ```bash
   cd %USERPROFILE%\Desktop
   idf.py create-project test_esp32
   cd test_esp32
   idf.py set-target esp32
   idf.py build
   ```

3. **Expected Result**
   - Project should build successfully
   - You should see "Project build complete" message

### Test MAUI Setup

1. **Open Command Prompt**
   ```bash
   dotnet new maui -n TestMauiApp
   cd TestMauiApp
   dotnet build
   ```

2. **Expected Result**
   - Project should build successfully
   - No errors in the build output

## Step 7: Hardware Setup (When Ready)

### ESP32 Development Board
- **Recommended**: ESP32-DevKitC-V4
- **Alternative**: ESP32-WROOM-32 development board
- **USB Cable**: USB-A to Micro-USB (for programming)

### Motor Options
- **Stepper Motor**: NEMA 17 + A4988 driver
- **DC Motor**: 12V geared motor + L298N driver

### Optional Components
- Load cell (1kg-5kg) + HX711 amplifier
- LEDs (status indication)
- Buzzer (5V active buzzer)
- Limit switches (for DC motor)

## Troubleshooting

### ESP-IDF Issues
- **Path too long**: Ensure installation path < 90 characters
- **Permission denied**: Run as Administrator
- **Python issues**: Use the embedded Python from ESP-IDF

### MAUI Issues
- **Workload not found**: Reinstall with `dotnet workload install maui --force`
- **Android SDK**: Install via Visual Studio Installer
- **Build errors**: Ensure .NET 8 SDK is installed

### VS Code Issues
- **ESP-IDF extension not working**: Restart VS Code after installation
- **IntelliSense not working**: Configure ESP-IDF extension properly

## Next Steps

After completing this setup:

1. **Test the environment** with the verification steps above
2. **Clone or navigate** to your CatFeederV2 project directory
3. **Start with ESP32 firmware development** in the `esp32-firmware` folder
4. **Begin MAUI app development** in the `mobile-app` folder

## Support Resources

- **ESP-IDF Documentation**: https://docs.espressif.com/projects/esp-idf/
- **MAUI Documentation**: https://docs.microsoft.com/en-us/dotnet/maui/
- **ESP32 Community**: https://esp32.com/
- **.NET Community**: https://dotnetfoundation.org/community

---

**Estimated Setup Time**: 30-60 minutes depending on internet speed and system performance.

**Storage Requirements**: 
- ESP-IDF: ~2-3 GB
- .NET 8 + MAUI: ~1-2 GB  
- Visual Studio 2022: ~3-5 GB
- Total: ~6-10 GB
