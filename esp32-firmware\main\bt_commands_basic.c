/**
 * Basic Bluetooth Command Handlers
 * 
 * Initial implementation for testing basic communication
 */

#include "bt_commands.h"
#include "bluetooth_manager.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_timer.h"
#include "esp_wifi.h"
#include <string.h>
#include <stdio.h>

static const char *TAG = "BT_COMMANDS";

/**
 * Handle PING command - Basic connectivity test
 */
esp_err_t bt_cmd_ping(const char* command, const char* params, 
                     char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "PING command from user: %s", username);
    snprintf(response, response_size, "SUCCESS:PONG");
    return ESP_OK;
}

/**
 * Handle GET_STATUS command - Basic device status
 */
esp_err_t bt_cmd_get_status(const char* command, const char* params, 
                           char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_STATUS command from user: %s", username);
    
    // Get basic system information
    uint32_t free_heap = esp_get_free_heap_size();
    int64_t uptime = esp_timer_get_time() / 1000000;
    
    // Get WiFi status
    wifi_ap_record_t ap_info;
    bool wifi_connected = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);
    
    // Get Bluetooth status
    bt_status_t bt_status = bluetooth_manager_get_status();
    const char* bt_status_str = (bt_status == BT_STATUS_CONNECTED) ? "connected" : "discoverable";
    
    snprintf(response, response_size,
        "STATUS:{"
        "\"wifi_connected\":%s,"
        "\"bt_status\":\"%s\","
        "\"free_heap\":%lu,"
        "\"uptime\":%lld,"
        "\"firmware_version\":\"1.0.0\","
        "\"device_type\":\"cat_feeder\""
        "}",
        wifi_connected ? "true" : "false",
        bt_status_str,
        free_heap,
        uptime
    );
    
    return ESP_OK;
}

/**
 * Handle GET_DEVICE_INFO command - Device information
 */
esp_err_t bt_cmd_get_device_info(const char* command, const char* params, 
                                char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_DEVICE_INFO command from user: %s", username);
    
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    
    snprintf(response, response_size,
        "INFO:{"
        "\"chip_model\":\"ESP32\","
        "\"chip_revision\":%d,"
        "\"cpu_cores\":%d,"
        "\"flash_size\":%d,"
        "\"device_name\":\"CatFeeder_ESP32\","
        "\"mac_address\":\"00:00:00:00:00:00\""
        "}",
        chip_info.revision,
        chip_info.cores,
        spi_flash_get_chip_size()
    );
    
    return ESP_OK;
}

/**
 * Handle ECHO command - Echo back parameters
 */
esp_err_t bt_cmd_echo(const char* command, const char* params, 
                     char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "ECHO command from user: %s, params: %s", username, params);
    snprintf(response, response_size, "ECHO:%s", params);
    return ESP_OK;
}

/**
 * Handle SET_DEVICE_NAME command - Set device name
 */
esp_err_t bt_cmd_set_device_name(const char* command, const char* params, 
                                char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "SET_DEVICE_NAME command from user: %s, name: %s", username, params);
    
    if (strlen(params) == 0 || strlen(params) > 31) {
        snprintf(response, response_size, "ERROR:Invalid device name length");
        return ESP_ERR_INVALID_ARG;
    }
    
    // For now, just acknowledge - actual implementation would save to NVS
    snprintf(response, response_size, "SUCCESS:Device name set to %s", params);
    return ESP_OK;
}

/**
 * Handle RESTART command - Restart device
 */
esp_err_t bt_cmd_restart(const char* command, const char* params, 
                        char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "RESTART command from user: %s", username);
    
    snprintf(response, response_size, "SUCCESS:Restarting device in 3 seconds");
    
    // Schedule restart after response is sent
    // For now, just acknowledge - actual restart would be implemented
    return ESP_OK;
}

/**
 * Handle GET_TIME command - Get current time
 */
esp_err_t bt_cmd_get_time(const char* command, const char* params, 
                         char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_TIME command from user: %s", username);
    
    time_t now;
    time(&now);
    
    snprintf(response, response_size, "TIME:%ld", now);
    return ESP_OK;
}

/**
 * Handle TEST_LED command - Test LED functionality
 */
esp_err_t bt_cmd_test_led(const char* command, const char* params, 
                         char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "TEST_LED command from user: %s", username);
    
    // For now, just acknowledge - actual LED control would be implemented
    snprintf(response, response_size, "SUCCESS:LED test completed");
    return ESP_OK;
}

/**
 * Initialize basic command handlers
 */
esp_err_t bt_commands_basic_init(void)
{
    ESP_LOGI(TAG, "Initializing basic Bluetooth commands");
    
    // Register basic commands for testing
    bluetooth_manager_register_command("PING", bt_cmd_ping);
    bluetooth_manager_register_command("GET_STATUS", bt_cmd_get_status);
    bluetooth_manager_register_command("GET_DEVICE_INFO", bt_cmd_get_device_info);
    bluetooth_manager_register_command("ECHO", bt_cmd_echo);
    bluetooth_manager_register_command("SET_DEVICE_NAME", bt_cmd_set_device_name);
    bluetooth_manager_register_command("RESTART", bt_cmd_restart);
    bluetooth_manager_register_command("GET_TIME", bt_cmd_get_time);
    bluetooth_manager_register_command("TEST_LED", bt_cmd_test_led);
    
    ESP_LOGI(TAG, "Basic Bluetooth commands initialized");
    return ESP_OK;
}
