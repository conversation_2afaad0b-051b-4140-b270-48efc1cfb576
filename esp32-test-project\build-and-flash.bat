@echo off
echo ========================================
echo ESP32-WROOM-32 Test Project Build Script
echo ========================================
echo.

:: Check if ESP-IDF is available
idf.py --version >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: ESP-IDF not found in PATH
    echo Please run this script from ESP-IDF Command Prompt
    echo Or run: %USERPROFILE%\Desktop\esp-idf\export.bat
    pause
    exit /b 1
)

echo ESP-IDF found, proceeding with build...
echo.

:: Set target to ESP32
echo [1/4] Setting target to ESP32...
idf.py set-target esp32
if %errorLevel% neq 0 (
    echo ERROR: Failed to set target
    pause
    exit /b 1
)

:: Build the project
echo.
echo [2/4] Building project...
idf.py build
if %errorLevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

:: Check if COM port is provided
if "%1"=="" (
    echo.
    echo [3/4] Detecting COM ports...
    echo Available COM ports:
    
    :: List available COM ports
    for /f "tokens=1" %%i in ('wmic path Win32_SerialPort get DeviceID /format:value ^| find "="') do (
        set "%%i"
    )
    
    echo.
    echo Please specify COM port as argument: build-and-flash.bat COM3
    echo Or manually flash with: idf.py -p COM_PORT flash monitor
    pause
    exit /b 0
)

:: Flash and monitor
echo.
echo [3/4] Flashing to %1...
idf.py -p %1 flash
if %errorLevel% neq 0 (
    echo ERROR: Flash failed
    echo Check if:
    echo - ESP32 is connected to %1
    echo - No other programs are using the port
    echo - ESP32 is in download mode (hold BOOT button while pressing RESET)
    pause
    exit /b 1
)

echo.
echo [4/4] Starting monitor...
echo Press Ctrl+] to exit monitor
echo.
idf.py -p %1 monitor
