# ESP32-WROOM-32 Test Project

This is a comprehensive test project for your ESP-WROOM-32 that demonstrates all the features needed for the Cat Feeder project.

## Features

### ✅ LED Status Indicators
- **Fast blink (200ms)**: Bluetooth ready for configuration
- **Slow blink (1000ms)**: <PERSON>i<PERSON><PERSON> connecting
- **Solid ON**: Wi<PERSON><PERSON> connected, web server running
- **Double blink**: Error state

### ✅ Bluetooth Configuration
- Configure WiFi credentials
- Select motor type (Stepper, DC Time, DC Sensor)
- Enable/disable optional features
- Save configuration to flash memory

### ✅ WiFi Web Interface
- Device status page
- System information
- LED test function
- Real-time status updates

### ✅ Complete Configuration System
- Persistent storage in NVS flash
- Motor type selection
- Optional features configuration
- WiFi credentials management

## Quick Start

### 1. Build and Flash

```bash
cd esp32-test-project
idf.py set-target esp32
idf.py build
idf.py -p COM_PORT flash monitor
```

Replace `COM_PORT` with your ESP32's serial port (e.g., `COM3` on Windows, `/dev/ttyUSB0` on Linux).

### 2. Watch the LED

After flashing, the built-in LED (GPIO 2) should start **fast blinking** (200ms intervals), indicating Bluetooth is ready for configuration.

### 3. Configure via Bluetooth

#### Option A: Use the Python Configuration Tool

```bash
# Install required package
pip install pybluez

# Run the configuration tool
python bluetooth-config-tool.py
```

The tool will:
1. Scan for your ESP32 device
2. Connect via Bluetooth
3. Guide you through configuration
4. Save settings to the device

#### Option B: Use Any Bluetooth Terminal App

1. **Pair with the device**: Look for "CatFeeder_ESP32" in Bluetooth settings
2. **Connect using a Bluetooth terminal app** (like "Bluetooth Terminal" on Android)
3. **Send configuration commands**:

```
GET_STATUS                    # Get current status
WIFI:YourSSID,YourPassword   # Set WiFi credentials
MOTOR:0                      # Set motor type (0=stepper, 1=dc_time, 2=dc_sensor)
FEATURES:1,1,0,1            # Set features (food_sensor,led,buzzer,button)
SAVE_CONFIG                 # Save and apply configuration
```

### 4. Access Web Interface

After configuration, the device will:
1. LED changes to **slow blink** (connecting to WiFi)
2. LED becomes **solid ON** (WiFi connected, web server running)
3. Check the serial monitor for the IP address
4. Open your browser and go to the ESP32's IP address

## Configuration Commands

### Bluetooth Commands

| Command | Description | Example |
|---------|-------------|---------|
| `GET_STATUS` | Get current device status | `GET_STATUS` |
| `WIFI:ssid,password` | Set WiFi credentials | `WIFI:MyNetwork,MyPassword` |
| `MOTOR:type` | Set motor type | `MOTOR:0` (0=stepper, 1=dc_time, 2=dc_sensor) |
| `FEATURES:a,b,c,d` | Set optional features | `FEATURES:1,1,0,1` |
| `SAVE_CONFIG` | Save configuration | `SAVE_CONFIG` |

### Feature Flags (for FEATURES command)

Position 1: Food Level Sensor (0=disabled, 1=enabled)
Position 2: LED Indicators (0=disabled, 1=enabled)  
Position 3: Buzzer (0=disabled, 1=enabled)
Position 4: Manual Button (0=disabled, 1=enabled)

Example: `FEATURES:1,1,0,1` = Food sensor ON, LEDs ON, Buzzer OFF, Button ON

## Web Interface

Once connected to WiFi, access the web interface at the ESP32's IP address:

- **Device Status**: WiFi, Bluetooth, Configuration status
- **Current Configuration**: Motor type, enabled features
- **System Information**: Free memory, uptime, chip model
- **LED Test**: Test the built-in LED functionality

## Troubleshooting

### LED Not Blinking
- Check power connection
- Verify ESP32 is properly flashed
- Check serial monitor for error messages

### Can't Find Bluetooth Device
- Make sure ESP32 is powered on
- LED should be fast blinking (Bluetooth ready)
- Try restarting the ESP32
- Check if device is already paired/connected

### WiFi Connection Issues
- Verify SSID and password are correct
- Check WiFi signal strength
- Ensure WiFi network is 2.4GHz (ESP32 doesn't support 5GHz)
- Check serial monitor for connection details

### Web Server Not Accessible
- Ensure WiFi is connected (LED solid ON)
- Check the IP address in serial monitor
- Try accessing from the same network
- Disable firewall temporarily for testing

## Serial Monitor Output

The device provides detailed logging via serial monitor:

```
I (1234) ESP32_TEST: ESP32-WROOM-32 Cat Feeder Test v1.0
I (1235) ESP32_TEST: Built on Dec 19 2024 10:30:00
I (1240) ESP32_TEST: LED initialized on GPIO 2
I (1245) ESP32_TEST: NVS initialized
I (1250) ESP32_TEST: No saved configuration found, using defaults
I (1255) ESP32_TEST: Bluetooth initialized - Device name: CatFeeder_ESP32
I (1260) ESP32_TEST: WiFi initialized
I (1265) ESP32_TEST: === ESP32 Cat Feeder Test Ready ===
```

## Hardware Information

### ESP-WROOM-32 Specifications
- **Chip**: ESP32-D0WDQ6 (dual core)
- **Flash**: 4MB
- **RAM**: 520KB
- **Built-in LED**: GPIO 2
- **WiFi**: 802.11 b/g/n (2.4GHz)
- **Bluetooth**: Classic + BLE

### Pin Usage in Test Project
- **GPIO 2**: Built-in LED (status indicator)
- **WiFi**: Internal antenna
- **Bluetooth**: Internal antenna

## Next Steps

After successfully testing this project:

1. **Verify all functions work**:
   - LED patterns
   - Bluetooth configuration
   - WiFi connection
   - Web interface

2. **Plan your hardware**:
   - Choose motor type based on testing
   - Select optional features you want
   - Order components from hardware guide

3. **Move to full project**:
   - Use the main Cat Feeder firmware
   - Add motor drivers and sensors
   - Build mechanical feeding system

## Configuration Examples

### Example 1: Stepper Motor with All Features
```
WIFI:MyNetwork,MyPassword
MOTOR:0
FEATURES:1,1,1,1
SAVE_CONFIG
```

### Example 2: Simple DC Motor Setup
```
WIFI:MyNetwork,MyPassword
MOTOR:1
FEATURES:0,1,0,1
SAVE_CONFIG
```

### Example 3: Minimal Configuration
```
WIFI:MyNetwork,MyPassword
MOTOR:0
FEATURES:0,0,0,0
SAVE_CONFIG
```

## Support

If you encounter issues:

1. Check the serial monitor output
2. Verify your ESP-IDF installation
3. Ensure proper power supply
4. Try different USB cables/ports
5. Check the main project documentation

This test project validates that your ESP-WROOM-32 is working correctly and ready for the full Cat Feeder implementation!
