# Studio 311 LP - Smart Cat Feeder Mobile Deploy Script
param([switch]$StartApp)

Write-Host "🚀 Studio 311 LP - Smart Cat Feeder Mobile Deploy" -ForegroundColor Cyan

# Check device
$devices = adb devices
if (-not ($devices | Select-String "device$")) {
    Write-Host "❌ No Android device connected!" -ForegroundColor Red
    exit 1
}
Write-Host "✅ Android device connected" -ForegroundColor Green

# Get paths
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$MobileProject = Join-Path $ProjectRoot "mobile-app\CatFeeder.Mobile"

Write-Host "📱 Building and deploying..." -ForegroundColor Cyan

# Build and deploy
Push-Location $MobileProject
try {
    dotnet build -f net9.0-android -c Debug -t:Install
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Deploy successful!" -ForegroundColor Green
        
        if ($StartApp) {
            Write-Host "🚀 Starting app..." -ForegroundColor Cyan
            adb shell monkey -p com.studio311lp.catfeeder -c android.intent.category.LAUNCHER 1
            Write-Host "✅ App started!" -ForegroundColor Green
        }
    } else {
        Write-Host "❌ Deploy failed!" -ForegroundColor Red
    }
} finally {
    Pop-Location
}

Write-Host "🎉 Complete!" -ForegroundColor Green
