using System.ComponentModel.DataAnnotations;

namespace CatFeeder.Models
{
    /// <summary>
    /// Bluetooth device connection status
    /// </summary>
    public enum BluetoothConnectionStatus
    {
        Disconnected,
        Connecting,
        Connected,
        Authenticating,
        Authenticated,
        Error
    }

    /// <summary>
    /// Bluetooth device information
    /// </summary>
    public class BluetoothDeviceInfo
    {
        public string Name { get; set; } = "";
        public string Address { get; set; } = "";
        public int SignalStrength { get; set; }
        public bool IsPaired { get; set; }
        public bool IsConnected { get; set; }
        public bool IsAuthenticated { get; set; }
        public DateTime LastSeen { get; set; }
        public string DeviceType { get; set; } = "";
        public List<string> Services { get; set; } = new();
    }

    /// <summary>
    /// Bluetooth connection configuration
    /// </summary>
    public class BluetoothConnectionConfig
    {
        [Required]
        [StringLength(7, MinimumLength = 4)]
        public string Pin { get; set; } = "1234";

        public int ConnectionTimeoutSeconds { get; set; } = 30;
        public int CommandTimeoutSeconds { get; set; } = 10;
        public bool AutoReconnect { get; set; } = true;
        public int MaxReconnectAttempts { get; set; } = 3;
        public bool EnableEncryption { get; set; } = true;
    }

    /// <summary>
    /// Bluetooth command types for Cat Feeder
    /// </summary>
    public static class BluetoothCommands
    {
        // Authentication Commands
        public const string LOGIN = "LOGIN";
        public const string LOGOUT = "LOGOUT";
        public const string CHANGE_PASSWORD = "CHANGE_PASSWORD";

        // Device Information Commands
        public const string GET_STATUS = "GET_STATUS";
        public const string GET_DEVICE_INFO = "GET_DEVICE_INFO";
        public const string SET_DEVICE_NAME = "SET_DEVICE_NAME";

        // WiFi Configuration Commands
        public const string WIFI_SCAN = "WIFI_SCAN";
        public const string WIFI_CONNECT = "WIFI_CONNECT";
        public const string WIFI_DISCONNECT = "WIFI_DISCONNECT";
        public const string WIFI_STATUS = "WIFI_STATUS";

        // Motor Configuration Commands
        public const string SET_MOTOR_TYPE = "SET_MOTOR_TYPE";
        public const string SET_STEPPER_CONFIG = "SET_STEPPER_CONFIG";
        public const string SET_DC_CONFIG = "SET_DC_CONFIG";
        public const string TEST_MOTOR = "TEST_MOTOR";

        // Optional Features Commands
        public const string SET_FEATURES = "SET_FEATURES";
        public const string SET_LED_CONFIG = "SET_LED_CONFIG";
        public const string SET_SENSOR_CONFIG = "SET_SENSOR_CONFIG";

        // Feeding Configuration Commands
        public const string SET_FEEDING_CONFIG = "SET_FEEDING_CONFIG";
        public const string MANUAL_FEED = "MANUAL_FEED";
        public const string GET_FEEDING_HISTORY = "GET_FEEDING_HISTORY";

        // Schedule Configuration Commands
        public const string SET_SCHEDULE = "SET_SCHEDULE";
        public const string GET_SCHEDULE = "GET_SCHEDULE";
        public const string CLEAR_SCHEDULE = "CLEAR_SCHEDULE";

        // Time Configuration Commands
        public const string SET_TIME_CONFIG = "SET_TIME_CONFIG";
        public const string SET_TIME = "SET_TIME";
        public const string GET_TIME = "GET_TIME";
        public const string SYNC_TIME = "SYNC_TIME";

        // System Commands
        public const string SAVE_CONFIG = "SAVE_CONFIG";
        public const string LOAD_CONFIG = "LOAD_CONFIG";
        public const string FACTORY_RESET = "FACTORY_RESET";
        public const string RESTART = "RESTART";
        public const string GET_LOGS = "GET_LOGS";

        // Security Commands
        public const string ADD_USER = "ADD_USER";
        public const string REMOVE_USER = "REMOVE_USER";
        public const string LIST_USERS = "LIST_USERS";
        public const string SET_SECURITY = "SET_SECURITY";
    }

    /// <summary>
    /// Bluetooth command builder for type-safe command construction
    /// </summary>
    public static class BluetoothCommandBuilder
    {
        public static string Login(string username, string password)
            => $"{BluetoothCommands.LOGIN}:{username},{password}";

        public static string Logout()
            => BluetoothCommands.LOGOUT;

        public static string ChangePassword(string oldPassword, string newPassword)
            => $"{BluetoothCommands.CHANGE_PASSWORD}:{oldPassword},{newPassword}";

        public static string GetStatus()
            => BluetoothCommands.GET_STATUS;

        public static string GetDeviceInfo()
            => BluetoothCommands.GET_DEVICE_INFO;

        public static string SetDeviceName(string name)
            => $"{BluetoothCommands.SET_DEVICE_NAME}:{name}";

        public static string WiFiScan()
            => BluetoothCommands.WIFI_SCAN;

        public static string WiFiConnect(string ssid, string password)
            => $"{BluetoothCommands.WIFI_CONNECT}:{ssid},{password}";

        public static string WiFiDisconnect()
            => BluetoothCommands.WIFI_DISCONNECT;

        public static string WiFiStatus()
            => BluetoothCommands.WIFI_STATUS;

        public static string SetMotorType(MotorType motorType)
            => $"{BluetoothCommands.SET_MOTOR_TYPE}:{(int)motorType}";

        public static string SetStepperConfig(StepperConfig config)
            => $"{BluetoothCommands.SET_STEPPER_CONFIG}:{config.StepPin},{config.DirPin},{config.EnablePin},{config.StepsPerRev},{config.Microsteps},{config.MaxSpeedHz},{config.Acceleration}";

        public static string SetDcConfig(DcConfig config)
            => $"{BluetoothCommands.SET_DC_CONFIG}:{config.PwmPin},{config.DirPin},{config.EnablePin},{config.SensorPin},{config.PwmFrequency},{config.PwmDutyCycle},{config.RunTimeMs}";

        public static string TestMotor(uint stepsOrTime)
            => $"{BluetoothCommands.TEST_MOTOR}:{stepsOrTime}";

        public static string SetFeatures(OptionalFeatures features)
            => $"{BluetoothCommands.SET_FEATURES}:{(features.EnableFoodLevelSensor ? 1 : 0)},{(features.EnableLedIndicators ? 1 : 0)},{(features.EnableBuzzer ? 1 : 0)},{(features.EnableManualButton ? 1 : 0)}";

        public static string SetFeedingConfig(FeedingConfig config)
            => $"{BluetoothCommands.SET_FEEDING_CONFIG}:{config.DefaultPortionSteps},{config.MinPortionSteps},{config.MaxPortionSteps},{config.FeedingSpeed},{config.MaxFeedingsPerDay},{config.MinIntervalMinutes}";

        public static string ManualFeed(uint portionSize)
            => $"{BluetoothCommands.MANUAL_FEED}:{portionSize}";

        public static string GetFeedingHistory(int days)
            => $"{BluetoothCommands.GET_FEEDING_HISTORY}:{days}";

        public static string SetSchedule(FeedingScheduleEntry entry)
            => $"{BluetoothCommands.SET_SCHEDULE}:{entry.Hour},{entry.Minute},{entry.PortionSize},{(entry.Enabled ? 1 : 0)},{(int)entry.DaysOfWeek}";

        public static string GetSchedule()
            => BluetoothCommands.GET_SCHEDULE;

        public static string ClearSchedule()
            => BluetoothCommands.CLEAR_SCHEDULE;

        public static string SetTimeConfig(TimeConfig config)
            => $"{BluetoothCommands.SET_TIME_CONFIG}:{config.NtpServer},{config.Timezone},{(config.AutoSyncEnabled ? 1 : 0)},{config.SyncIntervalHours}";

        public static string SetTime(DateTime time)
        {
            var unixTime = ((DateTimeOffset)time).ToUnixTimeSeconds();
            return $"{BluetoothCommands.SET_TIME}:{unixTime}";
        }

        public static string GetTime()
            => BluetoothCommands.GET_TIME;

        public static string SyncTime()
            => BluetoothCommands.SYNC_TIME;

        public static string SaveConfig()
            => BluetoothCommands.SAVE_CONFIG;

        public static string LoadConfig()
            => BluetoothCommands.LOAD_CONFIG;

        public static string FactoryReset(string confirmation = "CONFIRM")
            => $"{BluetoothCommands.FACTORY_RESET}:{confirmation}";

        public static string Restart()
            => BluetoothCommands.RESTART;

        public static string GetLogs(int lines = 50)
            => $"{BluetoothCommands.GET_LOGS}:{lines}";

        public static string AddUser(string username, string password, UserLevel level)
            => $"{BluetoothCommands.ADD_USER}:{username},{password},{(int)level}";

        public static string RemoveUser(string username)
            => $"{BluetoothCommands.REMOVE_USER}:{username}";

        public static string ListUsers()
            => BluetoothCommands.LIST_USERS;

        public static string SetSecurity(SecurityConfig config)
            => $"{BluetoothCommands.SET_SECURITY}:{(config.RequireAuthentication ? 1 : 0)},{(config.AllowGuestAccess ? 1 : 0)},{config.SessionTimeoutMinutes},{config.BluetoothPin}";
    }

    /// <summary>
    /// Bluetooth response parser for handling device responses
    /// </summary>
    public static class BluetoothResponseParser
    {
        public static bool IsSuccess(string response)
            => response.Contains("OK") || response.Contains("SUCCESS");

        public static bool IsError(string response)
            => response.Contains("ERROR") || response.Contains("FAIL");

        public static string GetErrorMessage(string response)
        {
            if (response.StartsWith("ERROR:"))
                return response.Substring(6);
            if (response.StartsWith("FAIL:"))
                return response.Substring(5);
            return response;
        }

        public static Dictionary<string, string> ParseKeyValueResponse(string response)
        {
            var result = new Dictionary<string, string>();

            if (response.StartsWith("STATUS:") || response.StartsWith("INFO:"))
            {
                var jsonPart = response.Substring(response.IndexOf(':') + 1);
                // Simple JSON-like parsing for key:value pairs
                var pairs = jsonPart.Trim('{', '}').Split(',');

                foreach (var pair in pairs)
                {
                    var keyValue = pair.Split(':');
                    if (keyValue.Length == 2)
                    {
                        var key = keyValue[0].Trim().Trim('"');
                        var value = keyValue[1].Trim().Trim('"');
                        result[key] = value;
                    }
                }
            }

            return result;
        }

        public static List<string> ParseListResponse(string response)
        {
            var result = new List<string>();

            if (response.StartsWith("LIST:"))
            {
                var listPart = response.Substring(5);
                var items = listPart.Split(',');

                foreach (var item in items)
                {
                    result.Add(item.Trim());
                }
            }

            return result;
        }
    }

    /// <summary>
    /// Bluetooth communication event arguments
    /// </summary>
    public class BluetoothEventArgs : EventArgs
    {
        public BluetoothConnectionStatus Status { get; set; }
        public string Message { get; set; } = "";
        public string DeviceAddress { get; set; } = "";
        public string DeviceName { get; set; } = "";
        public Exception? Error { get; set; }
    }

    /// <summary>
    /// Bluetooth command event arguments
    /// </summary>
    public class BluetoothCommandEventArgs : EventArgs
    {
        public string Command { get; set; } = "";
        public string Response { get; set; } = "";
        public bool Success { get; set; }
        public TimeSpan Duration { get; set; }
        public Exception? Error { get; set; }
    }
}
