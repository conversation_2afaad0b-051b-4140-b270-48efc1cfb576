#include "motor_control.h"
#include "esp_log.h"
#include "driver/gpio.h"
#include "driver/ledc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

// Forward declarations for stepper motor functions
esp_err_t stepper_motor_init(void);
esp_err_t stepper_motor_start(void);
esp_err_t stepper_motor_stop(void);
esp_err_t stepper_motor_step(uint32_t steps, bool clockwise);
esp_err_t stepper_motor_run_duration(uint32_t duration_ms, bool clockwise);
bool stepper_motor_is_running(void);

// Forward declarations for DC motor functions
esp_err_t dc_motor_init(void);
esp_err_t dc_motor_start(void);
esp_err_t dc_motor_stop(void);
esp_err_t dc_motor_set_speed(uint8_t speed_percent);
esp_err_t dc_motor_set_direction(bool clockwise);
esp_err_t dc_motor_run_duration(uint32_t duration_ms, uint8_t speed_percent);
bool dc_motor_is_running(void);

static const char *TAG = "motor_control";

static motor_type_t current_motor_type = MOTOR_TYPE_DC;
static bool motor_initialized = false;

esp_err_t motor_control_init(void)
{
    // Initialize with default motor type (can be changed later)
    current_motor_type = MOTOR_TYPE_DC;

    esp_err_t ret = ESP_OK;

    // Initialize both motor types
    ret = stepper_motor_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize stepper motor: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = dc_motor_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize DC motor: %s", esp_err_to_name(ret));
        return ret;
    }

    motor_initialized = true;
    ESP_LOGI(TAG, "Motor control initialized successfully");

    return ESP_OK;
}

esp_err_t motor_control_set_type(motor_type_t motor_type)
{
    if (!motor_initialized) {
        ESP_LOGE(TAG, "Motor control not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    current_motor_type = motor_type;
    ESP_LOGI(TAG, "Motor type set to: %s",
             motor_type == MOTOR_TYPE_STEPPER ? "Stepper" : "DC");

    return ESP_OK;
}

esp_err_t motor_control_start(void)
{
    if (!motor_initialized) {
        ESP_LOGE(TAG, "Motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Starting motor (type: %s)",
             current_motor_type == MOTOR_TYPE_STEPPER ? "Stepper" : "DC");

    switch (current_motor_type) {
        case MOTOR_TYPE_STEPPER:
            return stepper_motor_start();
        case MOTOR_TYPE_DC:
            return dc_motor_start();
        default:
            ESP_LOGE(TAG, "Unknown motor type: %d", current_motor_type);
            return ESP_ERR_INVALID_ARG;
    }
}

esp_err_t motor_control_stop(void)
{
    if (!motor_initialized) {
        ESP_LOGE(TAG, "Motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Stopping motor");

    switch (current_motor_type) {
        case MOTOR_TYPE_STEPPER:
            return stepper_motor_stop();
        case MOTOR_TYPE_DC:
            return dc_motor_stop();
        default:
            ESP_LOGE(TAG, "Unknown motor type: %d", current_motor_type);
            return ESP_ERR_INVALID_ARG;
    }
}

esp_err_t motor_control_feed(uint8_t portion_size)
{
    if (!motor_initialized) {
        ESP_LOGE(TAG, "Motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Feeding portion size: %d", portion_size);

    // Calculate feeding duration based on portion size (100ms per unit)
    uint32_t duration_ms = portion_size * 100;

    switch (current_motor_type) {
        case MOTOR_TYPE_STEPPER:
            return stepper_motor_run_duration(duration_ms, true);
        case MOTOR_TYPE_DC:
            return dc_motor_run_duration(duration_ms, 75); // 75% speed
        default:
            ESP_LOGE(TAG, "Unknown motor type: %d", current_motor_type);
            return ESP_ERR_INVALID_ARG;
    }
}

esp_err_t motor_control_configure_stepper(const stepper_config_t *config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    current_motor_type = MOTOR_TYPE_STEPPER;
    ESP_LOGI(TAG, "Stepper motor configured");
    return ESP_OK;
}

esp_err_t motor_control_configure_dc(const dc_config_t *config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    current_motor_type = MOTOR_TYPE_DC;
    ESP_LOGI(TAG, "DC motor configured");
    return ESP_OK;
}

esp_err_t motor_control_get_status(motor_status_t *status)
{
    if (status == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    status->motor_type = current_motor_type;
    status->is_running = false; // Simplified for now
    status->current_speed = 0;
    status->direction = true;

    return ESP_OK;
}
