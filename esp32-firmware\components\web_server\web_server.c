#include "web_server.h"
#include "esp_http_server.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_system.h"
#include "version.h"
#include <stdio.h>

static const char *TAG = "web_server";
static httpd_handle_t server = NULL;

static esp_err_t root_get_handler(httpd_req_t *req)
{
    const char* html_template =
        "<!DOCTYPE html>"
        "<html><head>"
        "<title>Studio 311 LP - Smart Cat Feeder</title>"
        "<meta name='viewport' content='width=device-width, initial-scale=1'>"
        "<style>"
        "body{font-family:Arial,sans-serif;margin:0;padding:20px;background:#f5f5f5}"
        ".container{max-width:800px;margin:0 auto;background:white;padding:30px;border-radius:15px;box-shadow:0 4px 20px rgba(0,0,0,0.1)}"
        ".header{text-align:center;margin-bottom:30px;border-bottom:2px solid #007bff;padding-bottom:20px}"
        ".company{color:#007bff;font-size:24px;font-weight:bold;margin:0}"
        ".product{color:#666;font-size:18px;margin:5px 0 0 0}"
        ".version{color:#28a745;font-size:14px;margin:10px 0 0 0}"
        ".status-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px;margin:20px 0}"
        ".status-card{background:#f8f9fa;padding:20px;border-radius:10px;border-left:4px solid #007bff}"
        ".status-title{font-weight:bold;color:#333;margin-bottom:10px}"
        ".status-value{font-size:18px;color:#007bff}"
        ".status-ok{border-left-color:#28a745;} .status-ok .status-value{color:#28a745}"
        ".status-warning{border-left-color:#ffc107;} .status-warning .status-value{color:#ffc107}"
        ".status-error{border-left-color:#dc3545;} .status-error .status-value{color:#dc3545}"
        ".actions{margin-top:30px;text-align:center}"
        ".btn{display:inline-block;padding:12px 24px;margin:5px;background:#007bff;color:white;text-decoration:none;border-radius:5px;border:none;cursor:pointer}"
        ".btn:hover{background:#0056b3}"
        ".refresh{font-size:12px;color:#666;margin-top:20px;text-align:center}"
        "</style>"
        "<script>setTimeout(function(){location.reload()},30000);</script>"
        "</head><body>"
        "<div class='container'>"
        "<div class='header'>"
        "<h1 class='company'>Studio 311 LP</h1>"
        "<p class='product'>Smart Cat Feeder - ESP32 Controller</p>"
        "<p class='version'>Firmware v%s (Build %d) - Built %s %s</p>"
        "</div>"
        "<div class='status-grid'>"
        "<div class='status-card status-ok'>"
        "<div class='status-title'>System Status</div>"
        "<div class='status-value'>Online & Ready</div>"
        "</div>"
        "<div class='status-card status-ok'>"
        "<div class='status-title'>WiFi Connection</div>"
        "<div class='status-value'>Connected</div>"
        "</div>"
        "<div class='status-card'>"
        "<div class='status-title'>Free Memory</div>"
        "<div class='status-value'>%u bytes</div>"
        "</div>"
        "<div class='status-card'>"
        "<div class='status-title'>Uptime</div>"
        "<div class='status-value'>%lld seconds</div>"
        "</div>"
        "</div>"
        "<div class='actions'>"
        "<a href='/status' class='btn'>JSON Status</a>"
        "<a href='#' onclick='location.reload()' class='btn'>Refresh</a>"
        "</div>"
        "<div class='refresh'>Page auto-refreshes every 30 seconds</div>"
        "<p style='text-align:center;color:#666;margin-top:30px;'>"
        "Use the Studio 311 LP Smart Cat Feeder mobile app to control feeding schedules and monitor your pet's feeding history."
        "</p>"
        "</div></body></html>";

    char html_response[4096];  // Increased buffer size for larger HTML
    uint32_t free_heap = esp_get_free_heap_size();
    int64_t uptime = esp_timer_get_time() / 1000000;

    snprintf(html_response, sizeof(html_response), html_template,
        get_version_string(),
        get_version_build(),
        BUILD_DATE,
        BUILD_TIME,
        free_heap,
        uptime
    );

    httpd_resp_set_type(req, "text/html");
    httpd_resp_send(req, html_response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static esp_err_t status_get_handler(httpd_req_t *req)
{
    char resp_str[256];
    snprintf(resp_str, sizeof(resp_str),
        "{"
        "\"status\":\"ok\","
        "\"device\":\"CatFeeder_ESP32\","
        "\"firmware_version\":\"%s\","
        "\"version_major\":%d,"
        "\"version_minor\":%d,"
        "\"version_patch\":%d,"
        "\"version_build\":%d"
        "}",
        get_version_string(),
        get_version_major(),
        get_version_minor(),
        get_version_patch(),
        get_version_build()
    );

    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, resp_str, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static const httpd_uri_t root = {
    .uri       = "/",
    .method    = HTTP_GET,
    .handler   = root_get_handler,
    .user_ctx  = NULL
};

static const httpd_uri_t status = {
    .uri       = "/status",
    .method    = HTTP_GET,
    .handler   = status_get_handler,
    .user_ctx  = NULL
};

esp_err_t web_server_start(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.lru_purge_enable = true;

    ESP_LOGI(TAG, "Starting server on port: '%d'", config.server_port);
    if (httpd_start(&server, &config) == ESP_OK) {
        ESP_LOGI(TAG, "Registering URI handlers");
        httpd_register_uri_handler(server, &root);
        httpd_register_uri_handler(server, &status);
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Error starting server!");
    return ESP_FAIL;
}

esp_err_t web_server_stop(void)
{
    if (server) {
        httpd_stop(server);
        server = NULL;
    }
    return ESP_OK;
}
