using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace CatFeeder.Models
{
    /// <summary>
    /// Motor types supported by the device
    /// </summary>
    public enum MotorType
    {
        Stepper,
        DcTime,
        DcSensor
    }

    /// <summary>
    /// Device connection status
    /// </summary>
    public enum DeviceStatus
    {
        Disconnected,
        Connecting,
        Connected,
        Error
    }



    /// <summary>
    /// Device configuration model
    /// </summary>
    public class DeviceConfig
    {
        public MotorType MotorType { get; set; }
        public StepperConfig StepperConfig { get; set; } = new();
        public DcConfig DcConfig { get; set; } = new();
        public OptionalFeatures Features { get; set; } = new();
        public FeedingConfig FeedingConfig { get; set; } = new();
        public WiFiConfig WiFiConfig { get; set; } = new();
        public BluetoothConfig BluetoothConfig { get; set; } = new();
        public uint ConfigVersion { get; set; }
    }

    /// <summary>
    /// Stepper motor configuration
    /// </summary>
    public class StepperConfig
    {
        [Range(0, 40)]
        public int StepPin { get; set; } = 2;
        
        [Range(0, 40)]
        public int DirPin { get; set; } = 4;
        
        [Range(-1, 40)]
        public int EnablePin { get; set; } = -1;
        
        [Range(100, 10000)]
        public uint StepsPerRev { get; set; } = 200;
        
        [Range(1, 32)]
        public uint Microsteps { get; set; } = 1;
        
        [Range(1, 10000)]
        public uint MaxSpeedHz { get; set; } = 1000;
        
        [Range(1, 50000)]
        public uint Acceleration { get; set; } = 500;
        
        public bool InvertDirection { get; set; } = false;
        public bool InvertEnable { get; set; } = false;
    }

    /// <summary>
    /// DC motor configuration
    /// </summary>
    public class DcConfig
    {
        [Range(0, 40)]
        public int PwmPin { get; set; } = 2;
        
        [Range(-1, 40)]
        public int DirPin { get; set; } = -1;
        
        [Range(-1, 40)]
        public int EnablePin { get; set; } = -1;
        
        [Range(-1, 40)]
        public int SensorPin { get; set; } = -1;
        
        [Range(100, 20000)]
        public uint PwmFrequency { get; set; } = 1000;
        
        [Range(0, 100)]
        public uint PwmDutyCycle { get; set; } = 80;
        
        [Range(100, 30000)]
        public uint RunTimeMs { get; set; } = 2000;
        
        public bool InvertDirection { get; set; } = false;
        public bool InvertEnable { get; set; } = false;
        public bool SensorActiveLow { get; set; } = true;
    }

    /// <summary>
    /// Optional features configuration
    /// </summary>
    public class OptionalFeatures
    {
        public bool EnableFoodLevelSensor { get; set; } = false;
        public bool EnableLedIndicators { get; set; } = false;
        public bool EnableBuzzer { get; set; } = false;
        public bool EnableManualButton { get; set; } = false;
    }

    /// <summary>
    /// Feeding configuration
    /// </summary>
    public class FeedingConfig
    {
        [Range(1, 10000)]
        public uint DefaultPortionSteps { get; set; } = 200;
        
        [Range(1, 1000)]
        public uint MinPortionSteps { get; set; } = 50;
        
        [Range(100, 50000)]
        public uint MaxPortionSteps { get; set; } = 2000;
        
        [Range(1, 5000)]
        public uint FeedingSpeed { get; set; } = 500;
        
        [Range(1, 20)]
        public uint MaxFeedingsPerDay { get; set; } = 8;
        
        [Range(30, 1440)]
        public uint MinIntervalMinutes { get; set; } = 120;
    }

    /// <summary>
    /// WiFi configuration
    /// </summary>
    public class WiFiConfig
    {
        [Required]
        [StringLength(31, MinimumLength = 1)]
        public string Ssid { get; set; } = "";
        
        [StringLength(63)]
        public string Password { get; set; } = "";
        
        public bool AutoConnect { get; set; } = true;
        
        [Range(5000, 60000)]
        public uint ConnectionTimeoutMs { get; set; } = 30000;
    }

    /// <summary>
    /// Bluetooth configuration
    /// </summary>
    public class BluetoothConfig
    {
        [Required]
        [StringLength(31, MinimumLength = 1)]
        public string DeviceName { get; set; } = "CatFeeder";
        
        public bool EnableOnStartup { get; set; } = true;
        public bool AllowConfigViaBt { get; set; } = true;
    }

    /// <summary>
    /// Device status information
    /// </summary>
    public class DeviceStatusInfo
    {
        public DeviceStatus Status { get; set; }
        public bool MotorReady { get; set; }
        public uint FeedingsToday { get; set; }
        public DateTime LastFeedingTime { get; set; }
        public float? FoodLevelPercent { get; set; }  // Make sure this property exists and is nullable
        public FeedingResult LastResult { get; set; }
        public string? ConnectedSsid { get; set; }
        public string? IpAddress { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Manual feeding request
    /// </summary>
    public class FeedingRequest
    {
        [Range(1, 50000)]
        public uint PortionSize { get; set; }
        
        public bool IsManual { get; set; } = true;
        public DateTime RequestTime { get; set; } = DateTime.Now;
    }



    /// <summary>
    /// Device discovery information
    /// </summary>
    public class DiscoveredDevice
    {
        public string Name { get; set; } = "";
        public string IpAddress { get; set; } = "";
        public string MacAddress { get; set; } = "";
        public int SignalStrength { get; set; }
        public DateTime LastSeen { get; set; }
        public bool IsOnline { get; set; }
        public bool IsAuthenticated { get; set; }
        public string DeviceId { get; set; } = "";
        public string FirmwareVersion { get; set; } = "";
    }

    /// <summary>
    /// User authentication model
    /// </summary>
    public class UserCredentials
    {
        [Required]
        [StringLength(31, MinimumLength = 3)]
        public string Username { get; set; } = "";

        [Required]
        [StringLength(63, MinimumLength = 6)]
        public string Password { get; set; } = "";

        public bool RememberMe { get; set; } = false;
    }

    /// <summary>
    /// User session information
    /// </summary>
    public class UserSession
    {
        public string Username { get; set; } = "";
        public UserLevel Level { get; set; }
        public DateTime LoginTime { get; set; }
        public DateTime LastActivity { get; set; }
        public bool IsBluetoothSession { get; set; }
        public string DeviceAddress { get; set; } = "";
        public TimeSpan SessionTimeout { get; set; }
    }

    /// <summary>
    /// User privilege levels
    /// </summary>
    public enum UserLevel
    {
        Guest = 0,
        User = 1,
        Admin = 2
    }

    /// <summary>
    /// Time synchronization configuration
    /// </summary>
    public class TimeConfig
    {
        [Required]
        [StringLength(63, MinimumLength = 1)]
        public string NtpServer { get; set; } = "pool.ntp.org";

        [Required]
        [StringLength(31, MinimumLength = 1)]
        public string Timezone { get; set; } = "UTC0";

        public bool AutoSyncEnabled { get; set; } = true;

        [Range(1, 168)]
        public uint SyncIntervalHours { get; set; } = 24;
    }

    /// <summary>
    /// Security configuration
    /// </summary>
    public class SecurityConfig
    {
        public bool RequireAuthentication { get; set; } = true;
        public bool AllowGuestAccess { get; set; } = false;

        [Range(5, 1440)]
        public uint SessionTimeoutMinutes { get; set; } = 30;

        [Required]
        [StringLength(7, MinimumLength = 4)]
        public string BluetoothPin { get; set; } = "1234";

        public string DeviceId { get; set; } = "";
    }

    /// <summary>
    /// Complete device configuration including all settings
    /// </summary>
    public class CompleteDeviceConfig
    {
        public DeviceConfig DeviceConfig { get; set; } = new();
        public TimeConfig TimeConfig { get; set; } = new();
        public SecurityConfig SecurityConfig { get; set; } = new();
        public uint ConfigVersion { get; set; }
        public DateTime LastModified { get; set; }
        public bool FactoryResetFlag { get; set; }
    }

    /// <summary>
    /// Device command request
    /// </summary>
    public class DeviceCommand
    {
        public string Command { get; set; } = "";
        public string Parameters { get; set; } = "";
        public string Username { get; set; } = "";
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public int TimeoutSeconds { get; set; } = 10;
    }

    /// <summary>
    /// Device command response
    /// </summary>
    public class DeviceCommandResponse
    {
        public bool Success { get; set; }
        public string Response { get; set; } = "";
        public string ErrorMessage { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public TimeSpan Duration { get; set; }
    }

    /// <summary>
    /// WiFi network information
    /// </summary>
    public class WiFiNetwork
    {
        public string Ssid { get; set; } = "";
        public int SignalStrength { get; set; }
        public string Security { get; set; } = "";
        public int Channel { get; set; }
        public bool IsConnected { get; set; }
        public bool IsHidden { get; set; }
    }

    /// <summary>
    /// System information
    /// </summary>
    public class SystemInfo
    {
        public string ChipModel { get; set; } = "";
        public string FirmwareVersion { get; set; } = "";
        public uint FreeHeap { get; set; }
        public uint TotalHeap { get; set; }
        public TimeSpan Uptime { get; set; }
        public DateTime CurrentTime { get; set; }
        public bool TimeIsSynchronized { get; set; }
        public DateTime LastTimeSyncTime { get; set; }
        public float CpuTemperature { get; set; }
    }
}

