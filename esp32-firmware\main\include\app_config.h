#pragma once

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_FEEDING_SCHEDULES 10
#define MAX_DEVICE_NAME_LEN 32
#define MAX_BLUETOOTH_PIN_LEN 8
#define MAX_WIFI_SSID_LEN 32
#define MAX_WIFI_PASSWORD_LEN 64

typedef enum {
    MOTOR_TYPE_STEPPER = 0,
    MOTOR_TYPE_DC = 1,
    MOTOR_TYPE_SERVO = 2
} motor_type_t;

typedef struct {
    uint8_t hour;
    uint8_t minute;
    uint8_t portion_size;
    bool enabled;
} feeding_schedule_t;

typedef struct {
    char device_name[MAX_DEVICE_NAME_LEN];
    char bluetooth_pin[MAX_BLUETOOTH_PIN_LEN];
    char wifi_ssid[MAX_WIFI_SSID_LEN];
    char wifi_password[MAX_WIFI_PASSWORD_LEN];
    motor_type_t motor_type;
    feeding_schedule_t feeding_schedules[MAX_FEEDING_SCHEDULES];
    uint8_t feeding_schedules_count;
    bool level_sensor_enabled;
    bool led_indicators_enabled;
    bool auto_feed_enabled;
    uint8_t manual_feed_portion_size;
} app_config_t;

/**
 * @brief Initialize application configuration
 * @return ESP_OK on success
 */
esp_err_t app_config_init(void);

/**
 * @brief Get current application configuration
 * @return Pointer to current configuration
 */
app_config_t* app_config_get(void);

/**
 * @brief Set device name
 * @param name Device name string
 * @return ESP_OK on success
 */
esp_err_t app_config_set_device_name(const char* name);

/**
 * @brief Set Bluetooth PIN
 * @param pin PIN string
 * @return ESP_OK on success
 */
esp_err_t app_config_set_bluetooth_pin(const char* pin);

/**
 * @brief Set WiFi credentials
 * @param ssid WiFi SSID
 * @param password WiFi password
 * @return ESP_OK on success
 */
esp_err_t app_config_set_wifi_credentials(const char* ssid, const char* password);

/**
 * @brief Set motor type
 * @param motor_type Type of motor
 * @return ESP_OK on success
 */
esp_err_t app_config_set_motor_type(motor_type_t motor_type);

/**
 * @brief Add feeding schedule
 * @param hour Hour (0-23)
 * @param minute Minute (0-59)
 * @param portion_size Portion size (1-20)
 * @return ESP_OK on success
 */
esp_err_t app_config_add_feeding_schedule(uint8_t hour, uint8_t minute, uint8_t portion_size);

/**
 * @brief Remove feeding schedule
 * @param hour Hour (0-23)
 * @param minute Minute (0-59)
 * @return ESP_OK on success
 */
esp_err_t app_config_remove_feeding_schedule(uint8_t hour, uint8_t minute);

/**
 * @brief Clear all feeding schedules
 * @return ESP_OK on success
 */
esp_err_t app_config_clear_feeding_schedules(void);

/**
 * @brief Enable/disable level sensor
 * @param enabled true to enable, false to disable
 * @return ESP_OK on success
 */
esp_err_t app_config_set_level_sensor_enabled(bool enabled);

/**
 * @brief Enable/disable LED indicators
 * @param enabled true to enable, false to disable
 * @return ESP_OK on success
 */
esp_err_t app_config_set_led_indicators_enabled(bool enabled);

/**
 * @brief Enable/disable auto feeding
 * @param enabled true to enable, false to disable
 * @return ESP_OK on success
 */
esp_err_t app_config_set_auto_feed_enabled(bool enabled);

/**
 * @brief Set manual feed portion size
 * @param portion_size Portion size (1-20)
 * @return ESP_OK on success
 */
esp_err_t app_config_set_manual_feed_portion_size(uint8_t portion_size);

#ifdef __cplusplus
}
#endif
