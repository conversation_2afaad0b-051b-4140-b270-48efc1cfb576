@echo off
echo ========================================
echo ESP32 Cat Feeder - Firmware Upload to COM3
echo ========================================
echo.

:: Set ESP-IDF path
set IDF_PATH=C:\Espressif\frameworks\esp-idf-v5.4.1
set IDF_TOOLS_PATH=C:\Espressif

echo Setting up ESP-IDF environment...
echo ESP-IDF Path: %IDF_PATH%
echo.

:: Check if ESP-IDF exists
if not exist "%IDF_PATH%\export.bat" (
    echo ERROR: ESP-IDF not found at %IDF_PATH%
    echo Please verify the ESP-IDF installation path.
    pause
    exit /b 1
)

:: Run ESP-IDF export script to set environment
echo [1/5] Setting up ESP-IDF environment...
call "%IDF_PATH%\export.bat"
if %errorLevel% neq 0 (
    echo ERROR: Failed to setup ESP-IDF environment
    pause
    exit /b 1
)

:: Verify idf.py is available
echo.
echo [2/5] Verifying ESP-IDF installation...
idf.py --version
if %errorLevel% neq 0 (
    echo ERROR: idf.py not available after environment setup
    pause
    exit /b 1
)

:: Set target to ESP32
echo.
echo [3/5] Setting target to ESP32...
idf.py set-target esp32
if %errorLevel% neq 0 (
    echo ERROR: Failed to set target
    pause
    exit /b 1
)

:: Build the project
echo.
echo [4/5] Building Cat Feeder firmware...
idf.py build
if %errorLevel% neq 0 (
    echo ERROR: Build failed
    echo.
    echo Common issues:
    echo - Missing component implementations
    echo - Bluetooth configuration issues
    echo - Include path problems
    echo.
    echo Check the error messages above for details.
    pause
    exit /b 1
)

:: Flash to COM3
echo.
echo [5/5] Flashing to COM3...
idf.py -p COM3 flash
if %errorLevel% neq 0 (
    echo ERROR: Flash failed
    echo Check if:
    echo - ESP32 is connected to COM3
    echo - No other programs are using the port
    echo - ESP32 is in download mode (hold BOOT button while pressing RESET)
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS: Firmware uploaded to COM3!
echo ========================================
echo.
echo Device Name: CatFeeder_ESP32
echo Bluetooth PIN: 1234
echo.
echo Available Commands:
echo - PING
echo - GET_STATUS
echo - GET_DEVICE_INFO
echo - MANUAL_FEED:portion_size
echo - SET_MOTOR_TYPE:0^|1^|2
echo - WIFI_CONNECT:ssid,password
echo - GET_TIME
echo - GET_FEEDING_STATS
echo - SYNC_FEEDING_HISTORY:last_id,max_entries
echo - GET_RECENT_FEEDINGS:count
echo - RESTART
echo - ECHO:message
echo.
echo To monitor output:
echo   idf.py -p COM3 monitor
echo.
echo Now test with the MAUI Android app!
echo.
pause
