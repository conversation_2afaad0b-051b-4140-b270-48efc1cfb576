/**
 * Time Synchronization Module
 * 
 * Handles NTP time synchronization and RTC management
 */

#pragma once

#include "esp_err.h"
#include <time.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

#define DEFAULT_NTP_SERVER "pool.ntp.org"
#define BACKUP_NTP_SERVER "time.google.com"
#define TIME_SYNC_INTERVAL_HOURS 24
#define TIMEZONE_MAX_LENGTH 32

/**
 * Time sync status
 */
typedef enum {
    TIME_SYNC_STATUS_NOT_STARTED,
    TIME_SYNC_STATUS_IN_PROGRESS,
    TIME_SYNC_STATUS_SUCCESS,
    TIME_SYNC_STATUS_FAILED
} time_sync_status_t;

/**
 * Time configuration
 */
typedef struct {
    char ntp_server[64];            ///< Primary NTP server
    char backup_ntp_server[64];     ///< Backup NTP server
    char timezone[TIMEZONE_MAX_LENGTH]; ///< Timezone string (e.g., "EST5EDT,M3.2.0/2,M11.1.0")
    uint32_t sync_interval_hours;   ///< Sync interval in hours
    bool auto_sync_enabled;         ///< Enable automatic synchronization
    bool dst_enabled;               ///< Daylight saving time enabled
} time_config_t;

/**
 * Time sync callback function type
 */
typedef void (*time_sync_callback_t)(time_sync_status_t status, time_t current_time);

/**
 * Initialize time synchronization system
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_init(void);

/**
 * Start time synchronization
 * 
 * @param callback Callback function to call when sync completes (optional)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_start(time_sync_callback_t callback);

/**
 * Stop time synchronization
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_stop(void);

/**
 * Force immediate time synchronization
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_force_update(void);

/**
 * Get current time sync status
 * 
 * @return Current sync status
 */
time_sync_status_t time_sync_get_status(void);

/**
 * Check if time is synchronized
 * 
 * @return true if time is synchronized
 */
bool time_sync_is_synchronized(void);

/**
 * Get last sync time
 * 
 * @return Unix timestamp of last successful sync, 0 if never synced
 */
time_t time_sync_get_last_sync_time(void);

/**
 * Get current time
 * 
 * @param current_time Pointer to store current time
 * @return ESP_OK if time is valid, ESP_ERR_INVALID_STATE if not synchronized
 */
esp_err_t time_sync_get_current_time(time_t* current_time);

/**
 * Get current time as formatted string
 * 
 * @param buffer Buffer to store formatted time
 * @param buffer_size Size of buffer
 * @param format Format string (strftime format)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_get_time_string(char* buffer, size_t buffer_size, const char* format);

/**
 * Set time configuration
 * 
 * @param config Time configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_set_config(const time_config_t* config);

/**
 * Get time configuration
 * 
 * @param config Pointer to store configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_get_config(time_config_t* config);

/**
 * Set timezone
 * 
 * @param timezone Timezone string (POSIX format)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_set_timezone(const char* timezone);

/**
 * Get timezone
 * 
 * @param timezone Buffer to store timezone string
 * @param buffer_size Size of buffer
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_get_timezone(char* timezone, size_t buffer_size);

/**
 * Convert time to local time
 * 
 * @param utc_time UTC time
 * @param local_time Pointer to store local time
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_utc_to_local(time_t utc_time, struct tm* local_time);

/**
 * Convert local time to UTC
 * 
 * @param local_time Local time
 * @param utc_time Pointer to store UTC time
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_local_to_utc(const struct tm* local_time, time_t* utc_time);

/**
 * Check if it's currently daylight saving time
 * 
 * @return true if DST is active
 */
bool time_sync_is_dst_active(void);

/**
 * Get time until next sync
 * 
 * @return Seconds until next automatic sync, 0 if auto sync disabled
 */
uint32_t time_sync_get_time_until_next_sync(void);

/**
 * Set manual time (when NTP is not available)
 * 
 * @param manual_time Time to set
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_set_manual_time(time_t manual_time);

/**
 * Get uptime since boot
 * 
 * @return Uptime in seconds
 */
uint32_t time_sync_get_uptime(void);

/**
 * Format uptime as human-readable string
 * 
 * @param buffer Buffer to store formatted uptime
 * @param buffer_size Size of buffer
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t time_sync_get_uptime_string(char* buffer, size_t buffer_size);

/**
 * Common timezone definitions
 */
#define TIMEZONE_UTC "UTC0"
#define TIMEZONE_EST "EST5EDT,M3.2.0/2,M11.1.0"
#define TIMEZONE_PST "PST8PDT,M3.2.0/2,M11.1.0"
#define TIMEZONE_CET "CET-1CEST,M3.5.0,M10.5.0/3"
#define TIMEZONE_JST "JST-9"
#define TIMEZONE_AEST "AEST-10AEDT,M10.1.0,M4.1.0/3"

#ifdef __cplusplus
}
#endif
