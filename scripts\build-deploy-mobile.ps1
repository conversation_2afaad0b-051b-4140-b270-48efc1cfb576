# Studio 311 LP - Smart Cat Feeder Mobile App Build & Deploy Script
# Builds and deploys the mobile app to connected Android device

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Debug",

    [Parameter(Mandatory=$false)]
    [switch]$IncrementVersion,

    [Parameter(Mandatory=$false)]
    [switch]$SkipDeploy,

    [Parameter(Mandatory=$false)]
    [switch]$StartApp,

    [Parameter(Mandatory=$false)]
    [switch]$ShowLogs
)

$ErrorActionPreference = "Stop"

# Colors for output
$ColorInfo = "Cyan"
$ColorSuccess = "Green"
$ColorWarning = "Yellow"
$ColorError = "Red"

# Get script directory and project paths
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$MobileProjectPath = Join-Path $ProjectRoot "mobile-app\CatFeeder.Mobile"
$ProjectFile = Join-Path $MobileProjectPath "CatFeeder.Mobile.csproj"

Write-Host "🚀 Studio 311 LP - Smart Cat Feeder Mobile Build & Deploy" -ForegroundColor $ColorInfo
Write-Host "Configuration: $Configuration" -ForegroundColor $ColorInfo
Write-Host "Project: $MobileProjectPath" -ForegroundColor $ColorInfo
Write-Host ""

# Check if Android device is connected
Write-Host "📱 Checking Android device connection..." -ForegroundColor $ColorInfo
try {
    $devices = adb devices
    $connectedDevices = $devices | Select-String "device$" | Measure-Object

    if ($connectedDevices.Count -eq 0) {
        Write-Host "❌ No Android device connected!" -ForegroundColor $ColorError
        Write-Host "Please connect your Android device and enable USB debugging." -ForegroundColor $ColorWarning
        exit 1
    }

    Write-Host "✅ Android device connected" -ForegroundColor $ColorSuccess
} catch {
    Write-Host "❌ ADB not found or not working!" -ForegroundColor $ColorError
    Write-Host "Please ensure Android SDK is installed and ADB is in PATH." -ForegroundColor $ColorWarning
    exit 1
}

# Increment version if requested
if ($IncrementVersion) {
    Write-Host "📊 Incrementing version..." -ForegroundColor $ColorInfo
    try {
        & "$ScriptDir\increment-version.ps1" -Target mobile -Type patch
        Write-Host "✅ Version incremented" -ForegroundColor $ColorSuccess
    } catch {
        Write-Host "⚠️ Version increment failed: $($_.Exception.Message)" -ForegroundColor $ColorWarning
        Write-Host "Continuing with current version..." -ForegroundColor $ColorWarning
    }
}

# Build the project
Write-Host "🔨 Building mobile app..." -ForegroundColor $ColorInfo
Write-Host "Target: net9.0-android | Configuration: $Configuration" -ForegroundColor $ColorInfo

try {
    Push-Location $MobileProjectPath
    
    # Clean first
    Write-Host "🧹 Cleaning previous build..." -ForegroundColor $ColorInfo
    dotnet clean -c $Configuration | Out-Null
    
    # Build and deploy
    if ($SkipDeploy) {
        Write-Host "🔨 Building APK only..." -ForegroundColor $ColorInfo
        dotnet build -f net9.0-android -c $Configuration
    } else {
        Write-Host "🔨 Building and deploying to device..." -ForegroundColor $ColorInfo
        dotnet build -f net9.0-android -c $Configuration -t:Install
    }
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed with exit code $LASTEXITCODE"
    }
    
    Write-Host "✅ Build completed successfully!" -ForegroundColor $ColorSuccess
    
} catch {
    Write-Host "❌ Build failed: $($_.Exception.Message)" -ForegroundColor $ColorError
    Pop-Location
    exit 1
} finally {
    Pop-Location
}

# Start app if requested
if ($StartApp -and -not $SkipDeploy) {
    Write-Host "🚀 Starting app on device..." -ForegroundColor $ColorInfo
    try {
        adb shell monkey -p com.studio311lp.catfeeder -c android.intent.category.LAUNCHER 1 | Out-Null
        Start-Sleep -Seconds 2
        
        # Check if app is running
        $runningApp = adb shell ps | Select-String "com.studio311lp.catfeeder"
        if ($runningApp) {
            Write-Host "✅ App started successfully!" -ForegroundColor $ColorSuccess
            Write-Host "Process: $($runningApp.ToString().Trim())" -ForegroundColor $ColorInfo
        } else {
            Write-Host "⚠️ App may not have started properly" -ForegroundColor $ColorWarning
        }
    } catch {
        Write-Host "⚠️ Failed to start app: $($_.Exception.Message)" -ForegroundColor $ColorWarning
    }
}

# Show logs if requested
if ($ShowLogs) {
    Write-Host "📋 Showing app logs (Ctrl+C to stop)..." -ForegroundColor $ColorInfo
    Write-Host "Filtering for: catfeeder, CatFeeder, Studio311LP" -ForegroundColor $ColorInfo
    try {
        adb logcat | Select-String "catfeeder|CatFeeder|Studio311LP" -Context 1
    } catch {
        Write-Host "⚠️ Log monitoring stopped" -ForegroundColor $ColorWarning
    }
}

# Show APK information
if ($SkipDeploy) {
    Write-Host ""
    Write-Host "📦 APK Information:" -ForegroundColor $ColorInfo
    $apkPath = Join-Path $MobileProjectPath "bin\$Configuration\net9.0-android"
    if (Test-Path $apkPath) {
        Get-ChildItem -Path $apkPath -Name "*.apk" | ForEach-Object {
            $apkFile = Join-Path $apkPath $_
            $apkInfo = Get-ItemProperty $apkFile
            Write-Host "File: $($apkInfo.Name)" -ForegroundColor $ColorSuccess
            Write-Host "Size: $([math]::Round($apkInfo.Length/1MB,2)) MB" -ForegroundColor $ColorInfo
            Write-Host "Modified: $($apkInfo.LastWriteTime)" -ForegroundColor $ColorInfo
            Write-Host "Path: $apkFile" -ForegroundColor $ColorInfo
        }
    }
}

Write-Host ""
Write-Host "🎉 Mobile app build and deploy completed!" -ForegroundColor $ColorSuccess
Write-Host "Studio 311 LP - Smart Cat Feeder v1.0.2+" -ForegroundColor $ColorInfo
