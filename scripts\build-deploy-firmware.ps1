# Studio 311 LP - Smart Cat Feeder ESP32 Firmware Build & Deploy Script
# Builds and deploys firmware to ESP32 device

param(
    [Parameter(Mandatory=$false)]
    [string]$Port = "auto",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("esp32", "esp32s2", "esp32s3", "esp32c3")]
    [string]$Target = "esp32",
    
    [Parameter(Mandatory=$false)]
    [switch]$IncrementVersion,
    
    [Parameter(Mandatory=$false)]
    [switch]$SkipDeploy,
    
    [Parameter(Mandatory=$false)]
    [switch]$Monitor,
    
    [Parameter(Mandatory=$false)]
    [switch]$Erase,
    
    [Parameter(Mandatory=$false)]
    [switch]$MenuConfig
)

$ErrorActionPreference = "Stop"

# Colors for output
$ColorInfo = "Cyan"
$ColorSuccess = "Green"
$ColorWarning = "Yellow"
$ColorError = "Red"

# Get script directory and project paths
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$FirmwareProjectPath = Join-Path $ProjectRoot "esp32-firmware"

Write-Host "🔧 Studio 311 LP - Smart Cat Feeder ESP32 Firmware Build and Deploy" -ForegroundColor $ColorInfo
Write-Host "Target: $Target | Port: $Port" -ForegroundColor $ColorInfo
Write-Host "Project: $FirmwareProjectPath" -ForegroundColor $ColorInfo
Write-Host ""

# Check if ESP-IDF is available
Write-Host "🔍 Checking ESP-IDF environment..." -ForegroundColor $ColorInfo
try {
    $idfPath = $env:IDF_PATH
    if (-not $idfPath -or -not (Test-Path $idfPath)) {
        Write-Host "❌ ESP-IDF not found!" -ForegroundColor $ColorError
        Write-Host "Please install ESP-IDF and set IDF_PATH environment variable." -ForegroundColor $ColorWarning
        Write-Host "Visit: https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/" -ForegroundColor $ColorInfo
        exit 1
    }
    
    Write-Host "✅ ESP-IDF found at: $idfPath" -ForegroundColor $ColorSuccess
} catch {
    Write-Host "❌ ESP-IDF environment check failed!" -ForegroundColor $ColorError
    exit 1
}

# Check if firmware project exists
if (-not (Test-Path $FirmwareProjectPath)) {
    Write-Host "❌ Firmware project not found at: $FirmwareProjectPath" -ForegroundColor $ColorError
    Write-Host "Creating basic ESP32 firmware project..." -ForegroundColor $ColorInfo
    
    try {
        New-Item -ItemType Directory -Path $FirmwareProjectPath -Force | Out-Null
        
        # Create basic CMakeLists.txt
        $cmakeContent = @"
cmake_minimum_required(VERSION 3.16)

include(`$ENV{IDF_PATH}/tools/cmake/project.cmake)
project(catfeeder-firmware)
"@
        Set-Content -Path (Join-Path $FirmwareProjectPath "CMakeLists.txt") -Value $cmakeContent
        
        # Create main directory and files
        $mainDir = Join-Path $FirmwareProjectPath "main"
        New-Item -ItemType Directory -Path $mainDir -Force | Out-Null
        
        $mainCmake = @"
idf_component_register(SRCS "main.c"
                    INCLUDE_DIRS ".")
"@
        Set-Content -Path (Join-Path $mainDir "CMakeLists.txt") -Value $mainCmake
        
        Write-Host "✅ Basic firmware project created" -ForegroundColor $ColorSuccess
    } catch {
        Write-Host "❌ Failed to create firmware project: $($_.Exception.Message)" -ForegroundColor $ColorError
        exit 1
    }
}

# Increment version if requested
if ($IncrementVersion) {
    Write-Host "📊 Incrementing firmware version..." -ForegroundColor $ColorInfo
    try {
        & "$ScriptDir\increment-version.ps1" -Target firmware -Type patch
        Write-Host "✅ Version incremented" -ForegroundColor $ColorSuccess
    } catch {
        Write-Host "⚠️ Version increment failed: $($_.Exception.Message)" -ForegroundColor $ColorWarning
        Write-Host "Continuing with current version..." -ForegroundColor $ColorWarning
    }
}

# Change to firmware project directory
try {
    Push-Location $FirmwareProjectPath
    
    # Set target
    Write-Host "🎯 Setting ESP32 target: $Target" -ForegroundColor $ColorInfo
    idf.py set-target $Target
    
    # Menu config if requested
    if ($MenuConfig) {
        Write-Host "⚙️ Opening menuconfig..." -ForegroundColor $ColorInfo
        idf.py menuconfig
    }
    
    # Erase flash if requested
    if ($Erase) {
        Write-Host "🗑️ Erasing ESP32 flash..." -ForegroundColor $ColorInfo
        if ($Port -eq "auto") {
            idf.py erase-flash
        } else {
            idf.py -p $Port erase-flash
        }
        Write-Host "✅ Flash erased" -ForegroundColor $ColorSuccess
    }
    
    # Build firmware
    Write-Host "🔨 Building ESP32 firmware..." -ForegroundColor $ColorInfo
    idf.py build
    
    if ($LASTEXITCODE -ne 0) {
        throw "Firmware build failed with exit code $LASTEXITCODE"
    }
    
    Write-Host "✅ Firmware build completed successfully!" -ForegroundColor $ColorSuccess
    
    # Deploy firmware if not skipped
    if (-not $SkipDeploy) {
        Write-Host "📡 Deploying firmware to ESP32..." -ForegroundColor $ColorInfo
        
        if ($Port -eq "auto") {
            idf.py flash
        } else {
            idf.py -p $Port flash
        }
        
        if ($LASTEXITCODE -ne 0) {
            throw "Firmware deployment failed with exit code $LASTEXITCODE"
        }
        
        Write-Host "✅ Firmware deployed successfully!" -ForegroundColor $ColorSuccess
    }
    
    # Monitor if requested
    if ($Monitor -and -not $SkipDeploy) {
        Write-Host "📋 Starting serial monitor (Ctrl+C to stop)..." -ForegroundColor $ColorInfo
        Write-Host "Monitoring ESP32 output..." -ForegroundColor $ColorInfo
        
        if ($Port -eq "auto") {
            idf.py monitor
        } else {
            idf.py -p $Port monitor
        }
    }
    
} catch {
    Write-Host "❌ Firmware operation failed: $($_.Exception.Message)" -ForegroundColor $ColorError
    Pop-Location
    exit 1
} finally {
    Pop-Location
}

# Show build information
Write-Host ""
Write-Host "📦 Firmware Build Information:" -ForegroundColor $ColorInfo
$buildDir = Join-Path $FirmwareProjectPath "build"
if (Test-Path $buildDir) {
    $binFiles = Get-ChildItem -Path $buildDir -Name "*.bin"
    foreach ($binFile in $binFiles) {
        $binPath = Join-Path $buildDir $binFile
        $binInfo = Get-ItemProperty $binPath
        Write-Host "File: $($binInfo.Name)" -ForegroundColor $ColorSuccess
        Write-Host "Size: $([math]::Round($binInfo.Length/1KB,2)) KB" -ForegroundColor $ColorInfo
        Write-Host "Modified: $($binInfo.LastWriteTime)" -ForegroundColor $ColorInfo
    }
}

Write-Host ""
Write-Host "🎉 ESP32 firmware build and deploy completed!" -ForegroundColor $ColorSuccess
Write-Host "Studio 311 LP - Smart Cat Feeder Firmware v1.0.0+" -ForegroundColor $ColorInfo

# Show next steps
Write-Host ""
Write-Host "💡 Next Steps:" -ForegroundColor $ColorInfo
Write-Host "• Use -Monitor to see ESP32 serial output" -ForegroundColor $ColorWarning
Write-Host "• Use -MenuConfig to configure ESP32 settings" -ForegroundColor $ColorWarning
Write-Host "• Use -Erase to completely erase ESP32 flash" -ForegroundColor $ColorWarning
Write-Host "• Connect mobile app to test Bluetooth communication" -ForegroundColor $ColorWarning
