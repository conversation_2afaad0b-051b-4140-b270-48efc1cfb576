#include "motor_control.h"
#include "esp_log.h"
#include "driver/gpio.h"
#include "driver/ledc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "motor_control";

static motor_type_t current_motor_type = MOTOR_TYPE_STEPPER;
static bool motor_initialized = false;

// GPIO pins for motor control
#define MOTOR_PIN_1     GPIO_NUM_18
#define MOTOR_PIN_2     GPIO_NUM_19
#define MOTOR_PIN_3     GPIO_NUM_21
#define MOTOR_PIN_4     GPIO_NUM_22
#define MOTOR_ENABLE    GPIO_NUM_23

esp_err_t motor_control_init(motor_type_t motor_type)
{
    current_motor_type = motor_type;
    
    // Configure GPIO pins
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << MOTOR_PIN_1) | (1ULL << MOTOR_PIN_2) | 
                       (1ULL << MOTOR_PIN_3) | (1ULL << MOTOR_PIN_4) | 
                       (1ULL << MOTOR_ENABLE),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    ESP_ERROR_CHECK(gpio_config(&io_conf));
    
    // Initialize all pins to low
    gpio_set_level(MOTOR_PIN_1, 0);
    gpio_set_level(MOTOR_PIN_2, 0);
    gpio_set_level(MOTOR_PIN_3, 0);
    gpio_set_level(MOTOR_PIN_4, 0);
    gpio_set_level(MOTOR_ENABLE, 0);
    
    motor_initialized = true;
    ESP_LOGI(TAG, "Motor control initialized for type: %d", motor_type);
    
    return ESP_OK;
}

esp_err_t motor_control_feed(uint8_t portion_size)
{
    if (!motor_initialized) {
        ESP_LOGE(TAG, "Motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "Feeding portion size: %d", portion_size);
    
    // Enable motor
    gpio_set_level(MOTOR_ENABLE, 1);
    
    switch (current_motor_type) {
        case MOTOR_TYPE_STEPPER:
            return motor_stepper_feed(portion_size);
        case MOTOR_TYPE_DC:
            return motor_dc_feed(portion_size);
        case MOTOR_TYPE_SERVO:
            return motor_servo_feed(portion_size);
        default:
            ESP_LOGE(TAG, "Unknown motor type: %d", current_motor_type);
            gpio_set_level(MOTOR_ENABLE, 0);
            return ESP_ERR_INVALID_ARG;
    }
}

esp_err_t motor_stepper_feed(uint8_t portion_size)
{
    ESP_LOGI(TAG, "Stepper motor feeding...");
    
    // Simple stepper motor sequence
    int steps = portion_size * 10; // 10 steps per portion unit
    
    for (int i = 0; i < steps; i++) {
        // Step sequence for stepper motor
        gpio_set_level(MOTOR_PIN_1, 1);
        gpio_set_level(MOTOR_PIN_2, 0);
        gpio_set_level(MOTOR_PIN_3, 0);
        gpio_set_level(MOTOR_PIN_4, 0);
        vTaskDelay(pdMS_TO_TICKS(5));
        
        gpio_set_level(MOTOR_PIN_1, 0);
        gpio_set_level(MOTOR_PIN_2, 1);
        gpio_set_level(MOTOR_PIN_3, 0);
        gpio_set_level(MOTOR_PIN_4, 0);
        vTaskDelay(pdMS_TO_TICKS(5));
        
        gpio_set_level(MOTOR_PIN_1, 0);
        gpio_set_level(MOTOR_PIN_2, 0);
        gpio_set_level(MOTOR_PIN_3, 1);
        gpio_set_level(MOTOR_PIN_4, 0);
        vTaskDelay(pdMS_TO_TICKS(5));
        
        gpio_set_level(MOTOR_PIN_1, 0);
        gpio_set_level(MOTOR_PIN_2, 0);
        gpio_set_level(MOTOR_PIN_3, 0);
        gpio_set_level(MOTOR_PIN_4, 1);
        vTaskDelay(pdMS_TO_TICKS(5));
    }
    
    // Stop motor
    gpio_set_level(MOTOR_PIN_1, 0);
    gpio_set_level(MOTOR_PIN_2, 0);
    gpio_set_level(MOTOR_PIN_3, 0);
    gpio_set_level(MOTOR_PIN_4, 0);
    gpio_set_level(MOTOR_ENABLE, 0);
    
    ESP_LOGI(TAG, "Stepper motor feeding completed");
    return ESP_OK;
}

esp_err_t motor_dc_feed(uint8_t portion_size)
{
    ESP_LOGI(TAG, "DC motor feeding...");
    
    // Simple DC motor control - run for time based on portion size
    int run_time_ms = portion_size * 100; // 100ms per portion unit
    
    gpio_set_level(MOTOR_PIN_1, 1);
    gpio_set_level(MOTOR_PIN_2, 0);
    
    vTaskDelay(pdMS_TO_TICKS(run_time_ms));
    
    // Stop motor
    gpio_set_level(MOTOR_PIN_1, 0);
    gpio_set_level(MOTOR_PIN_2, 0);
    gpio_set_level(MOTOR_ENABLE, 0);
    
    ESP_LOGI(TAG, "DC motor feeding completed");
    return ESP_OK;
}

esp_err_t motor_servo_feed(uint8_t portion_size)
{
    ESP_LOGI(TAG, "Servo motor feeding...");
    
    // Configure PWM for servo control
    ledc_timer_config_t ledc_timer = {
        .duty_resolution = LEDC_TIMER_13_BIT,
        .freq_hz = 50,
        .speed_mode = LEDC_LOW_SPEED_MODE,
        .timer_num = LEDC_TIMER_0,
        .clk_cfg = LEDC_AUTO_CLK,
    };
    ESP_ERROR_CHECK(ledc_timer_config(&ledc_timer));
    
    ledc_channel_config_t ledc_channel = {
        .channel = LEDC_CHANNEL_0,
        .duty = 0,
        .gpio_num = MOTOR_PIN_1,
        .speed_mode = LEDC_LOW_SPEED_MODE,
        .hpoint = 0,
        .timer_sel = LEDC_TIMER_0
    };
    ESP_ERROR_CHECK(ledc_channel_config(&ledc_channel));
    
    // Move servo based on portion size
    int angle = portion_size * 18; // 18 degrees per portion unit
    int duty = (angle * 8192) / 180 + 410; // Convert angle to duty cycle
    
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0, duty));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0));
    
    vTaskDelay(pdMS_TO_TICKS(1000)); // Wait for servo to move
    
    // Return to neutral position
    ESP_ERROR_CHECK(ledc_set_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0, 410));
    ESP_ERROR_CHECK(ledc_update_duty(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0));
    
    vTaskDelay(pdMS_TO_TICKS(500));
    
    // Stop PWM
    ESP_ERROR_CHECK(ledc_stop(LEDC_LOW_SPEED_MODE, LEDC_CHANNEL_0, 0));
    gpio_set_level(MOTOR_ENABLE, 0);
    
    ESP_LOGI(TAG, "Servo motor feeding completed");
    return ESP_OK;
}

bool motor_control_is_ready(void)
{
    return motor_initialized;
}

motor_type_t motor_control_get_type(void)
{
    return current_motor_type;
}
