# PowerShell script to increment version numbers for both firmware and mobile app
# Studio 311 LP - CatFeeder Version Management System

param(
    [string]$Component = "both",  # "firmware", "mobile", or "both"
    [switch]$Major,
    [switch]$Minor,
    [switch]$Patch,
    [switch]$Build = $true  # Default to build increment
)

$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$VersionFile = Join-Path $ScriptDir "version.json"

Write-Host "=== Studio 311 LP - CatFeeder Version Manager ===" -ForegroundColor Cyan
Write-Host "Component: $Component" -ForegroundColor Yellow

if (-not (Test-Path $VersionFile)) {
    Write-Error "Version file not found: $VersionFile"
    exit 1
}

# Read current version
$versionData = Get-Content $VersionFile | ConvertFrom-Json

# Determine which version component to increment
if ($Major) {
    $versionData.major++
    $versionData.minor = 0
    $versionData.patch = 0
    $versionData.build = 1
    Write-Host "Incrementing MAJOR version" -ForegroundColor Green
} elseif ($Minor) {
    $versionData.minor++
    $versionData.patch = 0
    $versionData.build = 1
    Write-Host "Incrementing MINOR version" -ForegroundColor Green
} elseif ($Patch) {
    $versionData.patch++
    $versionData.build = 1
    Write-Host "Incrementing PATCH version" -ForegroundColor Green
} else {
    $versionData.build++
    Write-Host "Incrementing BUILD number" -ForegroundColor Green
}

# Update version string
$newVersion = "$($versionData.major).$($versionData.minor).$($versionData.patch).$($versionData.build)"
$versionData.version = $newVersion

# Update timestamps
$currentTime = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"

if ($Component -eq "firmware" -or $Component -eq "both") {
    $versionData.firmware.version = "$($versionData.major).$($versionData.minor).$($versionData.patch)"
    $versionData.firmware.build_number = $versionData.build
    $versionData.firmware.last_build = $currentTime
    Write-Host "Updated firmware version: $($versionData.firmware.version).$($versionData.firmware.build_number)" -ForegroundColor Cyan
}

if ($Component -eq "mobile" -or $Component -eq "both") {
    $versionData.mobile_app.version = "$($versionData.major).$($versionData.minor).$($versionData.patch)"
    $versionData.mobile_app.build_number = $versionData.build
    $versionData.mobile_app.last_build = $currentTime
    Write-Host "Updated mobile app version: $($versionData.mobile_app.version).$($versionData.mobile_app.build_number)" -ForegroundColor Cyan
}

# Save updated version
$versionData | ConvertTo-Json -Depth 10 | Set-Content $VersionFile -Encoding UTF8

Write-Host "Version updated successfully: $newVersion" -ForegroundColor Green
Write-Host "Updated version file: $VersionFile" -ForegroundColor Gray

# Output version info for build scripts
Write-Host "VERSION_STRING=$newVersion" -ForegroundColor Yellow
Write-Host "VERSION_MAJOR=$($versionData.major)" -ForegroundColor Yellow
Write-Host "VERSION_MINOR=$($versionData.minor)" -ForegroundColor Yellow
Write-Host "VERSION_PATCH=$($versionData.patch)" -ForegroundColor Yellow
Write-Host "VERSION_BUILD=$($versionData.build)" -ForegroundColor Yellow
