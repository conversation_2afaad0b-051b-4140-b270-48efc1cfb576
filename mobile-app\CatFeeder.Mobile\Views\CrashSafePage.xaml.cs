using Microsoft.Maui.Controls;
using System;
using System.Text;
using System.Threading.Tasks;

namespace CatFeeder.Mobile.Views
{
    public partial class CrashSafePage : ContentPage
    {
        private Exception? _lastException;
        private string _crashDetails = "";

        public CrashSafePage(Exception? exception = null)
        {
            InitializeComponent();
            _lastException = exception;

            _ = InitializeSafeModeAsync();
        }

        private async Task InitializeSafeModeAsync()
        {
            try
            {
                await LoadSystemInformationAsync();
                await LoadDiagnosticStepsAsync();

                if (_lastException != null)
                {
                    _crashDetails = FormatExceptionDetails(_lastException);
                    ErrorDetailsLabel.Text = _crashDetails;
                }
            }
            catch (Exception ex)
            {
                // Even safe mode failed - show minimal info
                SystemInfoLabel.Text = $"Critical error in safe mode: {ex.Message}";
            }
        }

        private async Task LoadSystemInformationAsync()
        {
            try
            {
                var sb = new StringBuilder();

                // Basic device info
                sb.AppendLine($"Device: {DeviceInfo.Model}");
                sb.AppendLine($"Platform: {DeviceInfo.Platform} {DeviceInfo.VersionString}");

                // Get app version from AppInfo instead of hardcoding
                string appVersion = AppInfo.VersionString;
                string buildNumber = AppInfo.BuildString;
                sb.AppendLine($"App Version: {appVersion} (Build {buildNumber})");

                sb.AppendLine($"Company: Studio 311 LP");
                sb.AppendLine($"Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                // Memory info (if available)
                try
                {
                    var memoryInfo = GC.GetTotalMemory(false);
                    sb.AppendLine($"Memory Usage: {memoryInfo / 1024 / 1024:F1} MB");
                }
                catch
                {
                    sb.AppendLine("Memory Usage: Unknown");
                }

                SystemInfoLabel.Text = sb.ToString();
            }
            catch (Exception ex)
            {
                SystemInfoLabel.Text = $"Failed to load system info: {ex.Message}";
            }
        }

        private async Task LoadDiagnosticStepsAsync()
        {
            try
            {
                var steps = new[]
                {
                    "✅ Safe mode activated successfully",
                    "🔍 Checking system compatibility...",
                    "📱 Verifying device capabilities...",
                    "🔧 Analyzing crash data...",
                    "💾 Preparing error report..."
                };

                foreach (var step in steps)
                {
                    var stepLabel = new Label
                    {
                        Text = step,
                        FontSize = 12,
                        TextColor = Color.FromArgb("#666"),
                        Margin = new Thickness(0, 2)
                    };

                    DiagnosticSteps.Children.Add(stepLabel);
                    await Task.Delay(500); // Simulate diagnostic work
                }

                // Add final status
                var statusLabel = new Label
                {
                    Text = "✅ Diagnostics complete - Ready for user action",
                    FontSize = 12,
                    TextColor = Color.FromArgb("#4CAF50"),
                    FontAttributes = FontAttributes.Bold,
                    Margin = new Thickness(0, 5)
                };
                DiagnosticSteps.Children.Add(statusLabel);
            }
            catch (Exception ex)
            {
                var errorLabel = new Label
                {
                    Text = $"❌ Diagnostic failed: {ex.Message}",
                    FontSize = 12,
                    TextColor = Color.FromArgb("#FF6B6B")
                };
                DiagnosticSteps.Children.Add(errorLabel);
            }
        }

        private string FormatExceptionDetails(Exception ex)
        {
            var sb = new StringBuilder();

            sb.AppendLine($"Exception Type: {ex.GetType().Name}");
            sb.AppendLine($"Message: {ex.Message}");

            if (!string.IsNullOrEmpty(ex.StackTrace))
            {
                sb.AppendLine("\nStack Trace:");
                sb.AppendLine(ex.StackTrace);
            }

            if (ex.InnerException != null)
            {
                sb.AppendLine("\nInner Exception:");
                sb.AppendLine(FormatExceptionDetails(ex.InnerException));
            }

            return sb.ToString();
        }

        private async void OnShowErrorDetailsClicked(object sender, EventArgs e)
        {
            try
            {
                if (ErrorDetailsSection.IsVisible)
                {
                    ErrorDetailsSection.IsVisible = false;
                    ShowErrorDetailsButton.Text = "Show Error Details";
                }
                else
                {
                    ErrorDetailsSection.IsVisible = true;
                    ShowErrorDetailsButton.Text = "Hide Error Details";
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to toggle error details: {ex.Message}", "OK");
            }
        }

        private async void OnTryAgainClicked(object sender, EventArgs e)
        {
            try
            {
                TryAgainButton.Text = "Restarting...";
                TryAgainButton.IsEnabled = false;

                await Task.Delay(1000);

                // Try to restart the app normally
                Application.Current.MainPage = new StartupPage();
            }
            catch (Exception ex)
            {
                await DisplayAlert("Restart Failed",
                    $"Could not restart the app: {ex.Message}\n\nPlease close and reopen the app manually.",
                    "OK");

                TryAgainButton.Text = "Try Starting App Again";
                TryAgainButton.IsEnabled = true;
            }
        }

        private async void OnSafeModeClicked(object sender, EventArgs e)
        {
            try
            {
                SafeModeButton.Text = "Loading...";
                SafeModeButton.IsEnabled = false;

                await Task.Delay(1000);

                // Create a minimal safe version of the app
                Application.Current.MainPage = new NavigationPage(new SafeModeMainPage());
            }
            catch (Exception ex)
            {
                await DisplayAlert("Safe Mode Failed",
                    $"Could not start safe mode: {ex.Message}",
                    "OK");

                SafeModeButton.Text = "Continue in Safe Mode";
                SafeModeButton.IsEnabled = true;
            }
        }

        private async void OnSendReportClicked(object sender, EventArgs e)
        {
            try
            {
                var report = GenerateErrorReport();

                // For now, just show the report to the user
                // In a real app, you'd send this to your error reporting service
                await DisplayAlert("Error Report",
                    $"Error report generated:\n\n{report}\n\nPlease send this information to Studio 311 LP support.",
                    "OK");
            }
            catch (Exception ex)
            {
                await DisplayAlert("Report Failed",
                    $"Could not generate error report: {ex.Message}",
                    "OK");
            }
        }

        private string GenerateErrorReport()
        {
            var sb = new StringBuilder();

            sb.AppendLine("=== STUDIO 311 LP - SMART CAT FEEDER ERROR REPORT ===");
            sb.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();

            sb.AppendLine("SYSTEM INFORMATION:");
            sb.AppendLine(SystemInfoLabel.Text);
            sb.AppendLine();

            if (!string.IsNullOrEmpty(_crashDetails))
            {
                sb.AppendLine("CRASH DETAILS:");
                sb.AppendLine(_crashDetails);
                sb.AppendLine();
            }

            sb.AppendLine("USER ACTIONS:");
            sb.AppendLine("- App crashed and safe mode was activated");
            sb.AppendLine("- User requested error report generation");
            sb.AppendLine();

            sb.AppendLine("=== END OF REPORT ===");

            return sb.ToString();
        }
    }

    // Minimal safe mode main page
    public class SafeModeMainPage : ContentPage
    {
        public SafeModeMainPage()
        {
            Title = "Safe Mode - Smart Cat Feeder";
            BackgroundColor = Color.FromArgb("#FFF3E0");

            Content = new StackLayout
            {
                Padding = 20,
                Spacing = 20,
                VerticalOptions = LayoutOptions.CenterAndExpand,
                Children =
                {
                    new Label
                    {
                        Text = "🔧",
                        FontSize = 60,
                        HorizontalOptions = LayoutOptions.Center
                    },
                    new Label
                    {
                        Text = "Safe Mode Active",
                        FontSize = 24,
                        FontAttributes = FontAttributes.Bold,
                        HorizontalOptions = LayoutOptions.Center,
                        TextColor = Color.FromArgb("#FF8C00")
                    },
                    new Label
                    {
                        Text = "The app is running in safe mode with limited functionality.",
                        FontSize = 16,
                        HorizontalOptions = LayoutOptions.Center,
                        HorizontalTextAlignment = TextAlignment.Center,
                        TextColor = Color.FromArgb("#666")
                    },
                    new Button
                    {
                        Text = "Exit Safe Mode",
                        BackgroundColor = Color.FromArgb("#FF8C00"),
                        TextColor = Colors.White,
                        CornerRadius = 25,
                        HeightRequest = 50
                    }
                }
            };
        }
    }
}

