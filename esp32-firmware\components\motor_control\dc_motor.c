/**
 * @file dc_motor.c
 * @brief DC motor control implementation for CatFeeder ESP32
 * <AUTHOR> 311 LP
 * 
 * This module provides DC motor control functionality for food dispensing
 * in the cat feeder system using PWM control.
 */

#include "motor_control.h"
#include "esp_log.h"
#include "driver/gpio.h"
#include "driver/ledc.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_timer.h"

static const char* TAG = "dc_motor";

// DC motor configuration
#define DC_MOTOR_PWM_PIN    GPIO_NUM_18
#define DC_MOTOR_DIR_PIN    GPIO_NUM_19
#define DC_MOTOR_ENABLE_PIN GPIO_NUM_21

// PWM configuration
#define DC_MOTOR_PWM_FREQ   1000    // 1kHz PWM frequency
#define DC_MOTOR_PWM_RESOLUTION LEDC_TIMER_10_BIT
#define DC_MOTOR_PWM_CHANNEL LEDC_CHANNEL_0
#define DC_MOTOR_PWM_TIMER   LEDC_TIMER_0

static bool dc_motor_initialized = false;
static bool dc_motor_running = false;
static uint32_t current_duty_cycle = 0;

/**
 * Initialize DC motor GPIO and PWM
 */
esp_err_t dc_motor_init(void)
{
    if (dc_motor_initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Initializing DC motor...");
    
    // Configure PWM timer
    ledc_timer_config_t timer_conf = {
        .speed_mode = LEDC_LOW_SPEED_MODE,
        .timer_num = DC_MOTOR_PWM_TIMER,
        .duty_resolution = DC_MOTOR_PWM_RESOLUTION,
        .freq_hz = DC_MOTOR_PWM_FREQ,
        .clk_cfg = LEDC_AUTO_CLK
    };
    
    esp_err_t ret = ledc_timer_config(&timer_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure PWM timer: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // Configure PWM channel
    ledc_channel_config_t channel_conf = {
        .speed_mode = LEDC_LOW_SPEED_MODE,
        .channel = DC_MOTOR_PWM_CHANNEL,
        .timer_sel = DC_MOTOR_PWM_TIMER,
        .intr_type = LEDC_INTR_DISABLE,
        .gpio_num = DC_MOTOR_PWM_PIN,
        .duty = 0,
        .hpoint = 0
    };
    
    ret = ledc_channel_config(&channel_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure PWM channel: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // Configure direction and enable pins
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << DC_MOTOR_DIR_PIN) | (1ULL << DC_MOTOR_ENABLE_PIN),
        .pull_down_en = 0,
        .pull_up_en = 0,
    };
    
    ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure GPIO: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // Set initial states
    gpio_set_level(DC_MOTOR_DIR_PIN, 1);     // Forward direction
    gpio_set_level(DC_MOTOR_ENABLE_PIN, 0);  // Disable motor initially
    
    dc_motor_initialized = true;
    ESP_LOGI(TAG, "DC motor initialized successfully");
    
    return ESP_OK;
}

/**
 * Start DC motor rotation
 */
esp_err_t dc_motor_start(void)
{
    if (!dc_motor_initialized) {
        ESP_LOGE(TAG, "DC motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (dc_motor_running) {
        ESP_LOGW(TAG, "DC motor already running");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "Starting DC motor");
    
    // Enable motor
    gpio_set_level(DC_MOTOR_ENABLE_PIN, 1);
    
    // Set default duty cycle (50%)
    uint32_t duty = (1 << DC_MOTOR_PWM_RESOLUTION) / 2;
    esp_err_t ret = ledc_set_duty(LEDC_LOW_SPEED_MODE, DC_MOTOR_PWM_CHANNEL, duty);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set PWM duty: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ret = ledc_update_duty(LEDC_LOW_SPEED_MODE, DC_MOTOR_PWM_CHANNEL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to update PWM duty: %s", esp_err_to_name(ret));
        return ret;
    }
    
    current_duty_cycle = duty;
    dc_motor_running = true;
    
    return ESP_OK;
}

/**
 * Stop DC motor rotation
 */
esp_err_t dc_motor_stop(void)
{
    if (!dc_motor_initialized) {
        ESP_LOGE(TAG, "DC motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "Stopping DC motor");
    
    // Set duty cycle to 0
    esp_err_t ret = ledc_set_duty(LEDC_LOW_SPEED_MODE, DC_MOTOR_PWM_CHANNEL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set PWM duty to 0: %s", esp_err_to_name(ret));
    }
    
    ret = ledc_update_duty(LEDC_LOW_SPEED_MODE, DC_MOTOR_PWM_CHANNEL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to update PWM duty: %s", esp_err_to_name(ret));
    }
    
    // Disable motor
    gpio_set_level(DC_MOTOR_ENABLE_PIN, 0);
    
    current_duty_cycle = 0;
    dc_motor_running = false;
    
    return ESP_OK;
}

/**
 * Set DC motor speed (0-100%)
 */
esp_err_t dc_motor_set_speed(uint8_t speed_percent)
{
    if (!dc_motor_initialized) {
        ESP_LOGE(TAG, "DC motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (speed_percent > 100) {
        ESP_LOGE(TAG, "Invalid speed: %d%% (valid range: 0-100%%)", speed_percent);
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "Setting DC motor speed to %d%%", speed_percent);
    
    // Calculate duty cycle
    uint32_t max_duty = (1 << DC_MOTOR_PWM_RESOLUTION) - 1;
    uint32_t duty = (max_duty * speed_percent) / 100;
    
    esp_err_t ret = ledc_set_duty(LEDC_LOW_SPEED_MODE, DC_MOTOR_PWM_CHANNEL, duty);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set PWM duty: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ret = ledc_update_duty(LEDC_LOW_SPEED_MODE, DC_MOTOR_PWM_CHANNEL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to update PWM duty: %s", esp_err_to_name(ret));
        return ret;
    }
    
    current_duty_cycle = duty;
    
    // Update running state
    dc_motor_running = (speed_percent > 0);
    
    return ESP_OK;
}

/**
 * Set DC motor direction
 */
esp_err_t dc_motor_set_direction(bool clockwise)
{
    if (!dc_motor_initialized) {
        ESP_LOGE(TAG, "DC motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "Setting DC motor direction: %s", clockwise ? "clockwise" : "counter-clockwise");
    
    gpio_set_level(DC_MOTOR_DIR_PIN, clockwise ? 1 : 0);
    
    return ESP_OK;
}

/**
 * Run DC motor for a specific duration
 */
esp_err_t dc_motor_run_duration(uint32_t duration_ms, uint8_t speed_percent)
{
    if (!dc_motor_initialized) {
        ESP_LOGE(TAG, "DC motor not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "Running DC motor for %lu ms at %d%% speed", duration_ms, speed_percent);
    
    // Set speed and start
    esp_err_t ret = dc_motor_set_speed(speed_percent);
    if (ret != ESP_OK) {
        return ret;
    }
    
    // Enable motor
    gpio_set_level(DC_MOTOR_ENABLE_PIN, 1);
    
    // Wait for duration
    vTaskDelay(pdMS_TO_TICKS(duration_ms));
    
    // Stop motor
    return dc_motor_stop();
}

/**
 * Check if DC motor is currently running
 */
bool dc_motor_is_running(void)
{
    return dc_motor_running;
}

/**
 * Get current DC motor speed
 */
uint8_t dc_motor_get_speed(void)
{
    if (!dc_motor_initialized) {
        return 0;
    }
    
    uint32_t max_duty = (1 << DC_MOTOR_PWM_RESOLUTION) - 1;
    return (current_duty_cycle * 100) / max_duty;
}
