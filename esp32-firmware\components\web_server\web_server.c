#include "web_server.h"
#include "esp_http_server.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_system.h"
#include "version.h"
#include "wifi_manager.h"
#include <stdio.h>

static const char *TAG = "web_server";
static httpd_handle_t server = NULL;

static esp_err_t root_get_handler(httpd_req_t *req)
{
    // Check if we're in AP mode and redirect to setup page
    if (wifi_manager_is_ap_mode()) {
        httpd_resp_set_status(req, "302 Found");
        httpd_resp_set_hdr(req, "Location", "/setup");
        httpd_resp_send(req, NULL, 0);
        return ESP_OK;
    }

    // Normal mode - show status
    const char* simple_response =
        "Studio 311 LP - Smart Cat Feeder\n"
        "ESP32 Controller v1.0.0.2\n"
        "Status: Online & Ready\n"
        "WiFi: Connected to TomTom\n"
        "Web Server: Running\n"
        "\n"
        "System is working correctly!\n"
        "Use the mobile app to control feeding.";

    httpd_resp_set_type(req, "text/plain");
    httpd_resp_send(req, simple_response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static esp_err_t status_get_handler(httpd_req_t *req)
{
    // Ultra-simple JSON response
    const char* json_response =
        "{"
        "\"status\":\"ok\","
        "\"device\":\"CatFeeder_ESP32\","
        "\"version\":\"1.0.0.2\""
        "}";

    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, json_response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static const httpd_uri_t root = {
    .uri       = "/",
    .method    = HTTP_GET,
    .handler   = root_get_handler,
    .user_ctx  = NULL
};

static const httpd_uri_t status = {
    .uri       = "/status",
    .method    = HTTP_GET,
    .handler   = status_get_handler,
    .user_ctx  = NULL
};

static esp_err_t setup_get_handler(httpd_req_t *req)
{
    // Simple setup page for AP mode
    const char* setup_page =
        "<!DOCTYPE html>\n"
        "<html>\n"
        "<head>\n"
        "    <title>Cat Feeder Setup</title>\n"
        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n"
        "</head>\n"
        "<body>\n"
        "    <h1>Studio 311 LP Smart Cat Feeder Setup</h1>\n"
        "    <h2>WiFi Configuration</h2>\n"
        "    <form action=\"/configure\" method=\"post\">\n"
        "        <p>WiFi Network: <input type=\"text\" name=\"ssid\" required></p>\n"
        "        <p>Password: <input type=\"password\" name=\"password\"></p>\n"
        "        <p>Motor Type:\n"
        "            <select name=\"motor_type\">\n"
        "                <option value=\"0\">Stepper Motor</option>\n"
        "                <option value=\"1\">DC Motor</option>\n"
        "            </select>\n"
        "        </p>\n"
        "        <p><input type=\"submit\" value=\"Save Configuration\"></p>\n"
        "    </form>\n"
        "    <p><strong>Instructions:</strong></p>\n"
        "    <ol>\n"
        "        <li>Enter your WiFi network name and password</li>\n"
        "        <li>Select your motor type</li>\n"
        "        <li>Click Save Configuration</li>\n"
        "        <li>The device will restart and connect to your WiFi</li>\n"
        "        <li>Use the mobile app to control feeding</li>\n"
        "    </ol>\n"
        "</body>\n"
        "</html>";

    httpd_resp_set_type(req, "text/html");
    httpd_resp_send(req, setup_page, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static esp_err_t configure_post_handler(httpd_req_t *req)
{
    // Simple response for now - actual configuration will be implemented later
    const char* response =
        "<!DOCTYPE html>\n"
        "<html>\n"
        "<head><title>Configuration Saved</title></head>\n"
        "<body>\n"
        "    <h1>Configuration Saved!</h1>\n"
        "    <p>The device will restart and connect to your WiFi network.</p>\n"
        "    <p>You can now use the mobile app to control feeding.</p>\n"
        "</body>\n"
        "</html>";

    httpd_resp_set_type(req, "text/html");
    httpd_resp_send(req, response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static const httpd_uri_t setup = {
    .uri       = "/setup",
    .method    = HTTP_GET,
    .handler   = setup_get_handler,
    .user_ctx  = NULL
};

static const httpd_uri_t configure = {
    .uri       = "/configure",
    .method    = HTTP_POST,
    .handler   = configure_post_handler,
    .user_ctx  = NULL
};

esp_err_t web_server_start(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.lru_purge_enable = true;

    ESP_LOGI(TAG, "Starting server on port: '%d'", config.server_port);
    if (httpd_start(&server, &config) == ESP_OK) {
        ESP_LOGI(TAG, "Registering URI handlers");
        httpd_register_uri_handler(server, &root);
        httpd_register_uri_handler(server, &status);
        httpd_register_uri_handler(server, &setup);
        httpd_register_uri_handler(server, &configure);
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Error starting server!");
    return ESP_FAIL;
}

esp_err_t web_server_stop(void)
{
    if (server) {
        httpd_stop(server);
        server = NULL;
    }
    return ESP_OK;
}
