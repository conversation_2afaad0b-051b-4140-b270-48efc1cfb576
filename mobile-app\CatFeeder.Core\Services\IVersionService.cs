using System.Threading.Tasks;

namespace CatFeeder.Core.Services
{
    /// <summary>
    /// Service for managing version information
    /// Studio 311 LP - CatFeeder Version Management
    /// </summary>
    public interface IVersionService
    {
        /// <summary>
        /// Get the current mobile app version
        /// </summary>
        /// <returns>Version string in format major.minor.patch.build</returns>
        string GetMobileAppVersion();
        
        /// <summary>
        /// Get the expected firmware version (should match mobile app)
        /// </summary>
        /// <returns>Version string in format major.minor.patch.build</returns>
        string GetExpectedFirmwareVersion();
        
        /// <summary>
        /// Check if two version strings match
        /// </summary>
        /// <param name="version1">First version string</param>
        /// <param name="version2">Second version string</param>
        /// <returns>True if versions match</returns>
        bool AreVersionsCompatible(string version1, string version2);
    }
}
