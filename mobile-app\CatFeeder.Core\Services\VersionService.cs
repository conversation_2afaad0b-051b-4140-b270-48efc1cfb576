using System;
using System.IO;
using System.Reflection;
using System.Text.Json;
using System.Threading.Tasks;

namespace CatFeeder.Core.Services
{
    /// <summary>
    /// Service for managing version information
    /// Studio 311 LP - CatFeeder Version Management
    /// 
    /// This service reads version information from the global version.json file
    /// and provides version comparison functionality for mobile app and firmware.
    /// </summary>
    public class VersionService : IVersionService
    {
        private string? _cachedMobileVersion;
        private string? _cachedFirmwareVersion;
        
        /// <summary>
        /// Get the current mobile app version from assembly or version.json
        /// </summary>
        /// <returns>Version string in format major.minor.patch.build</returns>
        public string GetMobileAppVersion()
        {
            if (!string.IsNullOrEmpty(_cachedMobileVersion))
                return _cachedMobileVersion;
            
            try
            {
                // First try to get from version.json (build-time version)
                var versionFromJson = GetVersionFromJson("mobile_app");
                if (!string.IsNullOrEmpty(versionFromJson))
                {
                    _cachedMobileVersion = versionFromJson;
                    return _cachedMobileVersion;
                }
                
                // Fallback to assembly version
                var assembly = Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                if (version != null)
                {
                    _cachedMobileVersion = $"{version.Major}.{version.Minor}.{version.Build}.{version.Revision}";
                }
                else
                {
                    _cachedMobileVersion = "1.0.0.1"; // Ultimate fallback
                }
            }
            catch
            {
                _cachedMobileVersion = "1.0.0.1"; // Error fallback
            }
            
            return _cachedMobileVersion;
        }
        
        /// <summary>
        /// Get the expected firmware version (should match mobile app)
        /// </summary>
        /// <returns>Version string in format major.minor.patch.build</returns>
        public string GetExpectedFirmwareVersion()
        {
            if (!string.IsNullOrEmpty(_cachedFirmwareVersion))
                return _cachedFirmwareVersion;
            
            try
            {
                // Get firmware version from version.json
                var versionFromJson = GetVersionFromJson("firmware");
                if (!string.IsNullOrEmpty(versionFromJson))
                {
                    _cachedFirmwareVersion = versionFromJson;
                    return _cachedFirmwareVersion;
                }
                
                // If no firmware version in JSON, should match mobile app
                _cachedFirmwareVersion = GetMobileAppVersion();
            }
            catch
            {
                _cachedFirmwareVersion = GetMobileAppVersion(); // Error fallback
            }
            
            return _cachedFirmwareVersion;
        }
        
        /// <summary>
        /// Check if two version strings are compatible
        /// </summary>
        /// <param name="version1">First version string</param>
        /// <param name="version2">Second version string</param>
        /// <returns>True if versions match exactly</returns>
        public bool AreVersionsCompatible(string version1, string version2)
        {
            if (string.IsNullOrEmpty(version1) || string.IsNullOrEmpty(version2))
                return false;
            
            // For now, require exact match
            // In the future, could implement semantic version compatibility
            return string.Equals(version1.Trim(), version2.Trim(), StringComparison.OrdinalIgnoreCase);
        }
        
        /// <summary>
        /// Get version from version.json file
        /// </summary>
        /// <param name="component">Component name (mobile_app or firmware)</param>
        /// <returns>Version string or null if not found</returns>
        private string? GetVersionFromJson(string component)
        {
            try
            {
                // Look for version.json in various locations
                var possiblePaths = new[]
                {
                    "version.json",
                    "../version.json",
                    "../../version.json",
                    "../../../version.json",
                    "../../../../version.json"
                };
                
                foreach (var path in possiblePaths)
                {
                    if (File.Exists(path))
                    {
                        var jsonContent = File.ReadAllText(path);
                        using var document = JsonDocument.Parse(jsonContent);
                        
                        if (document.RootElement.TryGetProperty(component, out var componentElement))
                        {
                            if (componentElement.TryGetProperty("version", out var versionElement))
                            {
                                return versionElement.GetString();
                            }
                        }
                        
                        // Also try root level version property
                        if (document.RootElement.TryGetProperty("version", out var rootVersionElement))
                        {
                            return rootVersionElement.GetString();
                        }
                    }
                }
            }
            catch
            {
                // Ignore errors and return null
            }
            
            return null;
        }
    }
}
