# ESP32 Cat Feeder - Firmware Upload Script
# PowerShell version for better error handling

param(
    [string]$ComPort = "COM3",
    [string]$IdfPath = "C:\Espressif\frameworks\esp-idf-v5.4.1"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "ESP32 Cat Feeder - Firmware Upload to $ComPort" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if ESP-IDF exists
$exportScript = Join-Path $IdfPath "export.bat"
if (-not (Test-Path $exportScript)) {
    Write-Host "ERROR: ESP-IDF not found at $IdfPath" -ForegroundColor Red
    Write-Host "Please verify the ESP-IDF installation path." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[1/5] Setting up ESP-IDF environment..." -ForegroundColor Yellow
Write-Host "ESP-IDF Path: $IdfPath" -ForegroundColor Gray
Write-Host ""

# Set environment variables
$env:IDF_PATH = $IdfPath
$env:IDF_TOOLS_PATH = "C:\Espressif"

# Run commands in a cmd session with ESP-IDF environment
$commands = @"
call "$exportScript"
if %errorLevel% neq 0 exit /b %errorLevel%

echo [2/5] Verifying ESP-IDF installation...
idf.py --version
if %errorLevel% neq 0 exit /b %errorLevel%

echo.
echo [3/5] Setting target to ESP32...
idf.py set-target esp32
if %errorLevel% neq 0 exit /b %errorLevel%

echo.
echo [4/5] Building Cat Feeder firmware...
idf.py build
if %errorLevel% neq 0 exit /b %errorLevel%

echo.
echo [5/5] Flashing to $ComPort...
idf.py -p $ComPort flash
if %errorLevel% neq 0 exit /b %errorLevel%

echo.
echo ========================================
echo SUCCESS: Firmware uploaded to $ComPort!
echo ========================================
"@

# Write commands to temporary batch file
$tempBat = Join-Path $env:TEMP "esp32_upload.bat"
$commands | Out-File -FilePath $tempBat -Encoding ASCII

try {
    # Execute the batch file
    $result = Start-Process -FilePath "cmd.exe" -ArgumentList "/c `"$tempBat`"" -Wait -PassThru -WorkingDirectory (Get-Location)
    
    if ($result.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "Device Name: CatFeeder_ESP32" -ForegroundColor Green
        Write-Host "Bluetooth PIN: 1234" -ForegroundColor Green
        Write-Host ""
        Write-Host "Available Commands:" -ForegroundColor Cyan
        Write-Host "- PING" -ForegroundColor Gray
        Write-Host "- GET_STATUS" -ForegroundColor Gray
        Write-Host "- GET_DEVICE_INFO" -ForegroundColor Gray
        Write-Host "- MANUAL_FEED:portion_size" -ForegroundColor Gray
        Write-Host "- SET_MOTOR_TYPE:0|1|2" -ForegroundColor Gray
        Write-Host "- WIFI_CONNECT:ssid,password" -ForegroundColor Gray
        Write-Host "- GET_TIME" -ForegroundColor Gray
        Write-Host "- GET_FEEDING_STATS" -ForegroundColor Gray
        Write-Host "- SYNC_FEEDING_HISTORY:last_id,max_entries" -ForegroundColor Gray
        Write-Host "- GET_RECENT_FEEDINGS:count" -ForegroundColor Gray
        Write-Host "- RESTART" -ForegroundColor Gray
        Write-Host "- ECHO:message" -ForegroundColor Gray
        Write-Host ""
        Write-Host "To monitor output:" -ForegroundColor Yellow
        Write-Host "  idf.py -p $ComPort monitor" -ForegroundColor Gray
        Write-Host ""
        Write-Host "Now test with the MAUI Android app!" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Upload failed with exit code $($result.ExitCode)" -ForegroundColor Red
        Write-Host ""
        Write-Host "Common issues:" -ForegroundColor Yellow
        Write-Host "- ESP32 not connected to $ComPort" -ForegroundColor Gray
        Write-Host "- Another program using the port" -ForegroundColor Gray
        Write-Host "- ESP32 not in download mode (hold BOOT button while pressing RESET)" -ForegroundColor Gray
    }
} finally {
    # Clean up temporary file
    if (Test-Path $tempBat) {
        Remove-Item $tempBat -Force
    }
}

Read-Host "Press Enter to exit"
