#include "web_server.h"
#include "esp_http_server.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_system.h"
#include "version.h"
#include <stdio.h>

static const char *TAG = "web_server";
static httpd_handle_t server = NULL;

static esp_err_t root_get_handler(httpd_req_t *req)
{
    // Ultra-simple response to avoid any potential crashes
    const char* simple_response =
        "Studio 311 LP - Smart Cat Feeder\n"
        "ESP32 Controller v1.0.0.2\n"
        "Status: Online & Ready\n"
        "WiFi: Connected to TomTom\n"
        "Web Server: Running\n"
        "\n"
        "System is working correctly!\n"
        "Use the mobile app to control feeding.";

    httpd_resp_set_type(req, "text/plain");
    httpd_resp_send(req, simple_response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static esp_err_t status_get_handler(httpd_req_t *req)
{
    // Ultra-simple JSON response
    const char* json_response =
        "{"
        "\"status\":\"ok\","
        "\"device\":\"CatFeeder_ESP32\","
        "\"version\":\"1.0.0.2\""
        "}";

    httpd_resp_set_type(req, "application/json");
    httpd_resp_send(req, json_response, HTTPD_RESP_USE_STRLEN);
    return ESP_OK;
}

static const httpd_uri_t root = {
    .uri       = "/",
    .method    = HTTP_GET,
    .handler   = root_get_handler,
    .user_ctx  = NULL
};

static const httpd_uri_t status = {
    .uri       = "/status",
    .method    = HTTP_GET,
    .handler   = status_get_handler,
    .user_ctx  = NULL
};

esp_err_t web_server_start(void)
{
    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.lru_purge_enable = true;

    ESP_LOGI(TAG, "Starting server on port: '%d'", config.server_port);
    if (httpd_start(&server, &config) == ESP_OK) {
        ESP_LOGI(TAG, "Registering URI handlers");
        httpd_register_uri_handler(server, &root);
        httpd_register_uri_handler(server, &status);
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Error starting server!");
    return ESP_FAIL;
}

esp_err_t web_server_stop(void)
{
    if (server) {
        httpd_stop(server);
        server = NULL;
    }
    return ESP_OK;
}
