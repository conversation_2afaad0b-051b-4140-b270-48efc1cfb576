/**
 * @file version.h
 * @brief Version information for CatFeeder ESP32 Firmware
 * <AUTHOR> 311 LP
 * 
 * This file is auto-generated by the build system.
 * Do not edit manually - changes will be overwritten.
 */

#ifndef VERSION_H
#define VERSION_H

#ifdef __cplusplus
extern "C" {
#endif

// Version information - auto-generated from version.json
#define VERSION_MAJOR 1
#define VERSION_MINOR 0
#define VERSION_PATCH 0
#define VERSION_BUILD 1

#define VERSION_STRING "1.0.0.1"
#define COMPANY_NAME "Studio 311 LP"
#define PRODUCT_NAME "CatFeeder ESP32"

// Build information
#define BUILD_DATE __DATE__
#define BUILD_TIME __TIME__

// Version as integer for comparison (MAJOR.MINOR.PATCH.BUILD)
#define VERSION_INT ((VERSION_MAJOR << 24) | (VERSION_MINOR << 16) | (VERSION_PATCH << 8) | VERSION_BUILD)

/**
 * @brief Get version string
 * @return Pointer to version string
 */
const char* get_version_string(void);

/**
 * @brief Get full version info including build date/time
 * @return Pointer to full version info string
 */
const char* get_full_version_info(void);

/**
 * @brief Get version as integer for comparison
 * @return Version as 32-bit integer
 */
uint32_t get_version_int(void);

#ifdef __cplusplus
}
#endif

#endif // VERSION_H
