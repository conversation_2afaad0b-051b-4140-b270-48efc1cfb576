/**
 * ESP32 Cat Feeder - Main Application
 *
 * This is the main entry point and orchestrator for the ESP32 Cat Feeder firmware.
 * It initializes all subsystems, manages their lifecycle, and coordinates their operation.
 *
 * System Architecture:
 * - Motor Control: Supports stepper motors and DC motors (time-based or sensor-based)
 * - WiFi Manager: Handles WiFi connection and network management
 * - Bluetooth Manager: Provides SPP communication with mobile apps
 * - Web Server: HTTP interface for browser-based control
 * - Scheduler: Manages automatic feeding schedules
 * - Feeding Controller: Coordinates feeding operations and safety checks
 * - Feeding History: Tracks and synchronizes feeding events
 * - Configuration: Persistent storage of device settings
 *
 * Initialization Sequence:
 * 1. Initialize NVS (Non-Volatile Storage) for persistent data
 * 2. Initialize event loop for inter-component communication
 * 3. Load device configuration from NVS
 * 4. Initialize all subsystems (motor, WiFi, Bluetooth, etc.)
 * 5. Start network services (web server)
 * 6. Start feeding scheduler
 * 7. Enter main monitoring loop
 *
 * External Libraries Used:
 * - freertos/: Real-time operating system for task management and synchronization
 * - esp_system.h: System-level functions (restart, chip info, etc.)
 * - esp_wifi.h: WiFi stack management and connection handling
 * - esp_event.h: Event loop system for inter-component communication
 * - nvs_flash.h: Non-volatile storage for persistent configuration
 * - esp_timer.h: High-resolution timers for precise timing
 */

#include <stdio.h>                    // Standard C I/O functions
#include <string.h>                   // Standard C string manipulation
#include <inttypes.h>                 // Format specifier macros
#include "freertos/FreeRTOS.h"        // FreeRTOS core functions and types
#include "freertos/task.h"            // Task creation and management
#include "freertos/event_groups.h"    // Event groups for synchronization between tasks
#include "esp_system.h"               // System functions (restart, chip info, heap info)
#include "esp_wifi.h"                 // WiFi stack management and API
#include "esp_event.h"                // Event loop system for component communication
#include "esp_log.h"                  // ESP32 logging system
#include "nvs_flash.h"                // Non-volatile storage in flash memory
#include "esp_timer.h"                // High-resolution timer API

// Project-specific component headers
#include "motor_control.h"            // Motor control abstraction layer
#include "wifi_manager.h"             // WiFi connection management
#include "bluetooth_manager.h"        // Bluetooth SPP communication
#include "web_server.h"               // HTTP server for web interface
#include "scheduler.h"                // Feeding schedule management
#include "config.h"                   // Configuration system
#include "app_config.h"               // Application-specific configuration
#include "feeding_controller.h"       // Feeding operation coordination
#include "bt_commands.h"              // Bluetooth command handlers

// Logging tag for main application
static const char *TAG = "CAT_FEEDER_MAIN";

// ============================================================================
// GLOBAL SYNCHRONIZATION AND STATE MANAGEMENT
// ============================================================================

/**
 * Event group for coordinating initialization and operation of all subsystems
 *
 * Event groups allow multiple tasks to wait for specific conditions to be met
 * before proceeding. This ensures proper initialization order and prevents
 * race conditions between components.
 *
 * FreeRTOS Event Group Functions:
 * - xEventGroupCreate(): Create a new event group
 * - xEventGroupSetBits(): Set one or more event bits (signal completion)
 * - xEventGroupWaitBits(): Wait for specific bits to be set
 * - xEventGroupClearBits(): Clear specific event bits
 */
static EventGroupHandle_t app_event_group;

/**
 * Event bit definitions for system state tracking
 *
 * Each bit represents the completion of a specific initialization phase.
 * Tasks can wait for multiple bits to ensure all dependencies are ready.
 *
 * BIT0-BIT23 are available for use (24 bits total in FreeRTOS event groups)
 */
#define WIFI_CONNECTED_BIT      BIT0    // WiFi connection established
#define CONFIG_LOADED_BIT       BIT1    // Device configuration loaded from NVS
#define MOTOR_READY_BIT         BIT2    // Motor control system initialized
#define SCHEDULER_READY_BIT     BIT3    // Feeding scheduler initialized

// ============================================================================
// SYSTEM INITIALIZATION FUNCTIONS
// ============================================================================

/**
 * Initialize NVS (Non-Volatile Storage) system
 *
 * NVS is ESP32's key-value storage system in flash memory for persistent data.
 * It's used to store device configuration, WiFi credentials, feeding history, etc.
 *
 * NVS handles flash wear leveling and provides atomic operations for data integrity.
 *
 * @return ESP_OK on success, ESP_ERR_* on failure
 *
 * External functions used:
 * - nvs_flash_init(): Initialize NVS partition in flash memory
 *   Returns ESP_OK if successful
 *   Returns ESP_ERR_NVS_NO_FREE_PAGES if flash is full
 *   Returns ESP_ERR_NVS_NEW_VERSION_FOUND if NVS version changed
 * - nvs_flash_erase(): Erase entire NVS partition (factory reset)
 *   Used when NVS is corrupted or version incompatible
 * - ESP_ERROR_CHECK(expr): Abort program if expression returns error
 */
static esp_err_t init_nvs(void)
{
    // Attempt to initialize NVS partition
    esp_err_t ret = nvs_flash_init();

    // Handle special cases that require NVS partition erasure
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        // NVS partition is full or version incompatible - erase and retry
        ESP_ERROR_CHECK(nvs_flash_erase());  // Erase entire NVS partition
        ret = nvs_flash_init();              // Reinitialize after erasure
    }

    return ret;
}

/**
 * WiFi event handler
 */
static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                              int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        ESP_LOGI(TAG, "WiFi disconnected, trying to reconnect...");
        esp_wifi_connect();
        xEventGroupClearBits(app_event_group, WIFI_CONNECTED_BIT);
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP: " IPSTR, IP2STR(&event->ip_info.ip));
        xEventGroupSetBits(app_event_group, WIFI_CONNECTED_BIT);
    }
}

/**
 * Initialize system components
 */
static esp_err_t init_system_components(void)
{
    esp_err_t ret = ESP_OK;

    // Initialize event loop
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    // Register WiFi event handler
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &wifi_event_handler, NULL));

    // Initialize configuration system
    ret = config_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize configuration: %s", esp_err_to_name(ret));
        return ret;
    }
    xEventGroupSetBits(app_event_group, CONFIG_LOADED_BIT);

    // Load application configuration
    app_config_t app_config;
    ret = app_config_load(&app_config);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to load app config, using defaults");
        app_config_set_defaults(&app_config);
        app_config_save(&app_config);
    }

    // Initialize motor control
    ret = motor_control_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize motor control: %s", esp_err_to_name(ret));
        return ret;
    }

    // Configure motor based on type
    if (app_config.motor_type == MOTOR_TYPE_STEPPER) {
        ret = motor_control_configure_stepper(&app_config.stepper_config);
    } else if (app_config.motor_type == MOTOR_TYPE_DC) {
        ret = motor_control_configure_dc(&app_config.dc_config);
    }

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure motor: %s", esp_err_to_name(ret));
        return ret;
    }
    xEventGroupSetBits(app_event_group, MOTOR_READY_BIT);

    // Initialize feeding controller
    ret = feeding_controller_init(&app_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize feeding controller: %s", esp_err_to_name(ret));
        return ret;
    }

    // Initialize scheduler
    ret = scheduler_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize scheduler: %s", esp_err_to_name(ret));
        return ret;
    }
    xEventGroupSetBits(app_event_group, SCHEDULER_READY_BIT);

    // Initialize WiFi manager
    ret = wifi_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize WiFi manager: %s", esp_err_to_name(ret));
        return ret;
    }

    // Initialize Bluetooth manager
    ret = bluetooth_manager_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth manager: %s", esp_err_to_name(ret));
        return ret;
    }

    // Start Bluetooth service
    ret = bluetooth_manager_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start Bluetooth service: %s", esp_err_to_name(ret));
        return ret;
    }

    // Initialize Bluetooth commands
    ret = bt_commands_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize Bluetooth commands: %s", esp_err_to_name(ret));
        return ret;
    }

    return ESP_OK;
}

/**
 * Start network services
 */
static esp_err_t start_network_services(void)
{
    esp_err_t ret = ESP_OK;

    // Wait for WiFi connection
    EventBits_t bits = xEventGroupWaitBits(app_event_group,
                                          WIFI_CONNECTED_BIT,
                                          pdFALSE,
                                          pdFALSE,
                                          portMAX_DELAY);

    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "WiFi connected, starting web server...");
        
        // Start web server
        ret = web_server_start();
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "Failed to start web server: %s", esp_err_to_name(ret));
            return ret;
        }
        
        ESP_LOGI(TAG, "Web server started successfully");
    }

    return ret;
}

/**
 * Main application task
 */
static void app_main_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Cat Feeder application starting...");

    // Wait for all components to be ready
    EventBits_t bits = xEventGroupWaitBits(app_event_group,
                                          CONFIG_LOADED_BIT | MOTOR_READY_BIT | SCHEDULER_READY_BIT,
                                          pdFALSE,
                                          pdTRUE,
                                          portMAX_DELAY);

    if ((bits & (CONFIG_LOADED_BIT | MOTOR_READY_BIT | SCHEDULER_READY_BIT)) == 
        (CONFIG_LOADED_BIT | MOTOR_READY_BIT | SCHEDULER_READY_BIT)) {
        
        ESP_LOGI(TAG, "All components initialized successfully");
        
        // Start network services
        start_network_services();
        
        // Start scheduler
        scheduler_start();
        
        ESP_LOGI(TAG, "Cat Feeder system is ready!");
        
        // Main application loop
        while (1) {
            // System status monitoring
            feeding_controller_status_t status;
            feeding_controller_get_status(&status);
            
            // Log system status every 30 seconds
            ESP_LOGI(TAG, "System Status - Feedings today: %" PRIu32 ", Last feeding: %lld, Motor status: %s",
                    status.feedings_today,
                    status.last_feeding_time,
                    status.motor_ready ? "Ready" : "Busy");
            
            vTaskDelay(pdMS_TO_TICKS(30000)); // 30 seconds
        }
    } else {
        ESP_LOGE(TAG, "Failed to initialize all components");
    }

    vTaskDelete(NULL);
}

/**
 * Application entry point
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32 Cat Feeder Firmware v1.0");
    ESP_LOGI(TAG, "Built on %s %s", __DATE__, __TIME__);

    // Create event group
    app_event_group = xEventGroupCreate();
    if (app_event_group == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return;
    }

    // Initialize NVS
    ESP_ERROR_CHECK(init_nvs());

    // Initialize system components
    esp_err_t ret = init_system_components();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize system components: %s", esp_err_to_name(ret));
        return;
    }

    // Create main application task
    xTaskCreate(app_main_task, "app_main_task", 4096, NULL, 5, NULL);

    ESP_LOGI(TAG, "Application initialization complete");
}
