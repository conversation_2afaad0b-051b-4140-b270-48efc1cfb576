/**
 * Feeding History Management Implementation
 *
 * This module manages feeding history storage and synchronization for the Cat Feeder.
 * It provides comprehensive tracking of all feeding events with detailed metadata.
 *
 * Key Features:
 * - Circular buffer storage for last 100 feedings in RAM
 * - Persistent storage in NVS (Non-Volatile Storage) flash memory
 * - Delta synchronization with mobile app (only send new entries)
 * - Comprehensive feeding statistics and analytics
 * - Automatic feeding event recording with full metadata
 *
 * Storage Strategy:
 * - ESP32: Stores last 100 feedings in NVS (limited flash wear)
 * - Mobile: Stores complete 1-year history in local database
 * - Sync: Delta sync transfers only new entries to mobile
 *
 * External Libraries Used:
 * - nvs_flash.h: Non-Volatile Storage for persistent data in flash memory
 * - nvs.h: NVS API for reading/writing key-value pairs to flash
 * - time.h: Standard C time functions for timestamps and date calculations
 */

#include "feeding_history.h"
#include "esp_log.h"          // ESP32 logging system for debug output
#include "nvs_flash.h"        // NVS flash memory management and initialization
#include "nvs.h"              // NVS API for persistent key-value storage
#include <string.h>           // Standard C string manipulation (memset, strcpy, etc.)
#include <time.h>             // Standard C time functions (time, localtime_r, etc.)

// Logging tag for this module
static const char *TAG = "FEEDING_HISTORY";

// ============================================================================
// GLOBAL STATE VARIABLES
// ============================================================================

/**
 * In-memory circular buffer for feeding history entries
 *
 * This array stores the most recent feeding events in RAM for fast access.
 * When full, new entries overwrite the oldest entries (circular buffer).
 * Size: MAX_FEEDING_HISTORY_ENTRIES (100) * sizeof(feeding_entry_t) ≈ 15KB RAM
 */
static feeding_entry_t feeding_history[MAX_FEEDING_HISTORY_ENTRIES];

/**
 * Number of feeding entries currently stored in the circular buffer
 * Range: 0 to MAX_FEEDING_HISTORY_ENTRIES
 * When history_count < MAX_FEEDING_HISTORY_ENTRIES: buffer is not full yet
 * When history_count == MAX_FEEDING_HISTORY_ENTRIES: buffer is full, using circular overwrite
 */
static uint32_t history_count = 0;

/**
 * Next unique feeding ID to assign to new feeding entries
 * This ID continuously increments and never resets (even after reboot)
 * Used for delta synchronization - mobile app requests entries since last known ID
 * Persisted in NVS to survive reboots
 */
static uint32_t next_feeding_id = 1;

/**
 * Flag indicating whether the feeding history system has been initialized
 * Prevents operations before initialization and double-initialization
 */
static bool history_initialized = false;

// ============================================================================
// TIME UTILITY FUNCTIONS
// ============================================================================

/**
 * Get current time as Unix timestamp (seconds since January 1, 1970)
 *
 * @return Current time in seconds since Unix epoch
 *
 * External functions used:
 * - time(time_t *): Standard C function to get current time
 *   Returns seconds since 1970-01-01 00:00:00 UTC
 *   If time_t pointer is not NULL, also stores result there
 */
static time_t get_current_time(void)
{
    time_t now;
    time(&now);  // Get current time and store in 'now' variable
    return now;  // Return the timestamp
}

/**
 * Check if two timestamps represent the same calendar day
 *
 * This function is used for daily statistics (feedings today, etc.)
 * Compares year and day-of-year to handle month boundaries correctly
 *
 * @param time1 First timestamp to compare
 * @param time2 Second timestamp to compare
 * @return true if both timestamps are on the same day, false otherwise
 *
 * External functions used:
 * - localtime_r(time_t *, struct tm *): Thread-safe conversion of timestamp to broken-down time
 *   Converts Unix timestamp to struct tm with year, month, day, hour, etc.
 *   tm_year: Years since 1900 (e.g., 2024 = 124)
 *   tm_yday: Day of year (0-365, where Jan 1 = 0)
 */
static bool is_same_day(time_t time1, time_t time2)
{
    struct tm tm1, tm2;           // Broken-down time structures
    localtime_r(&time1, &tm1);    // Convert first timestamp to local time
    localtime_r(&time2, &tm2);    // Convert second timestamp to local time

    // Compare year and day-of-year for exact day match
    return (tm1.tm_year == tm2.tm_year &&     // Same year
            tm1.tm_yday == tm2.tm_yday);      // Same day of year (handles month boundaries)
}

/**
 * Check if timestamp is within the last week
 */
static bool is_within_week(time_t timestamp)
{
    time_t now = get_current_time();
    return (now - timestamp) <= (7 * 24 * 60 * 60); // 7 days in seconds
}

/**
 * Check if timestamp is within the current month
 */
static bool is_within_month(time_t timestamp)
{
    time_t now = get_current_time();
    struct tm tm_now, tm_timestamp;
    localtime_r(&now, &tm_now);
    localtime_r(&timestamp, &tm_timestamp);
    
    return (tm_now.tm_year == tm_timestamp.tm_year && 
            tm_now.tm_mon == tm_timestamp.tm_mon);
}

/**
 * Initialize feeding history system
 */
esp_err_t feeding_history_init(void)
{
    ESP_LOGI(TAG, "Initializing feeding history system");
    
    // Clear in-memory storage
    memset(feeding_history, 0, sizeof(feeding_history));
    history_count = 0;
    next_feeding_id = 1;
    
    // Load from NVS
    esp_err_t ret = feeding_history_load();
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to load feeding history from NVS, starting fresh");
    }
    
    history_initialized = true;
    ESP_LOGI(TAG, "Feeding history initialized with %lu entries", history_count);
    
    return ESP_OK;
}

/**
 * Add a new feeding entry
 */
esp_err_t feeding_history_add_entry(const feeding_entry_t* entry)
{
    if (!history_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    if (entry == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    // Create new entry with assigned ID
    feeding_entry_t new_entry = *entry;
    new_entry.id = next_feeding_id++;
    new_entry.timestamp = get_current_time();
    
    // Add to circular buffer
    uint32_t index = history_count % MAX_FEEDING_HISTORY_ENTRIES;
    feeding_history[index] = new_entry;
    
    if (history_count < MAX_FEEDING_HISTORY_ENTRIES) {
        history_count++;
    }
    
    ESP_LOGI(TAG, "Added feeding entry ID=%lu, portion=%lu, result=%d, trigger=%d",
             new_entry.id, new_entry.portion_size, new_entry.result, new_entry.trigger);
    
    // Save to NVS periodically (every 10 entries or on important events)
    if (new_entry.id % 10 == 0 || new_entry.result != FEEDING_RESULT_SUCCESS) {
        feeding_history_save();
    }
    
    return ESP_OK;
}

/**
 * Get feeding statistics
 */
esp_err_t feeding_history_get_stats(feeding_stats_t* stats)
{
    if (!history_initialized || stats == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memset(stats, 0, sizeof(feeding_stats_t));
    
    time_t now = get_current_time();
    uint32_t successful_today = 0;
    uint32_t failed_today = 0;
    uint32_t week_count = 0;
    float week_portion_sum = 0;
    float today_food_total = 0;
    
    // Calculate statistics
    for (uint32_t i = 0; i < history_count; i++) {
        const feeding_entry_t* entry = &feeding_history[i];
        
        stats->total_feedings++;
        
        // Today's statistics
        if (is_same_day(entry->timestamp, now)) {
            stats->feedings_today++;
            today_food_total += entry->actual_portion;
            
            if (entry->result == FEEDING_RESULT_SUCCESS) {
                successful_today++;
            } else {
                failed_today++;
            }
        }
        
        // Week statistics
        if (is_within_week(entry->timestamp)) {
            stats->feedings_this_week++;
            week_count++;
            week_portion_sum += entry->actual_portion;
        }
        
        // Month statistics
        if (is_within_month(entry->timestamp)) {
            stats->feedings_this_month++;
        }
        
        // Latest feeding info
        if (i == history_count - 1 || entry->timestamp > stats->last_feeding_time) {
            stats->last_feeding_time = entry->timestamp;
            stats->last_feeding_portion = entry->actual_portion;
            stats->last_feeding_result = entry->result;
        }
    }
    
    stats->successful_feedings_today = successful_today;
    stats->failed_feedings_today = failed_today;
    stats->avg_portion_size_week = week_count > 0 ? week_portion_sum / week_count : 0;
    stats->total_food_dispensed_today = today_food_total;
    
    return ESP_OK;
}

/**
 * Get recent feeding entries
 */
esp_err_t feeding_history_get_recent(feeding_entry_t* entries, uint32_t max_entries, uint32_t* count)
{
    if (!history_initialized || entries == NULL || count == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    uint32_t entries_to_copy = history_count < max_entries ? history_count : max_entries;
    *count = entries_to_copy;
    
    // Copy most recent entries (from end of array backwards)
    for (uint32_t i = 0; i < entries_to_copy; i++) {
        uint32_t src_index = (history_count - 1 - i) % MAX_FEEDING_HISTORY_ENTRIES;
        entries[i] = feeding_history[src_index];
    }
    
    return ESP_OK;
}

/**
 * Get feeding entries since a specific ID (for delta sync)
 */
esp_err_t feeding_history_get_since_id(uint32_t since_id, feeding_entry_t* entries, uint32_t max_entries, uint32_t* count)
{
    if (!history_initialized || entries == NULL || count == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    *count = 0;
    
    // Find entries with ID > since_id
    for (uint32_t i = 0; i < history_count && *count < max_entries; i++) {
        if (feeding_history[i].id > since_id) {
            entries[*count] = feeding_history[i];
            (*count)++;
        }
    }
    
    ESP_LOGI(TAG, "Delta sync: found %lu entries since ID %lu", *count, since_id);
    
    return ESP_OK;
}

/**
 * Get the latest feeding ID
 */
uint32_t feeding_history_get_latest_id(void)
{
    return next_feeding_id - 1;
}

/**
 * Process delta sync request from mobile app
 */
esp_err_t feeding_history_process_sync_request(const sync_request_t* request, sync_response_t* response)
{
    if (!history_initialized || request == NULL || response == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memset(response, 0, sizeof(sync_response_t));
    
    // Get entries since last sync
    uint32_t max_entries = request->max_entries < 20 ? request->max_entries : 20;
    esp_err_t ret = feeding_history_get_since_id(request->last_sync_id, response->entries, max_entries, &response->entry_count);
    
    if (ret != ESP_OK) {
        return ret;
    }
    
    response->latest_id = feeding_history_get_latest_id();
    response->has_more = (response->entry_count == max_entries); // Might have more if we hit the limit
    
    // Get current statistics
    ret = feeding_history_get_stats(&response->current_stats);
    
    ESP_LOGI(TAG, "Sync response: %lu entries, latest_id=%lu, has_more=%d",
             response->entry_count, response->latest_id, response->has_more);
    
    return ret;
}

/**
 * Save feeding history to NVS
 */
esp_err_t feeding_history_save(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("storage", NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS handle: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // Save feeding history array
    size_t required_size = sizeof(feeding_history);
    ret = nvs_set_blob(nvs_handle, FEEDING_HISTORY_NVS_KEY, feeding_history, required_size);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to save feeding history: %s", esp_err_to_name(ret));
        nvs_close(nvs_handle);
        return ret;
    }
    
    // Save metadata
    ret = nvs_set_u32(nvs_handle, "hist_count", history_count);
    if (ret == ESP_OK) {
        ret = nvs_set_u32(nvs_handle, "next_id", next_feeding_id);
    }
    
    if (ret == ESP_OK) {
        ret = nvs_commit(nvs_handle);
    }
    
    nvs_close(nvs_handle);
    
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Feeding history saved to NVS (%lu entries)", history_count);
    } else {
        ESP_LOGE(TAG, "Failed to save feeding history metadata: %s", esp_err_to_name(ret));
    }
    
    return ret;
}

/**
 * Load feeding history from NVS
 */
esp_err_t feeding_history_load(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret = nvs_open("storage", NVS_READONLY, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to open NVS handle for reading: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // Load metadata first
    ret = nvs_get_u32(nvs_handle, "hist_count", &history_count);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "No feeding history count found in NVS");
        nvs_close(nvs_handle);
        return ret;
    }
    
    ret = nvs_get_u32(nvs_handle, "next_id", &next_feeding_id);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "No next feeding ID found in NVS");
        next_feeding_id = 1;
    }
    
    // Load feeding history array
    size_t required_size = sizeof(feeding_history);
    ret = nvs_get_blob(nvs_handle, FEEDING_HISTORY_NVS_KEY, feeding_history, &required_size);
    
    nvs_close(nvs_handle);
    
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Feeding history loaded from NVS (%lu entries, next_id=%lu)", history_count, next_feeding_id);
    } else {
        ESP_LOGW(TAG, "Failed to load feeding history from NVS: %s", esp_err_to_name(ret));
        history_count = 0;
        next_feeding_id = 1;
    }
    
    return ret;
}

/**
 * Helper function to create a feeding entry
 */
feeding_entry_t feeding_history_create_entry(uint32_t portion_size, uint32_t actual_portion,
                                            feeding_result_t result, feeding_trigger_t trigger,
                                            uint32_t duration_ms, const char* user, const char* notes)
{
    feeding_entry_t entry = {0};
    
    entry.portion_size = portion_size;
    entry.actual_portion = actual_portion;
    entry.result = result;
    entry.trigger = trigger;
    entry.duration_ms = duration_ms;
    entry.food_level_before = 0.0f; // Will be filled by sensor if available
    entry.food_level_after = 0.0f;  // Will be filled by sensor if available
    
    if (user != NULL) {
        strncpy(entry.user, user, sizeof(entry.user) - 1);
    }
    
    if (notes != NULL) {
        strncpy(entry.notes, notes, sizeof(entry.notes) - 1);
    }
    
    return entry;
}

/**
 * Get human-readable string for feeding result
 */
const char* feeding_history_result_to_string(feeding_result_t result)
{
    switch (result) {
        case FEEDING_RESULT_SUCCESS: return "Success";
        case FEEDING_RESULT_MOTOR_ERROR: return "Motor Error";
        case FEEDING_RESULT_SENSOR_ERROR: return "Sensor Error";
        case FEEDING_RESULT_PORTION_TOO_LARGE: return "Portion Too Large";
        case FEEDING_RESULT_DAILY_LIMIT_REACHED: return "Daily Limit Reached";
        case FEEDING_RESULT_MANUAL_STOP: return "Manual Stop";
        case FEEDING_RESULT_UNKNOWN_ERROR: return "Unknown Error";
        default: return "Invalid Result";
    }
}

/**
 * Get human-readable string for feeding trigger
 */
const char* feeding_history_trigger_to_string(feeding_trigger_t trigger)
{
    switch (trigger) {
        case FEEDING_TRIGGER_MANUAL: return "Manual";
        case FEEDING_TRIGGER_SCHEDULED: return "Scheduled";
        case FEEDING_TRIGGER_BLUETOOTH: return "Bluetooth";
        case FEEDING_TRIGGER_WEB: return "Web";
        case FEEDING_TRIGGER_BUTTON: return "Button";
        default: return "Unknown";
    }
}
