#include "app_config.h"
#include "esp_log.h"
#include <string.h>

static const char *TAG = "app_config";

// Default configuration values
static app_config_t current_config = {
    .motor_type = MOTOR_TYPE_STEPPER,
    .stepper_config = {
        .step_pin = GPIO_NUM_2,
        .dir_pin = GPIO_NUM_4,
        .enable_pin = GPIO_NUM_NC,
        .steps_per_revolution = 200,
        .max_speed_rpm = 60,
        .acceleration = 1000
    },
    .dc_config = {
        .pwm_pin = GPIO_NUM_2,
        .dir_pin = GPIO_NUM_NC,
        .enable_pin = GPIO_NUM_NC,
        .pwm_frequency = 1000,
        .max_duty_cycle = 80
    },
    .features = {
        .enable_food_level_sensor = false,
        .enable_led_indicators = true,
        .enable_buzzer = false,
        .enable_manual_button = true
    },
    .bt_config = {
        .device_name = "CatFeeder_ESP32",
        .enable_on_startup = true,
        .allow_config_via_bt = true
    },
    .wifi_config = {
        .ssid = "TomTom",
        .password = "LIETUVATEVYNEMUSU",
        .auto_connect = true,
        .connection_timeout_ms = 30000
    },
    .config_version = 1,
    .factory_reset_flag = false
};

esp_err_t app_config_init(void)
{
    ESP_LOGI(TAG, "Initializing application configuration");

    // Configuration is already initialized with default values
    ESP_LOGI(TAG, "Application configuration initialized");
    ESP_LOGI(TAG, "Device Name: %s", current_config.bt_config.device_name);
    ESP_LOGI(TAG, "Bluetooth Config: %s", current_config.bt_config.allow_config_via_bt ? "Enabled" : "Disabled");
    ESP_LOGI(TAG, "Motor Type: %d", current_config.motor_type);

    return ESP_OK;
}

app_config_t* app_config_get(void)
{
    return &current_config;
}

esp_err_t app_config_set_device_name(const char* name)
{
    if (name == NULL || strlen(name) >= sizeof(current_config.bt_config.device_name)) {
        return ESP_ERR_INVALID_ARG;
    }

    strcpy(current_config.bt_config.device_name, name);
    ESP_LOGI(TAG, "Device name set to: %s", name);
    return ESP_OK;
}

esp_err_t app_config_set_bluetooth_pin(const char* pin)
{
    // PIN functionality removed - using simple pairing
    ESP_LOGW(TAG, "Bluetooth PIN setting is deprecated");
    return ESP_OK;
}

esp_err_t app_config_load(app_config_t *config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    // For now, just use defaults - could load from NVS later
    ESP_LOGI(TAG, "Configuration loaded (using defaults)");
    return app_config_set_defaults(config);
}

esp_err_t app_config_save(const app_config_t *config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    // For now, just log - could save to NVS later
    ESP_LOGI(TAG, "Configuration saved");
    return ESP_OK;
}

esp_err_t app_config_set_defaults(app_config_t *config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    // Set default values
    config->motor_type = MOTOR_TYPE_STEPPER;
    config->feeding_config.default_portion_steps = 100;
    config->feeding_config.max_feedings_per_day = 10;
    config->features.enable_food_level_sensor = false;
    config->features.enable_led_indicators = true;
    config->factory_reset_flag = false;

    ESP_LOGI(TAG, "Configuration set to defaults");
    return ESP_OK;
}

esp_err_t app_config_set_wifi_credentials(const char* ssid, const char* password)
{
    if (ssid == NULL || password == NULL ||
        strlen(ssid) >= sizeof(current_config.wifi_config.ssid) ||
        strlen(password) >= sizeof(current_config.wifi_config.password)) {
        return ESP_ERR_INVALID_ARG;
    }

    strcpy(current_config.wifi_config.ssid, ssid);
    strcpy(current_config.wifi_config.password, password);
    ESP_LOGI(TAG, "WiFi credentials set for SSID: %s", ssid);
    return ESP_OK;
}

esp_err_t app_config_set_motor_type(motor_type_t motor_type)
{
    if (motor_type < MOTOR_TYPE_STEPPER || motor_type > MOTOR_TYPE_DC) {
        return ESP_ERR_INVALID_ARG;
    }

    current_config.motor_type = motor_type;
    ESP_LOGI(TAG, "Motor type set to: %d", motor_type);
    return ESP_OK;
}

// Additional configuration functions can be added here as needed
