/**
 * Feeding History Management System
 *
 * This module provides comprehensive feeding event tracking and synchronization
 * between the ESP32 device and mobile applications. It implements an efficient
 * delta synchronization protocol to minimize data transfer while maintaining
 * complete feeding history on mobile devices.
 *
 * Key Features:
 * - Circular buffer storage for recent feedings on ESP32 (limited flash wear)
 * - Persistent storage in NVS with automatic save/load
 * - Delta synchronization protocol (only transfer new entries)
 * - Comprehensive feeding statistics and analytics
 * - Support for multiple device types and triggers
 * - Detailed metadata for each feeding event
 *
 * Storage Architecture:
 * - ESP32: Last 100 feedings in RAM + NVS backup (efficient, limited storage)
 * - Mobile: Complete 1-year history in local database (unlimited storage)
 * - Sync: Delta protocol transfers only new entries since last sync
 *
 * Data Flow:
 * 1. Feeding occurs -> Record entry with full metadata
 * 2. Mobile app requests sync with last known ID
 * 3. ESP32 returns only entries newer than last known ID
 * 4. Mobile app stores new entries and updates last sync ID
 *
 * Thread Safety:
 * All functions are designed to be called from FreeRTOS tasks and are
 * protected against concurrent access where necessary.
 */

#pragma once

#include "esp_err.h"      // ESP32 error code definitions
#include <time.h>         // Standard C time functions and types
#include <stdbool.h>      // Standard C boolean type
#include <stdint.h>       // Standard C integer types

#ifdef __cplusplus
extern "C" {
#endif

// ============================================================================
// CONFIGURATION CONSTANTS
// ============================================================================

/**
 * Maximum number of feeding entries stored in ESP32 memory
 *
 * This creates a circular buffer - when full, new entries overwrite oldest ones.
 * Size chosen to balance memory usage (~15KB) with useful history depth.
 * Typical feeding frequency: 2-6 times per day = 30-90 days of history.
 */
#define MAX_FEEDING_HISTORY_ENTRIES 100

/**
 * NVS key for storing feeding history in flash memory
 *
 * NVS (Non-Volatile Storage) uses string keys to identify stored data.
 * This key is used to save/load the feeding history array to/from flash.
 */
#define FEEDING_HISTORY_NVS_KEY "feeding_hist"

// ============================================================================
// ENUMERATION TYPES FOR FEEDING CLASSIFICATION
// ============================================================================

/**
 * Feeding result types - Outcome of a feeding attempt
 *
 * These values are stored in feeding history and used for statistics.
 * They help identify patterns in feeding failures and system reliability.
 *
 * Values are explicitly numbered for stable storage/transmission compatibility.
 */
typedef enum {
    FEEDING_RESULT_SUCCESS = 0,              ///< Feeding completed successfully
    FEEDING_RESULT_MOTOR_ERROR = 1,          ///< Motor failed to operate (mechanical issue)
    FEEDING_RESULT_SENSOR_ERROR = 2,         ///< Food level sensor malfunction
    FEEDING_RESULT_PORTION_TOO_LARGE = 3,    ///< Requested portion exceeds safety limits
    FEEDING_RESULT_DAILY_LIMIT_REACHED = 4,  ///< Daily feeding limit already reached
    FEEDING_RESULT_MANUAL_STOP = 5,          ///< User manually stopped feeding
    FEEDING_RESULT_UNKNOWN_ERROR = 6         ///< Unspecified error occurred
} feeding_result_t;

/**
 * Feeding trigger types - What initiated the feeding
 *
 * This helps analyze feeding patterns and user behavior.
 * Useful for optimizing automatic schedules and identifying usage patterns.
 *
 * Values are explicitly numbered for stable storage/transmission compatibility.
 */
typedef enum {
    FEEDING_TRIGGER_MANUAL = 0,        ///< Manual feeding via mobile app interface
    FEEDING_TRIGGER_SCHEDULED = 1,     ///< Automatic feeding from programmed schedule
    FEEDING_TRIGGER_BLUETOOTH = 2,     ///< Direct Bluetooth command from mobile app
    FEEDING_TRIGGER_WEB = 3,          ///< Web browser interface command
    FEEDING_TRIGGER_BUTTON = 4        ///< Physical button press on device
} feeding_trigger_t;

/**
 * Single feeding history entry
 */
typedef struct {
    uint32_t id;                      ///< Unique feeding ID (incremental)
    time_t timestamp;                 ///< Unix timestamp when feeding occurred
    uint32_t portion_size;            ///< Portion size in motor steps/units
    uint32_t actual_portion;          ///< Actual portion delivered (may differ due to errors)
    feeding_result_t result;          ///< Feeding result
    feeding_trigger_t trigger;        ///< What triggered the feeding
    uint32_t duration_ms;             ///< How long the feeding took
    float food_level_before;          ///< Food level before feeding (if sensor enabled)
    float food_level_after;           ///< Food level after feeding (if sensor enabled)
    char user[16];                    ///< User who triggered feeding (if applicable)
    char notes[32];                   ///< Additional notes (error details, etc.)
} feeding_entry_t;

/**
 * Feeding history statistics
 */
typedef struct {
    uint32_t total_feedings;          ///< Total number of feedings recorded
    uint32_t feedings_today;          ///< Feedings today
    uint32_t feedings_this_week;      ///< Feedings this week
    uint32_t feedings_this_month;     ///< Feedings this month
    time_t last_feeding_time;         ///< Last feeding timestamp
    uint32_t last_feeding_portion;    ///< Last feeding portion size
    feeding_result_t last_feeding_result; ///< Last feeding result
    uint32_t successful_feedings_today; ///< Successful feedings today
    uint32_t failed_feedings_today;   ///< Failed feedings today
    float avg_portion_size_week;      ///< Average portion size this week
    float total_food_dispensed_today; ///< Total food dispensed today (in grams if sensor available)
} feeding_stats_t;

/**
 * Delta sync request from mobile app
 */
typedef struct {
    uint32_t last_sync_id;            ///< Last feeding ID the mobile app has
    time_t last_sync_time;            ///< Last sync timestamp
    uint32_t max_entries;             ///< Maximum entries to return in response
} sync_request_t;

/**
 * Delta sync response to mobile app
 */
typedef struct {
    uint32_t latest_id;               ///< Latest feeding ID on ESP32
    uint32_t entry_count;             ///< Number of entries in this response
    feeding_entry_t entries[20];      ///< New feeding entries (max 20 per sync)
    bool has_more;                    ///< True if more entries available
    feeding_stats_t current_stats;    ///< Current feeding statistics
} sync_response_t;

/**
 * Initialize feeding history system
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_init(void);

/**
 * Add a new feeding entry
 * 
 * @param entry Feeding entry to add
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_add_entry(const feeding_entry_t* entry);

/**
 * Get feeding statistics
 * 
 * @param stats Pointer to store statistics
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_get_stats(feeding_stats_t* stats);

/**
 * Get recent feeding entries
 * 
 * @param entries Array to store entries
 * @param max_entries Maximum number of entries to return
 * @param count Pointer to store actual number of entries returned
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_get_recent(feeding_entry_t* entries, uint32_t max_entries, uint32_t* count);

/**
 * Get feeding entries since a specific ID (for delta sync)
 * 
 * @param since_id Get entries with ID greater than this
 * @param entries Array to store entries
 * @param max_entries Maximum number of entries to return
 * @param count Pointer to store actual number of entries returned
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_get_since_id(uint32_t since_id, feeding_entry_t* entries, uint32_t max_entries, uint32_t* count);

/**
 * Get feeding entries for a specific date
 * 
 * @param date Date to get entries for (Unix timestamp, will use date part only)
 * @param entries Array to store entries
 * @param max_entries Maximum number of entries to return
 * @param count Pointer to store actual number of entries returned
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_get_for_date(time_t date, feeding_entry_t* entries, uint32_t max_entries, uint32_t* count);

/**
 * Get the latest feeding ID
 * 
 * @return Latest feeding ID, 0 if no feedings recorded
 */
uint32_t feeding_history_get_latest_id(void);

/**
 * Process delta sync request from mobile app
 * 
 * @param request Sync request from mobile app
 * @param response Sync response to send back
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_process_sync_request(const sync_request_t* request, sync_response_t* response);

/**
 * Clear all feeding history (factory reset)
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_clear_all(void);

/**
 * Save feeding history to NVS
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_save(void);

/**
 * Load feeding history from NVS
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t feeding_history_load(void);

/**
 * Helper function to create a feeding entry
 * 
 * @param portion_size Portion size requested
 * @param actual_portion Actual portion delivered
 * @param result Feeding result
 * @param trigger What triggered the feeding
 * @param duration_ms Duration in milliseconds
 * @param user User who triggered (can be NULL)
 * @param notes Additional notes (can be NULL)
 * @return Populated feeding entry
 */
feeding_entry_t feeding_history_create_entry(uint32_t portion_size, uint32_t actual_portion,
                                            feeding_result_t result, feeding_trigger_t trigger,
                                            uint32_t duration_ms, const char* user, const char* notes);

/**
 * Get human-readable string for feeding result
 * 
 * @param result Feeding result
 * @return String representation
 */
const char* feeding_history_result_to_string(feeding_result_t result);

/**
 * Get human-readable string for feeding trigger
 * 
 * @param trigger Feeding trigger
 * @return String representation
 */
const char* feeding_history_trigger_to_string(feeding_trigger_t trigger);

#ifdef __cplusplus
}
#endif
