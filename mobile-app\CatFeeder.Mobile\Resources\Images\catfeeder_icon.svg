<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="#4A90E2" stroke="#2E5C8A" stroke-width="8"/>
  
  <!-- Cat feeder bowl base -->
  <ellipse cx="256" cy="380" rx="120" ry="40" fill="#8B4513"/>
  <ellipse cx="256" cy="375" rx="120" ry="40" fill="#A0522D"/>
  
  <!-- Cat feeder bowl -->
  <ellipse cx="256" cy="350" rx="100" ry="30" fill="#FFE4B5"/>
  <ellipse cx="256" cy="345" rx="90" ry="25" fill="#FFF8DC"/>
  
  <!-- Food kibbles in bowl -->
  <circle cx="240" cy="345" r="8" fill="#8B4513"/>
  <circle cx="260" cy="340" r="6" fill="#A0522D"/>
  <circle cx="250" cy="350" r="7" fill="#CD853F"/>
  <circle cx="270" cy="345" r="5" fill="#8B4513"/>
  
  <!-- Cat head -->
  <ellipse cx="256" cy="200" rx="80" ry="70" fill="#FF8C00"/>
  
  <!-- Cat ears -->
  <ellipse cx="220" cy="150" rx="25" ry="35" fill="#FF8C00" transform="rotate(-20 220 150)"/>
  <ellipse cx="292" cy="150" rx="25" ry="35" fill="#FF8C00" transform="rotate(20 292 150)"/>
  
  <!-- Inner ears -->
  <ellipse cx="220" cy="155" rx="12" ry="20" fill="#FFB347" transform="rotate(-20 220 155)"/>
  <ellipse cx="292" cy="155" rx="12" ry="20" fill="#FFB347" transform="rotate(20 292 155)"/>
  
  <!-- Cat stripes -->
  <ellipse cx="230" cy="180" rx="15" ry="8" fill="#FF7F00" transform="rotate(-10 230 180)"/>
  <ellipse cx="282" cy="180" rx="15" ry="8" fill="#FF7F00" transform="rotate(10 282 180)"/>
  <ellipse cx="256" cy="170" rx="20" ry="6" fill="#FF7F00"/>
  
  <!-- Cat eyes -->
  <ellipse cx="235" cy="190" rx="12" ry="15" fill="#32CD32"/>
  <ellipse cx="277" cy="190" rx="12" ry="15" fill="#32CD32"/>
  
  <!-- Eye pupils -->
  <ellipse cx="235" cy="192" rx="6" ry="10" fill="#000"/>
  <ellipse cx="277" cy="192" rx="6" ry="10" fill="#000"/>
  
  <!-- Eye highlights -->
  <circle cx="237" cy="188" r="3" fill="#FFF"/>
  <circle cx="279" cy="188" r="3" fill="#FFF"/>
  
  <!-- Cat nose -->
  <path d="M 256 205 L 250 215 L 262 215 Z" fill="#FF69B4"/>
  
  <!-- Cat mouth -->
  <path d="M 256 215 Q 245 225 235 220" stroke="#000" stroke-width="3" fill="none"/>
  <path d="M 256 215 Q 267 225 277 220" stroke="#000" stroke-width="3" fill="none"/>
  
  <!-- Cat whiskers -->
  <line x1="190" y1="200" x2="220" y2="205" stroke="#000" stroke-width="2"/>
  <line x1="190" y1="210" x2="220" y2="210" stroke="#000" stroke-width="2"/>
  <line x1="292" y1="205" x2="322" y2="200" stroke="#000" stroke-width="2"/>
  <line x1="292" y1="210" x2="322" y2="210" stroke="#000" stroke-width="2"/>
  
  <!-- Feeding dispenser -->
  <rect x="226" y="280" width="60" height="40" rx="10" fill="#C0C0C0"/>
  <rect x="230" y="284" width="52" height="32" rx="8" fill="#E6E6FA"/>
  
  <!-- Dispenser opening -->
  <ellipse cx="256" cy="320" rx="15" ry="8" fill="#696969"/>
  
  <!-- Food falling -->
  <circle cx="250" cy="330" r="3" fill="#8B4513"/>
  <circle cx="262" cy="335" r="2" fill="#A0522D"/>
  
  <!-- Cute paw prints around -->
  <g fill="#FFB347" opacity="0.6">
    <circle cx="150" cy="100" r="8"/>
    <circle cx="145" cy="110" r="4"/>
    <circle cx="155" cy="110" r="4"/>
    <circle cx="140" cy="115" r="3"/>
    <circle cx="160" cy="115" r="3"/>
    
    <circle cx="380" cy="120" r="8"/>
    <circle cx="375" cy="130" r="4"/>
    <circle cx="385" cy="130" r="4"/>
    <circle cx="370" cy="135" r="3"/>
    <circle cx="390" cy="135" r="3"/>
    
    <circle cx="100" cy="300" r="8"/>
    <circle cx="95" cy="310" r="4"/>
    <circle cx="105" cy="310" r="4"/>
    <circle cx="90" cy="315" r="3"/>
    <circle cx="110" cy="315" r="3"/>
  </g>
  
  <!-- App title text -->
  <text x="256" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFF">Cat Feeder</text>
</svg>
