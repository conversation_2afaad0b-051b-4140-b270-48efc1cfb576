/**
 * Security and Authentication Module
 * 
 * Handles user authentication and device security
 */

#pragma once

#include "esp_err.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_USERNAME_LENGTH 32
#define MAX_PASSWORD_LENGTH 64
#define MAX_DEVICE_NAME_LENGTH 32
#define BT_PIN_CODE "1234"  // Hardcoded Bluetooth PIN
#define MAX_USERS 5
#define SESSION_TIMEOUT_MINUTES 30

/**
 * User privilege levels
 */
typedef enum {
    USER_LEVEL_GUEST = 0,       ///< Read-only access
    USER_LEVEL_USER = 1,        ///< Normal user operations
    USER_LEVEL_ADMIN = 2        ///< Full configuration access
} user_level_t;

/**
 * User account structure
 */
typedef struct {
    char username[MAX_USERNAME_LENGTH];
    char password_hash[64];     ///< SHA-256 hash of password
    user_level_t level;
    bool enabled;
    uint32_t last_login;        ///< Unix timestamp
    uint32_t login_count;
} user_account_t;

/**
 * Active session structure
 */
typedef struct {
    char username[MAX_USERNAME_LENGTH];
    user_level_t level;
    uint32_t login_time;
    uint32_t last_activity;
    bool is_bluetooth;          ///< True if logged in via Bluetooth
    char device_address[18];    ///< Bluetooth device address
} user_session_t;

/**
 * Device security configuration
 */
typedef struct {
    char device_name[MAX_DEVICE_NAME_LENGTH];
    char bt_pin[8];             ///< Bluetooth PIN code
    bool require_authentication;
    bool allow_guest_access;
    uint32_t session_timeout_minutes;
    uint32_t max_failed_attempts;
    uint32_t lockout_duration_minutes;
    bool enable_encryption;
} security_config_t;

/**
 * Initialize security system
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_init(void);

/**
 * Create default admin user
 * 
 * @param username Admin username
 * @param password Admin password
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_create_admin_user(const char* username, const char* password);

/**
 * Authenticate user
 * 
 * @param username Username
 * @param password Password
 * @param is_bluetooth True if authentication via Bluetooth
 * @param device_address Bluetooth device address (if applicable)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_authenticate(const char* username, const char* password, 
                               bool is_bluetooth, const char* device_address);

/**
 * Check if user is authenticated and session is valid
 * 
 * @param username Username to check
 * @return true if authenticated and session valid
 */
bool security_is_authenticated(const char* username);

/**
 * Get current user session
 * 
 * @param username Username
 * @param session Pointer to store session info
 * @return ESP_OK if session found, ESP_ERR_NOT_FOUND otherwise
 */
esp_err_t security_get_session(const char* username, user_session_t* session);

/**
 * Update session activity (extend timeout)
 * 
 * @param username Username
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_update_activity(const char* username);

/**
 * Logout user
 * 
 * @param username Username to logout
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_logout(const char* username);

/**
 * Check if user has required privilege level
 * 
 * @param username Username
 * @param required_level Required privilege level
 * @return true if user has sufficient privileges
 */
bool security_check_privilege(const char* username, user_level_t required_level);

/**
 * Add new user account
 * 
 * @param username Username
 * @param password Password
 * @param level User privilege level
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_add_user(const char* username, const char* password, user_level_t level);

/**
 * Remove user account
 * 
 * @param username Username to remove
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_remove_user(const char* username);

/**
 * Change user password
 * 
 * @param username Username
 * @param old_password Current password
 * @param new_password New password
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_change_password(const char* username, const char* old_password, const char* new_password);

/**
 * Get security configuration
 * 
 * @param config Pointer to store configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_get_config(security_config_t* config);

/**
 * Update security configuration
 * 
 * @param config New configuration
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_set_config(const security_config_t* config);

/**
 * Generate device ID for multi-device support
 * 
 * @param device_id Buffer to store device ID (minimum 16 bytes)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t security_get_device_id(char* device_id);

/**
 * Validate Bluetooth PIN
 * 
 * @param pin PIN code to validate
 * @return true if PIN is correct
 */
bool security_validate_bt_pin(const char* pin);

/**
 * Clean up expired sessions
 * 
 * @return Number of sessions cleaned up
 */
int security_cleanup_sessions(void);

/**
 * Get list of active sessions
 * 
 * @param sessions Array to store sessions
 * @param max_sessions Maximum number of sessions to return
 * @return Number of active sessions
 */
int security_get_active_sessions(user_session_t* sessions, int max_sessions);

#ifdef __cplusplus
}
#endif
