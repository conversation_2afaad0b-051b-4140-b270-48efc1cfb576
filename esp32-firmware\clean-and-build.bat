@echo off
echo Cleaning build directory...
if exist build (
    rd /s /q build
    echo Build directory removed.
) else (
    echo Build directory does not exist.
)

echo Setting up ESP-IDF...
call "C:\Espressif\frameworks\esp-idf-v5.4.1\export.bat"

echo Setting target to ESP32...
idf.py set-target esp32

echo Building firmware...
idf.py build

echo Flashing to COM3...
idf.py -p COM3 flash

echo Done!
pause
