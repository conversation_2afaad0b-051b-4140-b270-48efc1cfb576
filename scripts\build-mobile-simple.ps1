# Studio 311 LP - Smart Cat Feeder Mobile App Build & Deploy Script
# Simple version for testing

param(
    [switch]$StartApp,
    [switch]$ShowLogs
)

$ErrorActionPreference = "Stop"

Write-Host "🚀 Studio 311 LP - Smart Cat Feeder Mobile Build & Deploy" -ForegroundColor Cyan
Write-Host ""

# Check if Android device is connected
Write-Host "📱 Checking Android device connection..." -ForegroundColor Cyan
try {
    $devices = adb devices
    $connectedDevices = $devices | Select-String "device$" | Measure-Object
    
    if ($connectedDevices.Count -eq 0) {
        Write-Host "❌ No Android device connected!" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "✅ Android device connected" -ForegroundColor Green
} catch {
    Write-Host "❌ ADB not found!" -ForegroundColor Red
    exit 1
}

# Get project path
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$MobileProjectPath = Join-Path $ProjectRoot "mobile-app\CatFeeder.Mobile"

Write-Host "Project: $MobileProjectPath" -ForegroundColor Cyan

# Build and deploy
Write-Host "🔨 Building and deploying mobile app..." -ForegroundColor Cyan

try {
    Push-Location $MobileProjectPath
    
    # Clean and build with deploy
    dotnet clean -c Debug | Out-Null
    dotnet build -f net9.0-android -c Debug -t:Install
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    
    Write-Host "✅ Build and deploy completed!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Build failed: $($_.Exception.Message)" -ForegroundColor Red
    Pop-Location
    exit 1
} finally {
    Pop-Location
}

# Start app if requested
if ($StartApp) {
    Write-Host "🚀 Starting app..." -ForegroundColor Cyan
    try {
        adb shell monkey -p com.studio311lp.catfeeder -c android.intent.category.LAUNCHER 1 | Out-Null
        Start-Sleep -Seconds 2
        
        $runningApp = adb shell ps | Select-String "com.studio311lp.catfeeder"
        if ($runningApp) {
            Write-Host "✅ App started successfully!" -ForegroundColor Green
        } else {
            Write-Host "⚠️ App may not have started" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠️ Failed to start app" -ForegroundColor Yellow
    }
}

# Show logs if requested
if ($ShowLogs) {
    Write-Host "📋 Showing app logs (Ctrl+C to stop)..." -ForegroundColor Cyan
    try {
        adb logcat | Select-String "catfeeder|CatFeeder|Studio311LP" -Context 1
    } catch {
        Write-Host "⚠️ Log monitoring stopped" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 Studio 311 LP - Smart Cat Feeder deployment completed!" -ForegroundColor Green
