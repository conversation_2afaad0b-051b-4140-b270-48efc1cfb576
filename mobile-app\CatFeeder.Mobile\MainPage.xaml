﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:models="clr-namespace:CatFeeder.Models;assembly=CatFeeder.Models"
             xmlns:local="clr-namespace:CatFeeder.Mobile"
             x:Class="CatFeeder.Mobile.MainPage"
             Title="Cat Feeder">

    <ScrollView>
        <VerticalStackLayout Spacing="20" Padding="20">

            <!-- Header -->
            <Label Text="🐱 Cat Feeder Control"
                   FontSize="28"
                   FontAttributes="Bold"
                   HorizontalOptions="Center"
                   Margin="0,20,0,20" />

            <!-- Connection Status -->
            <Frame BackgroundColor="{DynamicResource Primary}"
                   Padding="15"
                   CornerRadius="10">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Device Connection"
                           FontAttributes="Bold"
                           TextColor="White"
                           FontSize="16" />
                    <Label x:Name="ConnectionStatusLabel"
                           Text="Not Connected"
                           TextColor="White" />
                    <Label x:Name="DeviceNameLabel"
                           Text="No device selected"
                           TextColor="White"
                           FontSize="12" />
                </VerticalStackLayout>
            </Frame>

            <!-- Device Discovery -->
            <Frame BackgroundColor="LightBlue"
                   Padding="15"
                   CornerRadius="10">
                <VerticalStackLayout Spacing="15">
                    <Label Text="Device Discovery"
                           FontAttributes="Bold"
                           FontSize="16" />

                    <Button x:Name="ScanButton"
                            Text="Scan for Cat Feeders"
                            Clicked="OnScanClicked"
                            BackgroundColor="{DynamicResource Primary}" />

                    <CollectionView x:Name="DevicesCollectionView"
                                    ItemsSource="{Binding DiscoveredDevices}"
                                    SelectionMode="Single"
                                    SelectionChanged="OnDeviceSelected"
                                    HeightRequest="120">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:BluetoothDeviceInfo">
                                <Grid Padding="10"
                                      RowDefinitions="Auto,Auto"
                                      BackgroundColor="White"
                                      Margin="0,2">
                                    <Label Grid.Row="0"
                                           Text="{Binding Name}"
                                           FontAttributes="Bold" />
                                    <Label Grid.Row="1"
                                           Text="{Binding Address}"
                                           FontSize="12"
                                           TextColor="Gray" />
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>

                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <Button Grid.Column="0"
                                x:Name="ConnectButton"
                                Text="Connect"
                                Clicked="OnConnectClicked"
                                IsEnabled="False"
                                BackgroundColor="Green" />
                        <Button Grid.Column="1"
                                x:Name="DisconnectButton"
                                Text="Disconnect"
                                Clicked="OnDisconnectClicked"
                                IsEnabled="False"
                                BackgroundColor="Red" />
                    </Grid>
                </VerticalStackLayout>
            </Frame>

            <!-- Device Control -->
            <Frame BackgroundColor="LightGreen"
                   Padding="15"
                   CornerRadius="10">
                <VerticalStackLayout Spacing="15">
                    <Label Text="Device Control"
                           FontAttributes="Bold"
                           FontSize="16" />

                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <Button Grid.Column="0"
                                x:Name="FeedButton"
                                Text="Feed Now"
                                Clicked="OnFeedClicked"
                                IsEnabled="False"
                                BackgroundColor="Orange" />
                        <Button Grid.Column="1"
                                x:Name="StatusButton"
                                Text="Get Status"
                                Clicked="OnGetStatusClicked"
                                IsEnabled="False"
                                BackgroundColor="{DynamicResource Primary}" />
                    </Grid>

                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <Button Grid.Column="0"
                                x:Name="ConfigButton"
                                Text="Configure"
                                Clicked="OnConfigureClicked"
                                IsEnabled="False"
                                BackgroundColor="Purple" />
                        <Button Grid.Column="1"
                                x:Name="TestButton"
                                Text="Test Device"
                                Clicked="OnTestClicked"
                                IsEnabled="False"
                                BackgroundColor="DarkBlue" />
                    </Grid>
                </VerticalStackLayout>
            </Frame>

            <!-- Device Status -->
            <Frame BackgroundColor="LightYellow"
                   Padding="15"
                   CornerRadius="10">
                <VerticalStackLayout Spacing="10">
                    <Label Text="Device Status"
                           FontAttributes="Bold"
                           FontSize="16" />
                    <Label x:Name="DeviceStatusLabel"
                           Text="Connect to device to see status"
                           FontSize="12" />
                </VerticalStackLayout>
            </Frame>

            <!-- Activity Log -->
            <Frame BackgroundColor="LightGray"
                   Padding="15"
                   CornerRadius="10">
                <VerticalStackLayout Spacing="10">
                    <Grid ColumnDefinitions="*,Auto">
                        <Label Grid.Column="0"
                               Text="Activity Log"
                               FontAttributes="Bold"
                               FontSize="16" />
                        <Button Grid.Column="1"
                                Text="Clear"
                                Clicked="OnClearLogClicked"
                                FontSize="12"
                                Padding="5"
                                BackgroundColor="Gray" />
                    </Grid>
                    <ScrollView HeightRequest="150">
                        <Label x:Name="ActivityLogLabel"
                               Text="App started - Ready to connect to Cat Feeder"
                               FontFamily="Courier"
                               FontSize="11" />
                    </ScrollView>
                </VerticalStackLayout>
            </Frame>

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
