/**
 * Bluetooth Manager Implementation
 *
 * This module handles Bluetooth Serial Port Profile (SPP) communication for the Cat Feeder.
 * It provides a command-based interface for mobile apps to control the device.
 *
 * Key Features:
 * - SPP (Serial Port Profile) server for mobile app communication
 * - Command parsing and response handling
 * - Device pairing and authentication with PIN
 * - Connection status management
 * - Multiple device support (up to BT_MAX_CONNECTED_DEVICES)
 *
 * External Libraries Used:
 * - esp_bt.h: ESP32 Bluetooth controller management
 * - esp_bt_main.h: Bluedroid stack initialization and management
 * - esp_gap_bt_api.h: Generic Access Profile for device discovery and pairing
 * - esp_spp_api.h: Serial Port Profile for data communication
 * - freertos/: Real-time operating system for task management
 */

#include "bluetooth_manager.h"
#include "esp_log.h"          // ESP32 logging system for debug output
#include "esp_bt.h"           // Bluetooth controller management (enable/disable BT radio)
#include "esp_bt_main.h"      // Bluedroid stack management (BT protocol stack)
#include "esp_gap_bt_api.h"   // Generic Access Profile (device discovery, pairing, security)
#include "esp_spp_api.h"      // Serial Port Profile (RFCOMM-based serial communication)
#include "freertos/FreeRTOS.h" // Real-time OS core functions
#include "freertos/task.h"    // Task creation and management
#include "freertos/queue.h"   // Inter-task communication queues
#include "esp_timer.h"        // ESP32 timer functions
#include "esp_bt_device.h"    // Bluetooth device functions
#include "cJSON.h"            // JSON parsing library
#include <string.h>           // Standard C string manipulation functions
#include <stdio.h>            // Standard C I/O functions (sprintf, etc.)
#include <inttypes.h>         // Integer type format macros

// Logging tag for this module - used by ESP_LOGI, ESP_LOGE, etc.
static const char *TAG = "BT_MANAGER";

// ============================================================================
// GLOBAL STATE VARIABLES
// ============================================================================

/**
 * Current Bluetooth service status
 * Values: BT_STATUS_DISABLED, BT_STATUS_INITIALIZING, BT_STATUS_DISCOVERABLE, BT_STATUS_CONNECTED
 */
static bt_status_t bt_status = BT_STATUS_DISABLED;

/**
 * Bluetooth configuration settings (device name, PIN, security settings)
 * Contains: device_name[32], pin_code[16], auto_accept_connections, require_authentication, etc.
 */
static bt_config_t bt_config;

/**
 * Array of currently connected devices information
 * Each entry contains: address[6], name[32], is_authenticated, username[16], connection_time, etc.
 * Maximum simultaneous connections defined by BT_MAX_CONNECTED_DEVICES
 */
static bt_device_info_t connected_devices[BT_MAX_CONNECTED_DEVICES];

/**
 * Number of currently connected devices (0 to BT_MAX_CONNECTED_DEVICES)
 */
static int connected_device_count = 0;

/**
 * Callback function pointer for connection status changes
 * Called when devices connect/disconnect - allows main app to react to connection events
 * Function signature: void callback(esp_bd_addr_t device_addr, bool connected)
 */
static bt_connection_callback_t connection_callback = NULL;

// ============================================================================
// COMMAND HANDLING SYSTEM
// ============================================================================

/**
 * Structure to map command strings to their handler functions
 * command[32]: Command string (e.g., "PING", "GET_STATUS")
 * handler: Function pointer to execute when command is received
 */
typedef struct {
    char command[32];                    // Command name (null-terminated string)
    bt_command_handler_t handler;        // Function to handle this command
} command_handler_entry_t;

/**
 * Array of registered command handlers
 * Maximum 32 different commands supported
 */
static command_handler_entry_t command_handlers[32];

/**
 * Number of currently registered command handlers (0-32)
 */
static int command_handler_count = 0;

// ============================================================================
// SPP (Serial Port Profile) COMMUNICATION
// ============================================================================

/**
 * SPP connection handle for data communication
 * 0 = no active connection, >0 = active SPP connection handle
 * Used by esp_spp_write() to send data to connected device
 */
static uint32_t spp_handle = 0;

// ============================================================================
// COMMAND PARSING FUNCTIONS
// ============================================================================

/**
 * Parse incoming command string into command and parameters
 *
 * Command format: "COMMAND:parameter1,parameter2,parameter3"
 * Examples: "PING" (no parameters), "MANUAL_FEED:200", "WIFI_CONNECT:MyNetwork,password123"
 *
 * @param data Input command string from mobile app (null-terminated)
 * @param command Output buffer for command name (e.g., "PING", "MANUAL_FEED")
 * @param params Output buffer for parameters (e.g., "200", "MyNetwork,password123")
 * @param max_len Maximum length for command and params buffers (including null terminator)
 *
 * @return ESP_OK on success, ESP_ERR_* on failure
 *
 * External functions used:
 * - strchr(str, char): Find first occurrence of character in string (returns pointer or NULL)
 * - strncpy(dest, src, n): Copy at most n characters from src to dest
 */
static esp_err_t parse_command(const char* data, char* command, char* params, size_t max_len)
{
    // Find the colon separator between command and parameters
    // strchr() returns pointer to first ':' or NULL if not found
    const char* colon = strchr(data, ':');

    if (colon == NULL) {
        // Command without parameters (e.g., "PING", "GET_STATUS")
        strncpy(command, data, max_len - 1);  // Copy command, leave space for null terminator
        command[max_len - 1] = '\0';          // Ensure null termination
        params[0] = '\0';                     // Empty parameters string
        return ESP_OK;
    }

    // Command with parameters (e.g., "MANUAL_FEED:200")
    size_t cmd_len = colon - data;            // Calculate command length (pointer arithmetic)
    if (cmd_len >= max_len) cmd_len = max_len - 1;  // Prevent buffer overflow

    // Extract command part (before colon)
    strncpy(command, data, cmd_len);          // Copy command portion
    command[cmd_len] = '\0';                  // Null terminate command

    // Extract parameters part (after colon)
    strncpy(params, colon + 1, max_len - 1);  // Copy parameters (skip the colon)
    params[max_len - 1] = '\0';               // Ensure null termination

    return ESP_OK;
}

/**
 * Process received command from mobile app
 *
 * This function handles the complete command processing pipeline:
 * 1. Parse command string into command and parameters
 * 2. Find registered handler for the command
 * 3. Look up user authentication info for the sending device
 * 4. Execute the command handler
 * 5. Send response back to mobile app
 *
 * @param data Raw command string received from mobile app (e.g., "MANUAL_FEED:200")
 * @param remote_addr Bluetooth address of the device that sent the command (6 bytes)
 *
 * External functions used:
 * - snprintf(buf, size, format, ...): Safe formatted string printing with buffer size limit
 * - strcmp(str1, str2): Compare two strings, returns 0 if equal
 * - memcmp(ptr1, ptr2, n): Compare n bytes of memory, returns 0 if equal
 */
static void process_command(const char* data, esp_bd_addr_t remote_addr)
{
    char command[32];                        // Buffer for command name (e.g., "MANUAL_FEED")
    char params[BT_COMMAND_MAX_LEN];         // Buffer for parameters (e.g., "200")
    char response[BT_RESPONSE_MAX_LEN];      // Buffer for response to send back

    // Step 1: Parse the incoming command string
    if (parse_command(data, command, params, sizeof(command)) != ESP_OK) {
        // Command parsing failed - send error response
        snprintf(response, sizeof(response), "ERROR:Invalid command format");
        bluetooth_manager_send_response(remote_addr, response);
        return;
    }

    // Log the command for debugging
    ESP_LOGI(TAG, "Processing command: %s, params: %s", command, params);

    // Step 2: Find the registered handler function for this command
    bt_command_handler_t handler = NULL;     // Function pointer to command handler
    for (int i = 0; i < command_handler_count; i++) {
        // strcmp() compares strings, returns 0 if they match exactly
        if (strcmp(command_handlers[i].command, command) == 0) {
            handler = command_handlers[i].handler;
            break;
        }
    }

    if (handler == NULL) {
        // No handler found for this command
        snprintf(response, sizeof(response), "ERROR:Unknown command: %s", command);
        bluetooth_manager_send_response(remote_addr, response);
        return;
    }

    // Step 3: Find device info for authentication and user identification
    const char* username = "guest";         // Default username if device not found
    for (int i = 0; i < connected_device_count; i++) {
        // memcmp() compares ESP_BD_ADDR_LEN (6) bytes of Bluetooth addresses
        if (memcmp(connected_devices[i].address, remote_addr, ESP_BD_ADDR_LEN) == 0) {
            username = connected_devices[i].username;
            break;
        }
    }

    // Step 4: Execute the command handler function
    // Handler signature: esp_err_t handler(command, params, response_buffer, buffer_size, username)
    esp_err_t result = handler(command, params, response, sizeof(response), username);

    if (result != ESP_OK) {
        // Command execution failed - override response with generic error
        snprintf(response, sizeof(response), "ERROR:Command execution failed");
    }

    // Step 5: Send response back to the mobile app
    bluetooth_manager_send_response(remote_addr, response);
}

// ============================================================================
// SPP (Serial Port Profile) EVENT CALLBACKS
// ============================================================================

/**
 * SPP (Serial Port Profile) callback function
 *
 * This function is called by the ESP32 Bluetooth stack when SPP events occur.
 * SPP provides a serial-like communication channel over Bluetooth.
 *
 * @param event Type of SPP event that occurred (init, start, connect, data, etc.)
 * @param param Event-specific parameters containing additional data
 *
 * External ESP-IDF functions used:
 * - esp_spp_start_srv(): Start SPP server to accept incoming connections
 * - esp_timer_get_time(): Get current time in microseconds since boot
 * - malloc(size): Allocate memory from heap
 * - free(ptr): Free previously allocated memory
 * - strchr(str, char): Find character in string
 */
static void esp_spp_cb(esp_spp_cb_event_t event, esp_spp_cb_param_t *param)
{
    switch (event) {
        case ESP_SPP_INIT_EVT:
            // SPP service has been initialized successfully
            ESP_LOGI(TAG, "ESP_SPP_INIT_EVT");

            // Start SPP server to listen for incoming connections
            // Parameters:
            // - ESP_SPP_SEC_AUTHENTICATE: Require authentication (PIN pairing)
            // - ESP_SPP_ROLE_SLAVE: Act as server (mobile app connects to us)
            // - 0: Use any available RFCOMM channel
            // - "CatFeeder_SPP": Service name visible to connecting devices
            esp_spp_start_srv(ESP_SPP_SEC_AUTHENTICATE, ESP_SPP_ROLE_SLAVE, 0, "CatFeeder_SPP");
            break;

        case ESP_SPP_START_EVT:
            // SPP server started successfully and is ready to accept connections
            ESP_LOGI(TAG, "ESP_SPP_START_EVT");
            bt_status = BT_STATUS_DISCOVERABLE;  // Update global status
            break;
            
        case ESP_SPP_SRV_OPEN_EVT:
            // A mobile device has successfully connected to our SPP server
            ESP_LOGI(TAG, "ESP_SPP_SRV_OPEN_EVT: handle=%" PRIu32, param->srv_open.handle);

            // Store the connection handle for sending data back to this device
            // This handle is used by esp_spp_write() to send responses
            spp_handle = param->srv_open.handle;

            // Add the new device to our connected devices list
            if (connected_device_count < BT_MAX_CONNECTED_DEVICES) {
                bt_device_info_t* device = &connected_devices[connected_device_count];

                // Copy the 6-byte Bluetooth address of the connected device
                // ESP_BD_ADDR_LEN = 6 bytes (e.g., AA:BB:CC:DD:EE:FF)
                memcpy(device->address, param->srv_open.rem_bda, ESP_BD_ADDR_LEN);

                // Store the connection handle for this device
                device->handle = param->srv_open.handle;

                // Initialize device information with defaults
                strcpy(device->name, "Unknown");        // Device name (will be updated later)
                device->is_authenticated = false;       // Not authenticated yet
                strcpy(device->username, "guest");      // Default username

                // Record connection timing for session management
                // esp_timer_get_time() returns microseconds since boot, convert to seconds
                device->connection_time = esp_timer_get_time() / 1000000;
                device->last_activity = device->connection_time;

                connected_device_count++;               // Increment connected device counter

                bt_status = BT_STATUS_CONNECTED;        // Update global status

                // Notify the main application about the new connection
                if (connection_callback) {
                    // Call registered callback with device address and connected=true
                    connection_callback(param->srv_open.rem_bda, true);
                }
            }
            break;
            
        case ESP_SPP_CLOSE_EVT:
            ESP_LOGI(TAG, "ESP_SPP_CLOSE_EVT: handle=%" PRIu32, param->close.handle);
            
            // Remove from connected devices using handle
            esp_bd_addr_t disconnected_addr;
            bool found = false;
            for (int i = 0; i < connected_device_count; i++) {
                if (connected_devices[i].handle == param->close.handle) {
                    // Save address for callback
                    memcpy(disconnected_addr, connected_devices[i].address, ESP_BD_ADDR_LEN);
                    found = true;

                    // Shift remaining devices
                    for (int j = i; j < connected_device_count - 1; j++) {
                        connected_devices[j] = connected_devices[j + 1];
                    }
                    connected_device_count--;
                    break;
                }
            }
            
            if (connected_device_count == 0) {
                bt_status = BT_STATUS_DISCOVERABLE;
            }
            
            if (connection_callback && found) {
                connection_callback(disconnected_addr, false);
            }
            break;
            
        case ESP_SPP_DATA_IND_EVT:
            ESP_LOGI(TAG, "ESP_SPP_DATA_IND_EVT: len=%d", param->data_ind.len);
            
            // Null-terminate received data
            char* data = malloc(param->data_ind.len + 1);
            memcpy(data, param->data_ind.data, param->data_ind.len);
            data[param->data_ind.len] = '\0';
            
            // Remove newline characters
            char* newline = strchr(data, '\n');
            if (newline) *newline = '\0';
            newline = strchr(data, '\r');
            if (newline) *newline = '\0';
            
            ESP_LOGI(TAG, "Received: %s", data);
            
            // Find device address from handle
            esp_bd_addr_t sender_addr;
            bool addr_found = false;
            for (int i = 0; i < connected_device_count; i++) {
                if (connected_devices[i].handle == param->data_ind.handle) {
                    memcpy(sender_addr, connected_devices[i].address, ESP_BD_ADDR_LEN);
                    addr_found = true;
                    break;
                }
            }

            // Process command if we found the sender
            if (addr_found) {
                process_command(data, sender_addr);
            }
            
            free(data);
            break;
            
        default:
            break;
    }
}

/**
 * GAP callback function
 */
static void esp_bt_gap_cb(esp_bt_gap_cb_event_t event, esp_bt_gap_cb_param_t *param)
{
    switch (event) {
        case ESP_BT_GAP_AUTH_CMPL_EVT:
            if (param->auth_cmpl.stat == ESP_BT_STATUS_SUCCESS) {
                ESP_LOGI(TAG, "Authentication success: %s", param->auth_cmpl.device_name);
            } else {
                ESP_LOGE(TAG, "Authentication failed, status:%d", param->auth_cmpl.stat);
            }
            break;
            
        case ESP_BT_GAP_PIN_REQ_EVT:
            ESP_LOGI(TAG, "ESP_BT_GAP_PIN_REQ_EVT min_16_digit:%d", param->pin_req.min_16_digit);
            if (param->pin_req.min_16_digit) {
                ESP_LOGI(TAG, "Input pin code: 0000 0000 0000 0000");
                esp_bt_pin_code_t pin_code = {0};
                esp_bt_gap_pin_reply(param->pin_req.bda, true, 16, pin_code);
            } else {
                ESP_LOGI(TAG, "Input pin code: %s", bt_config.pin_code);
                esp_bt_pin_code_t pin_code;
                strcpy((char*)pin_code, bt_config.pin_code);
                esp_bt_gap_pin_reply(param->pin_req.bda, true, strlen(bt_config.pin_code), pin_code);
            }
            break;
            
        default:
            break;
    }
}

/**
 * Initialize Bluetooth manager
 */
esp_err_t bluetooth_manager_init(void)
{
    ESP_LOGI(TAG, "Initializing Bluetooth manager");
    
    // Set default configuration
    strcpy(bt_config.device_name, "CatFeeder_ESP32");
    strcpy(bt_config.pin_code, "1234");
    bt_config.auto_accept_connections = true;
    bt_config.require_authentication = true;
    bt_config.discoverable_timeout = 0; // Always discoverable
    bt_config.enable_encryption = true;
    
    // Initialize connected devices array
    connected_device_count = 0;
    command_handler_count = 0;
    
    return ESP_OK;
}

/**
 * Start Bluetooth service
 */
esp_err_t bluetooth_manager_start(void)
{
    ESP_LOGI(TAG, "Starting Bluetooth service");
    
    bt_status = BT_STATUS_INITIALIZING;
    
    // Release BLE memory
    esp_err_t ret = esp_bt_controller_mem_release(ESP_BT_MODE_BLE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to release BLE memory: %s", esp_err_to_name(ret));
        return ret;
    }

    // Initialize BT controller
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BT controller: %s", esp_err_to_name(ret));
        return ret;
    }

    // Enable BT controller in Classic mode only
    ret = esp_bt_controller_enable(ESP_BT_MODE_CLASSIC_BT);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable BT controller: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // Initialize Bluedroid
    ret = esp_bluedroid_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize Bluedroid: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_bluedroid_enable();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to enable Bluedroid: %s", esp_err_to_name(ret));
        return ret;
    }

    // Register callbacks
    ret = esp_bt_gap_register_callback(esp_bt_gap_cb);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register GAP callback: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_spp_register_callback(esp_spp_cb);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to register SPP callback: %s", esp_err_to_name(ret));
        return ret;
    }

    ret = esp_spp_enhanced_init(ESP_SPP_MODE_CB);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize SPP: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // Set device name
    ESP_ERROR_CHECK(esp_bt_gap_set_device_name(bt_config.device_name));
    
    // Set discoverable and connectable
    ESP_ERROR_CHECK(esp_bt_gap_set_scan_mode(ESP_BT_CONNECTABLE, ESP_BT_GENERAL_DISCOVERABLE));
    
    ESP_LOGI(TAG, "Bluetooth service started - Device: %s, PIN: %s", 
             bt_config.device_name, bt_config.pin_code);
    
    return ESP_OK;
}

/**
 * Register command handler
 */
esp_err_t bluetooth_manager_register_command(const char* command, bt_command_handler_t handler)
{
    if (command_handler_count >= 32) {
        return ESP_ERR_NO_MEM;
    }
    
    strcpy(command_handlers[command_handler_count].command, command);
    command_handlers[command_handler_count].handler = handler;
    command_handler_count++;
    
    ESP_LOGI(TAG, "Registered command: %s", command);
    return ESP_OK;
}

/**
 * Send response to connected device
 */
esp_err_t bluetooth_manager_send_response(esp_bd_addr_t device_addr, const char* response)
{
    if (spp_handle == 0) {
        return ESP_ERR_INVALID_STATE;
    }
    
    ESP_LOGI(TAG, "Sending response: %s", response);
    
    // Add newline to response
    char response_with_newline[BT_RESPONSE_MAX_LEN + 2];
    snprintf(response_with_newline, sizeof(response_with_newline), "%s\n", response);
    
    esp_err_t ret = esp_spp_write(spp_handle, strlen(response_with_newline), (uint8_t*)response_with_newline);
    return ret;
}

/**
 * Get Bluetooth status
 */
bt_status_t bluetooth_manager_get_status(void)
{
    return bt_status;
}

/**
 * Get connected devices
 */
int bluetooth_manager_get_connected_devices(bt_device_info_t* devices, int max_devices)
{
    int count = connected_device_count < max_devices ? connected_device_count : max_devices;
    memcpy(devices, connected_devices, count * sizeof(bt_device_info_t));
    return count;
}

/**
 * Set connection callback
 */
esp_err_t bluetooth_manager_set_connection_callback(bt_connection_callback_t callback)
{
    connection_callback = callback;
    return ESP_OK;
}
