idf_component_register(
    SRCS
        "main.c"
        "bt_commands.c"
        "feeding_history.c"
        "app_config.c"
        "feeding_controller.c"
        "version.c"
    INCLUDE_DIRS
        "."
    REQUIRES
        motor_control
        wifi_manager
        bluetooth_manager
        web_server
        scheduler
        config
        nvs_flash
        esp_wifi
        esp_http_server
        bt
        freertos
        driver
        esp_timer
        esp_netif
)
