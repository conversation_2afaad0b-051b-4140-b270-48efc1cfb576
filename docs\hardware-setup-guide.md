# Hardware Setup Guide

This guide covers the hardware setup for the ESP32 Cat Feeder project, including both stepper and DC motor configurations.

## ESP32 Development Board

### Recommended Boards
- **ESP32-DevKitC-V4** (Most popular, good documentation)
- **ESP32-WROOM-32** development board
- **ESP32-S3-DevKitC-1** (newer, more features)

### Key Requirements
- At least 4MB flash memory
- Built-in USB-to-UART converter
- Breadboard-friendly pin layout
- 3.3V and 5V power rails

## Motor Options

### Option 1: Stepper Motor (Recommended for Precision)

**Advantages:**
- Precise portion control
- No feedback sensors needed
- Repeatable positioning
- Quiet operation

**Components:**
- **Motor**: NEMA 17 stepper motor (1.8° step angle, 200 steps/rev)
- **Driver**: A4988 or DRV8825 stepper driver
- **Power**: 12V 2A power supply

**Wiring (Stepper):**
```
ESP32 Pin    →    A4988 Driver
GPIO 2       →    STEP
GPIO 4       →    DIR
GPIO 5       →    ENABLE (optional)
3.3V         →    VDD
GND          →    GND

A4988 Driver →    Power/Motor
VMOT         →    12V+
GND          →    12V- and ESP32 GND
1A, 1B       →    Motor Coil 1
2A, 2B       →    Motor Coil 2
```

**Configuration Example:**
```c
stepper_config_t stepper_config = {
    .step_pin = 2,
    .dir_pin = 4,
    .enable_pin = 5,
    .steps_per_rev = 200,
    .microsteps = 1,
    .max_speed_hz = 1000,
    .acceleration = 500,
    .invert_direction = false,
    .invert_enable = false
};
```

### Option 2: DC Motor with Time Control

**Advantages:**
- Simple and cheap
- High torque available
- Easy to source

**Components:**
- **Motor**: 12V geared DC motor (10-100 RPM)
- **Driver**: L298N motor driver module
- **Power**: 12V 2A power supply

**Wiring (DC Time-based):**
```
ESP32 Pin    →    L298N Driver
GPIO 2       →    ENA (PWM)
GPIO 4       →    IN1
GPIO 5       →    IN2
3.3V         →    VCC (logic)
GND          →    GND

L298N Driver →    Power/Motor
VIN          →    12V+
GND          →    12V- and ESP32 GND
OUT1, OUT2   →    Motor terminals
```

**Configuration Example:**
```c
dc_config_t dc_config = {
    .pwm_pin = 2,
    .dir_pin = 4,
    .enable_pin = 5,
    .sensor_pin = -1,  // Not used for time-based
    .pwm_frequency = 1000,
    .pwm_duty_cycle = 80,
    .run_time_ms = 2000,  // 2 seconds
    .invert_direction = false,
    .invert_enable = false
};
```

### Option 3: DC Motor with Sensor Control

**Advantages:**
- Consistent portions regardless of food type
- Automatic stop when container is full
- Adaptive to different food densities

**Additional Components:**
- **Sensor**: Limit switch or optical sensor
- **Mounting**: Sensor bracket for food container

**Wiring (DC Sensor-based):**
```
ESP32 Pin    →    L298N + Sensor
GPIO 2       →    ENA (PWM)
GPIO 4       →    IN1
GPIO 5       →    IN2
GPIO 18      →    Limit switch (with pullup)
3.3V         →    VCC and switch pullup
GND          →    GND and switch common
```

**Configuration Example:**
```c
dc_config_t dc_config = {
    .pwm_pin = 2,
    .dir_pin = 4,
    .enable_pin = 5,
    .sensor_pin = 18,
    .pwm_frequency = 1000,
    .pwm_duty_cycle = 80,
    .run_time_ms = 10000,  // Max time (safety)
    .invert_direction = false,
    .invert_enable = false,
    .sensor_active_low = true
};
```

## Optional Components

### Food Level Sensor (Load Cell)

**Components:**
- **Load Cell**: 1kg-5kg capacity
- **Amplifier**: HX711 24-bit ADC
- **Mounting**: Platform under food container

**Wiring:**
```
ESP32 Pin    →    HX711
GPIO 19      →    DOUT
GPIO 21      →    SCK
3.3V         →    VCC
GND          →    GND

HX711        →    Load Cell
E+           →    Red wire
E-           →    Black wire
A+           →    White wire
A-           →    Green wire
```

### LED Status Indicators

**Components:**
- **LEDs**: 5mm LEDs (Power, WiFi, Feeding, Error)
- **Resistors**: 220Ω current limiting resistors

**Wiring:**
```
ESP32 Pin    →    LED + Resistor
GPIO 25      →    Power LED (Green)
GPIO 26      →    WiFi LED (Blue)
GPIO 27      →    Feeding LED (Yellow)
GPIO 32      →    Error LED (Red)
GND          →    LED cathodes
```

### Manual Feed Button

**Components:**
- **Button**: Momentary push button
- **Resistor**: 10kΩ pullup resistor

**Wiring:**
```
ESP32 Pin    →    Button
GPIO 0       →    Button (one side)
3.3V         →    10kΩ resistor → Button
GND          →    Button (other side)
```

### Buzzer Notifications

**Components:**
- **Buzzer**: 5V active buzzer

**Wiring:**
```
ESP32 Pin    →    Buzzer
GPIO 33      →    Buzzer positive
GND          →    Buzzer negative
```

## Power Supply Considerations

### Power Requirements
- **ESP32**: 3.3V, ~240mA (WiFi active)
- **Stepper Motor**: 12V, 0.5-2A depending on motor
- **DC Motor**: 12V, 0.5-3A depending on motor
- **Total**: 12V 3A power supply recommended

### Power Distribution
```
12V PSU → L298N/A4988 → Motor
12V PSU → Buck Converter → 5V → ESP32 Dev Board
```

**Alternative:** Use ESP32 dev board with built-in voltage regulator:
```
12V PSU → ESP32 VIN pin (if board supports it)
```

## Mechanical Assembly

### Food Dispensing Mechanism

**Stepper Motor Design:**
1. **Auger System**: Helical screw for precise dispensing
2. **Rotating Disc**: Disc with holes for portion control
3. **Sliding Gate**: Linear actuator for gate control

**DC Motor Design:**
1. **Vibrating Feeder**: Vibration-based dispensing
2. **Rotating Drum**: Drum with chambers
3. **Conveyor System**: Belt-driven food transport

### Mounting Considerations
- Secure motor mounting to prevent vibration
- Food-safe materials for food contact surfaces
- Easy access for cleaning and maintenance
- Protection from moisture and dust

## Safety Features

### Hardware Safety
- **Fuses**: Inline fuses for motor power
- **Flyback Diodes**: Protection for inductive loads
- **Thermal Protection**: Temperature monitoring
- **Emergency Stop**: Manual override capability

### Software Safety
- **Watchdog Timer**: System reset on hang
- **Maximum Run Time**: Prevent motor overrun
- **Daily Limits**: Prevent overfeeding
- **Error Detection**: Motor stall detection

## Testing and Calibration

### Initial Testing
1. **Power On Test**: Verify all voltages
2. **Motor Test**: Basic movement without load
3. **Sensor Test**: Verify all inputs
4. **Communication Test**: WiFi and Bluetooth

### Calibration Process
1. **Motor Calibration**: Steps per portion
2. **Sensor Calibration**: Load cell zero and span
3. **Timing Calibration**: Feeding duration
4. **Portion Calibration**: Actual vs. commanded

## Troubleshooting

### Common Issues
- **Motor not moving**: Check power, wiring, driver
- **Erratic movement**: Check power supply capacity
- **WiFi connection issues**: Check antenna, power
- **Sensor readings**: Check wiring, calibration

### Debug Tools
- **Multimeter**: Voltage and continuity testing
- **Oscilloscope**: Signal timing verification
- **Serial Monitor**: ESP32 debug output
- **Logic Analyzer**: Digital signal analysis

## Bill of Materials (BOM)

### Core Components (Stepper)
| Component | Quantity | Estimated Cost |
|-----------|----------|----------------|
| ESP32-DevKitC | 1 | $10 |
| NEMA 17 Stepper | 1 | $15 |
| A4988 Driver | 1 | $3 |
| 12V 2A PSU | 1 | $8 |
| Breadboard/PCB | 1 | $5 |
| Wires/Connectors | - | $5 |
| **Total** | | **~$46** |

### Optional Add-ons
| Component | Quantity | Estimated Cost |
|-----------|----------|----------------|
| Load Cell + HX711 | 1 | $10 |
| LEDs + Resistors | 4 | $2 |
| Push Button | 1 | $1 |
| Buzzer | 1 | $2 |
| **Total Optional** | | **~$15** |

**Grand Total: ~$61 for full-featured system**
