﻿using System.Collections.ObjectModel;
using CatFeeder.Core.Services;
using CatFeeder.Models;

namespace CatFeeder.Mobile;

public partial class MainPage : ContentPage
{
    private readonly IBluetoothService _bluetoothService;
    private readonly IFeedingHistoryService _feedingHistoryService;
    private BluetoothDeviceInfo? _selectedDevice;

    public ObservableCollection<BluetoothDeviceInfo> DiscoveredDevices { get; } = new();

    public MainPage(IBluetoothService bluetoothService, IFeedingHistoryService feedingHistoryService)
    {
        InitializeComponent();

        _bluetoothService = bluetoothService;
        _feedingHistoryService = feedingHistoryService;

        // Subscribe to events
        _bluetoothService.ConnectionStatusChanged += OnConnectionStatusChanged;
        _bluetoothService.CommandResponseReceived += OnCommandResponseReceived;
        _feedingHistoryService.HistoryUpdated += OnFeedingHistoryUpdated;

        BindingContext = this;

        InitializeAsync();
    }

    private async void InitializeAsync()
    {
        await _feedingHistoryService.InitializeAsync();
        UpdateUI();
        LogActivity("App started - Ready to connect to Cat Feeder");

        // Load and display recent feeding statistics
        await UpdateFeedingStatsAsync();
    }

    private async void OnScanClicked(object sender, EventArgs e)
    {
        try
        {
            ScanButton.IsEnabled = false;
            ScanButton.Text = "Scanning...";

            LogActivity("Starting device scan...");

            var devices = await _bluetoothService.ScanForDevicesAsync(TimeSpan.FromSeconds(10));

            DiscoveredDevices.Clear();
            foreach (var device in devices)
            {
                DiscoveredDevices.Add(device);
                LogActivity($"Found device: {device.Name} ({device.Address})");
            }

            LogActivity($"Scan completed. Found {devices.Count} device(s).");
        }
        catch (Exception ex)
        {
            LogActivity($"Scan failed: {ex.Message}");
            await DisplayAlert("Error", $"Failed to scan for devices: {ex.Message}", "OK");
        }
        finally
        {
            ScanButton.IsEnabled = true;
            ScanButton.Text = "Scan for Cat Feeders";
        }
    }

    private void OnDeviceSelected(object sender, SelectionChangedEventArgs e)
    {
        if (e.CurrentSelection.FirstOrDefault() is BluetoothDeviceInfo device)
        {
            _selectedDevice = device;
            DeviceNameLabel.Text = $"Selected: {device.Name}";
            ConnectButton.IsEnabled = true;
            LogActivity($"Selected device: {device.Name} ({device.Address})");
        }
    }

    private async void OnConnectClicked(object sender, EventArgs e)
    {
        if (_selectedDevice == null)
        {
            await DisplayAlert("Error", "Please select a device first", "OK");
            return;
        }

        try
        {
            ConnectButton.IsEnabled = false;
            ConnectButton.Text = "Connecting...";

            LogActivity($"Connecting to {_selectedDevice.Name}...");

            var success = await _bluetoothService.ConnectAsync(_selectedDevice, "1234");

            if (success)
            {
                LogActivity("Connected successfully!");
                await DisplayAlert("Success", "Connected to Cat Feeder", "OK");
            }
            else
            {
                LogActivity("Connection failed!");
                await DisplayAlert("Error", "Failed to connect to device", "OK");
            }
        }
        catch (Exception ex)
        {
            LogActivity($"Connection error: {ex.Message}");
            await DisplayAlert("Error", $"Connection failed: {ex.Message}", "OK");
        }
        finally
        {
            ConnectButton.Text = "Connect";
            UpdateUI();
        }
    }

    private async void OnDisconnectClicked(object sender, EventArgs e)
    {
        try
        {
            DisconnectButton.IsEnabled = false;
            LogActivity("Disconnecting...");

            var success = await _bluetoothService.DisconnectAsync();

            if (success)
            {
                LogActivity("Disconnected successfully!");
            }
            else
            {
                LogActivity("Disconnection failed!");
            }
        }
        catch (Exception ex)
        {
            LogActivity($"Disconnection error: {ex.Message}");
            await DisplayAlert("Error", $"Disconnection failed: {ex.Message}", "OK");
        }
        finally
        {
            UpdateUI();
        }
    }

    private async void OnFeedClicked(object sender, EventArgs e)
    {
        try
        {
            FeedButton.IsEnabled = false;
            LogActivity("Executing manual feeding...");

            var command = new DeviceCommand { Command = "MANUAL_FEED", Parameters = "200" };
            var response = await _bluetoothService.SendCommandAsync(command);

            if (response.Success)
            {
                LogActivity("Manual feeding completed successfully!");

                // Sync feeding history after successful feeding
                await SyncFeedingHistoryAsync();

                // Update feeding statistics display
                await UpdateFeedingStatsAsync();

                await DisplayAlert("Success", "Cat has been fed!", "OK");
            }
            else
            {
                LogActivity($"Feeding failed: {response.ErrorMessage}");
                await DisplayAlert("Error", $"Feeding failed: {response.ErrorMessage}", "OK");
            }
        }
        catch (Exception ex)
        {
            LogActivity($"Feeding error: {ex.Message}");
            await DisplayAlert("Error", $"Feeding failed: {ex.Message}", "OK");
        }
        finally
        {
            FeedButton.IsEnabled = true;
        }
    }

    private async void OnGetStatusClicked(object sender, EventArgs e)
    {
        try
        {
            StatusButton.IsEnabled = false;
            LogActivity("Getting device status...");

            var status = await _bluetoothService.GetStatusAsync();

            var statusText = $"WiFi: {(status.ConnectedSsid != "" ? "Connected" : "Disconnected")}\n" +
                           $"Motor Ready: {status.MotorReady}\n" +
                           $"Feedings Today: {status.FeedingsToday}\n" +
                           $"Food Level: {status.FoodLevelPercent:F1}%";

            DeviceStatusLabel.Text = statusText;
            LogActivity("Device status updated");
        }
        catch (Exception ex)
        {
            LogActivity($"Get status error: {ex.Message}");
            await DisplayAlert("Error", $"Failed to get status: {ex.Message}", "OK");
        }
        finally
        {
            StatusButton.IsEnabled = true;
        }
    }

    private async void OnConfigureClicked(object sender, EventArgs e)
    {
        // TODO: Navigate to configuration page
        await DisplayAlert("Info", "Configuration page coming soon!", "OK");
        LogActivity("Configuration requested - feature coming soon");
    }

    private async void OnTestClicked(object sender, EventArgs e)
    {
        try
        {
            TestButton.IsEnabled = false;
            LogActivity("Testing device connectivity...");

            var success = await _bluetoothService.PingAsync();

            if (success)
            {
                LogActivity("Device test successful - device is responding!");
                await DisplayAlert("Success", "Device is responding correctly", "OK");
            }
            else
            {
                LogActivity("Device test failed - device not responding");
                await DisplayAlert("Error", "Device is not responding", "OK");
            }
        }
        catch (Exception ex)
        {
            LogActivity($"Device test error: {ex.Message}");
            await DisplayAlert("Error", $"Device test failed: {ex.Message}", "OK");
        }
        finally
        {
            TestButton.IsEnabled = true;
        }
    }

    private void OnClearLogClicked(object sender, EventArgs e)
    {
        ActivityLogLabel.Text = "Log cleared.\n";
        LogActivity("Activity log cleared");
    }

    private void OnConnectionStatusChanged(object? sender, BluetoothEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            LogActivity($"Connection status: {e.Status} - {e.Message}");
            UpdateUI();
        });
    }

    private void OnCommandResponseReceived(object? sender, BluetoothCommandEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            LogActivity($"Command: {e.Command} -> {(e.Success ? "Success" : "Failed")} ({e.Duration.TotalMilliseconds:F0}ms)");
        });
    }

    private void UpdateUI()
    {
        var isConnected = _bluetoothService.ConnectionStatus == BluetoothConnectionStatus.Connected;

        ConnectionStatusLabel.Text = _bluetoothService.ConnectionStatus.ToString();

        if (_bluetoothService.ConnectedDevice != null)
        {
            DeviceNameLabel.Text = $"Connected: {_bluetoothService.ConnectedDevice.Name}";
        }
        else if (_selectedDevice != null)
        {
            DeviceNameLabel.Text = $"Selected: {_selectedDevice.Name}";
        }
        else
        {
            DeviceNameLabel.Text = "No device selected";
        }

        ConnectButton.IsEnabled = !isConnected && _selectedDevice != null;
        DisconnectButton.IsEnabled = isConnected;

        FeedButton.IsEnabled = isConnected;
        StatusButton.IsEnabled = isConnected;
        ConfigButton.IsEnabled = isConnected;
        TestButton.IsEnabled = isConnected;
    }

    private async Task SyncFeedingHistoryAsync()
    {
        try
        {
            if (_bluetoothService.ConnectedDevice == null) return;

            LogActivity("Syncing feeding history...");

            var deviceId = _bluetoothService.ConnectedDevice.Address;
            var syncResponse = await _feedingHistoryService.SyncWithDeviceAsync(_bluetoothService, deviceId);

            LogActivity($"Synced {syncResponse.EntryCount} new feeding entries");

            if (syncResponse.HasMore)
            {
                LogActivity("More entries available - sync again for complete history");
            }
        }
        catch (Exception ex)
        {
            LogActivity($"Sync failed: {ex.Message}");
        }
    }

    private async Task UpdateFeedingStatsAsync()
    {
        try
        {
            var stats = await _feedingHistoryService.GetFeedingStatisticsAsync();

            var statusText = $"Total Feedings: {stats.TotalFeedings}\n" +
                           $"Today: {stats.FeedingsToday} ({stats.SuccessfulFeedingsToday} successful)\n" +
                           $"This Week: {stats.FeedingsThisWeek}\n" +
                           $"Last Feeding: {stats.LastFeedingTimeString}\n" +
                           $"Success Rate Today: {stats.SuccessRateToday:F1}%";

            DeviceStatusLabel.Text = statusText;
        }
        catch (Exception ex)
        {
            LogActivity($"Failed to update feeding stats: {ex.Message}");
        }
    }

    private void OnFeedingHistoryUpdated(object? sender, FeedingHistoryEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            switch (e.Action)
            {
                case HistoryAction.Added:
                    LogActivity($"New feeding recorded: {e.Entry?.PortionString} - {e.Entry?.ResultString}");
                    break;
                case HistoryAction.Synced:
                    LogActivity($"Feeding history synced: {e.Entries?.Count ?? 0} entries");
                    break;
                case HistoryAction.Cleared:
                    LogActivity("Feeding history cleared");
                    break;
            }

            await UpdateFeedingStatsAsync();
        });
    }

    private void LogActivity(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        ActivityLogLabel.Text += $"[{timestamp}] {message}\n";
    }
}
