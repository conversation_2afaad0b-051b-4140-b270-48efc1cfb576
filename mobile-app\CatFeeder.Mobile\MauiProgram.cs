﻿using Microsoft.Extensions.Logging;
using CatFeeder.Core.Services;

namespace CatFeeder.Mobile;

public static class MauiProgram
{
	public static MauiApp CreateMauiApp()
	{
		try
		{
			var builder = MauiApp.CreateBuilder();
			builder
				.UseMauiApp<App>()
				.ConfigureFonts(fonts =>
				{
					fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
					fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
				});

			// Register services for dependency injection
			builder.Services.AddSingleton<IBluetoothService, CatFeeder.Core.Services.BluetoothService>();
			builder.Services.AddSingleton<IFeedingHistoryService, CatFeeder.Core.Services.FeedingHistoryService>();
			builder.Services.AddSingleton<IPermissionService, CatFeeder.Core.Services.PermissionService>();
			builder.Services.AddSingleton<ILoggingService, CatFeeder.Core.Services.LoggingService>();

#if DEBUG
			builder.Logging.AddDebug();
#endif

			return builder.Build();
		}
		catch (Exception ex)
		{
			// Log the error and create a minimal app
			System.Diagnostics.Debug.WriteLine($"Error creating MAUI app: {ex}");

			// Create minimal app without services that might cause issues
			var builder = MauiApp.CreateBuilder();
			builder.UseMauiApp<App>();
			
			// Add minimal logging
			builder.Logging.AddDebug();
			
			return builder.Build();
		}
	}
}
