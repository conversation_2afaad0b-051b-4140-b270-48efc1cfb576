/**
 * Bluetooth Command Handlers
 * 
 * Handles all Bluetooth configuration commands for the Cat Feeder
 */

#pragma once

#include "esp_err.h"
#include "bluetooth_manager.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Initialize Bluetooth command handlers
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t bt_commands_init(void);

/**
 * Authentication Commands
 */

/**
 * Handle LOGIN command
 * Format: LOGIN:username,password
 */
esp_err_t bt_cmd_login(const char* command, const char* params, 
                      char* response, size_t response_size, const char* username);

/**
 * Handle LOGOUT command
 * Format: LOGOUT
 */
esp_err_t bt_cmd_logout(const char* command, const char* params, 
                       char* response, size_t response_size, const char* username);

/**
 * Handle CHANGE_PASSWORD command
 * Format: CHANGE_PASSWORD:old_password,new_password
 */
esp_err_t bt_cmd_change_password(const char* command, const char* params, 
                                char* response, size_t response_size, const char* username);

/**
 * Device Information Commands
 */

/**
 * Handle GET_STATUS command
 * Format: GET_STATUS
 */
esp_err_t bt_cmd_get_status(const char* command, const char* params, 
                           char* response, size_t response_size, const char* username);

/**
 * Handle GET_DEVICE_INFO command
 * Format: GET_DEVICE_INFO
 */
esp_err_t bt_cmd_get_device_info(const char* command, const char* params,
                                char* response, size_t response_size, const char* username);

/**
 * Handle GET_VERSION command
 * Format: GET_VERSION
 */
esp_err_t bt_cmd_get_version(const char* command, const char* params,
                            char* response, size_t response_size, const char* username);

/**
 * Handle SET_DEVICE_NAME command
 * Format: SET_DEVICE_NAME:new_name
 */
esp_err_t bt_cmd_set_device_name(const char* command, const char* params, 
                                char* response, size_t response_size, const char* username);

/**
 * WiFi Configuration Commands
 */

/**
 * Handle WIFI_SCAN command
 * Format: WIFI_SCAN
 */
esp_err_t bt_cmd_wifi_scan(const char* command, const char* params, 
                          char* response, size_t response_size, const char* username);

/**
 * Handle WIFI_CONNECT command
 * Format: WIFI_CONNECT:ssid,password
 */
esp_err_t bt_cmd_wifi_connect(const char* command, const char* params, 
                             char* response, size_t response_size, const char* username);

/**
 * Handle WIFI_DISCONNECT command
 * Format: WIFI_DISCONNECT
 */
esp_err_t bt_cmd_wifi_disconnect(const char* command, const char* params, 
                                char* response, size_t response_size, const char* username);

/**
 * Handle WIFI_STATUS command
 * Format: WIFI_STATUS
 */
esp_err_t bt_cmd_wifi_status(const char* command, const char* params, 
                            char* response, size_t response_size, const char* username);

/**
 * Motor Configuration Commands
 */

/**
 * Handle SET_MOTOR_TYPE command
 * Format: SET_MOTOR_TYPE:type (0=stepper, 1=dc_time, 2=dc_sensor)
 */
esp_err_t bt_cmd_set_motor_type(const char* command, const char* params, 
                               char* response, size_t response_size, const char* username);

/**
 * Handle SET_STEPPER_CONFIG command
 * Format: SET_STEPPER_CONFIG:step_pin,dir_pin,enable_pin,steps_per_rev,microsteps,max_speed,acceleration
 */
esp_err_t bt_cmd_set_stepper_config(const char* command, const char* params, 
                                   char* response, size_t response_size, const char* username);

/**
 * Handle SET_DC_CONFIG command
 * Format: SET_DC_CONFIG:pwm_pin,dir_pin,enable_pin,sensor_pin,frequency,duty_cycle,run_time
 */
esp_err_t bt_cmd_set_dc_config(const char* command, const char* params, 
                              char* response, size_t response_size, const char* username);

/**
 * Handle TEST_MOTOR command
 * Format: TEST_MOTOR:steps_or_time
 */
esp_err_t bt_cmd_test_motor(const char* command, const char* params, 
                           char* response, size_t response_size, const char* username);

/**
 * Optional Features Commands
 */

/**
 * Handle SET_FEATURES command
 * Format: SET_FEATURES:food_sensor,led_indicators,buzzer,manual_button
 */
esp_err_t bt_cmd_set_features(const char* command, const char* params, 
                             char* response, size_t response_size, const char* username);

/**
 * Handle SET_LED_CONFIG command
 * Format: SET_LED_CONFIG:power_pin,wifi_pin,feeding_pin,error_pin
 */
esp_err_t bt_cmd_set_led_config(const char* command, const char* params, 
                               char* response, size_t response_size, const char* username);

/**
 * Handle SET_SENSOR_CONFIG command
 * Format: SET_SENSOR_CONFIG:dout_pin,sck_pin,calibration,offset,button_pin,buzzer_pin
 */
esp_err_t bt_cmd_set_sensor_config(const char* command, const char* params, 
                                  char* response, size_t response_size, const char* username);

/**
 * Feeding Configuration Commands
 */

/**
 * Handle SET_FEEDING_CONFIG command
 * Format: SET_FEEDING_CONFIG:default_portion,min_portion,max_portion,speed,max_per_day,min_interval
 */
esp_err_t bt_cmd_set_feeding_config(const char* command, const char* params, 
                                   char* response, size_t response_size, const char* username);

/**
 * Handle MANUAL_FEED command
 * Format: MANUAL_FEED:portion_size
 */
esp_err_t bt_cmd_manual_feed(const char* command, const char* params, 
                            char* response, size_t response_size, const char* username);

/**
 * Handle GET_FEEDING_HISTORY command
 * Format: GET_FEEDING_HISTORY:days
 */
esp_err_t bt_cmd_get_feeding_history(const char* command, const char* params, 
                                    char* response, size_t response_size, const char* username);

/**
 * Schedule Configuration Commands
 */

/**
 * Handle SET_SCHEDULE command
 * Format: SET_SCHEDULE:hour,minute,portion,enabled,days_of_week
 */
esp_err_t bt_cmd_set_schedule(const char* command, const char* params, 
                             char* response, size_t response_size, const char* username);

/**
 * Handle GET_SCHEDULE command
 * Format: GET_SCHEDULE
 */
esp_err_t bt_cmd_get_schedule(const char* command, const char* params, 
                             char* response, size_t response_size, const char* username);

/**
 * Handle CLEAR_SCHEDULE command
 * Format: CLEAR_SCHEDULE
 */
esp_err_t bt_cmd_clear_schedule(const char* command, const char* params, 
                               char* response, size_t response_size, const char* username);

/**
 * Time Configuration Commands
 */

/**
 * Handle SET_TIME_CONFIG command
 * Format: SET_TIME_CONFIG:ntp_server,timezone,auto_sync,sync_interval
 */
esp_err_t bt_cmd_set_time_config(const char* command, const char* params, 
                                char* response, size_t response_size, const char* username);

/**
 * Handle SET_TIME command
 * Format: SET_TIME:unix_timestamp
 */
esp_err_t bt_cmd_set_time(const char* command, const char* params, 
                         char* response, size_t response_size, const char* username);

/**
 * Handle GET_TIME command
 * Format: GET_TIME
 */
esp_err_t bt_cmd_get_time(const char* command, const char* params, 
                         char* response, size_t response_size, const char* username);

/**
 * Handle SYNC_TIME command
 * Format: SYNC_TIME
 */
esp_err_t bt_cmd_sync_time(const char* command, const char* params, 
                          char* response, size_t response_size, const char* username);

/**
 * System Commands
 */

/**
 * Handle SAVE_CONFIG command
 * Format: SAVE_CONFIG
 */
esp_err_t bt_cmd_save_config(const char* command, const char* params, 
                            char* response, size_t response_size, const char* username);

/**
 * Handle LOAD_CONFIG command
 * Format: LOAD_CONFIG
 */
esp_err_t bt_cmd_load_config(const char* command, const char* params, 
                            char* response, size_t response_size, const char* username);

/**
 * Handle FACTORY_RESET command
 * Format: FACTORY_RESET:confirm
 */
esp_err_t bt_cmd_factory_reset(const char* command, const char* params, 
                              char* response, size_t response_size, const char* username);

/**
 * Handle RESTART command
 * Format: RESTART
 */
esp_err_t bt_cmd_restart(const char* command, const char* params, 
                        char* response, size_t response_size, const char* username);

/**
 * Handle GET_LOGS command
 * Format: GET_LOGS:lines
 */
esp_err_t bt_cmd_get_logs(const char* command, const char* params, 
                         char* response, size_t response_size, const char* username);

/**
 * Security Commands
 */

/**
 * Handle ADD_USER command
 * Format: ADD_USER:username,password,level
 */
esp_err_t bt_cmd_add_user(const char* command, const char* params, 
                         char* response, size_t response_size, const char* username);

/**
 * Handle REMOVE_USER command
 * Format: REMOVE_USER:username
 */
esp_err_t bt_cmd_remove_user(const char* command, const char* params, 
                            char* response, size_t response_size, const char* username);

/**
 * Handle LIST_USERS command
 * Format: LIST_USERS
 */
esp_err_t bt_cmd_list_users(const char* command, const char* params, 
                           char* response, size_t response_size, const char* username);

/**
 * Handle SET_SECURITY command
 * Format: SET_SECURITY:require_auth,allow_guest,session_timeout,bt_pin
 */
esp_err_t bt_cmd_set_security(const char* command, const char* params, 
                             char* response, size_t response_size, const char* username);

#ifdef __cplusplus
}
#endif
