/**
 * Bluetooth Command Handlers Implementation
 *
 * This module implements all Bluetooth commands that can be sent from the mobile app
 * to control and monitor the Cat Feeder device. Each command has a specific handler
 * function that processes parameters and returns a formatted response.
 *
 * Command Format: "COMMAND:parameter1,parameter2,parameter3"
 * Response Format: "SUCCESS:data" or "ERROR:message" or "STATUS:{json}" etc.
 *
 * Supported Commands:
 * - PING: Test connectivity
 * - GET_STATUS: Get complete device status
 * - GET_DEVICE_INFO: Get hardware and firmware information
 * - MANUAL_FEED: Execute manual feeding with portion size
 * - SET_MOTOR_TYPE: Configure motor type (stepper/DC)
 * - WIFI_CONNECT: Connect to WiFi network
 * - GET_TIME: Get current device time
 * - GET_FEEDING_STATS: Get feeding statistics
 * - SYNC_FEEDING_HISTORY: Delta sync feeding history
 * - GET_RECENT_FEEDINGS: Get recent feeding entries
 * - RESTART: Restart the device
 * - ECHO: Echo back parameters (for testing)
 *
 * External Libraries Used:
 * - esp_system.h: System information (chip info, heap, etc.)
 * - esp_timer.h: High-resolution timer for duration measurement
 * - esp_wifi.h: WiFi status and connection management
 * - time.h: Time functions for timestamps
 */

#include "bt_commands.h"
#include "bluetooth_manager.h"    // Bluetooth communication management
#include "feeding_controller.h"   // Feeding execution and control
#include "feeding_history.h"      // Feeding history tracking and sync
#include "motor_control.h"        // Motor status and configuration
#include "wifi_manager.h"         // WiFi connection management
#include "scheduler.h"            // Feeding schedule management
#include "app_config.h"           // Device configuration storage
#include "version.h"              // Version information
#include "esp_log.h"              // ESP32 logging system
#include "esp_system.h"           // System info (chip model, heap, etc.)
#include "esp_chip_info.h"        // Chip information functions
#include "esp_timer.h"            // High-resolution timer (microsecond precision)
#include "esp_wifi.h"             // WiFi API for status and connection info
#include "esp_partition.h"        // Flash size information
#include <string.h>               // String manipulation (strlen, strchr, etc.)
#include <time.h>                 // Time functions
#include <inttypes.h>             // Format specifier macros
#include <stdio.h>                // Standard I/O (snprintf for response formatting)
#include <time.h>                 // Time functions (time, localtime_r, etc.)

// Logging tag for this module
static const char *TAG = "BT_COMMANDS";

// ============================================================================
// BASIC CONNECTIVITY AND SYSTEM COMMANDS
// ============================================================================

/**
 * Handle PING command - Basic connectivity test
 *
 * This is the simplest command used to test if the Bluetooth connection
 * and command processing system is working correctly. Mobile apps use this
 * to verify the device is responsive before sending more complex commands.
 *
 * Command: "PING"
 * Response: "SUCCESS:PONG"
 *
 * @param command Command string ("PING")
 * @param params Parameters string (empty for PING)
 * @param response Buffer to store response string
 * @param response_size Maximum size of response buffer
 * @param username Username of the requesting user (for logging)
 * @return ESP_OK always (PING never fails)
 *
 * External functions used:
 * - ESP_LOGI(tag, format, ...): Log info message for debugging
 * - snprintf(buf, size, format, ...): Safe formatted string printing
 */
esp_err_t bt_cmd_ping(const char* command, const char* params,
                     char* response, size_t response_size, const char* username)
{
    // Log the ping request for debugging and monitoring
    ESP_LOGI(TAG, "PING command from user: %s", username);

    // Format the standard PONG response
    // snprintf ensures we don't overflow the response buffer
    snprintf(response, response_size, "SUCCESS:PONG");

    return ESP_OK;  // PING command always succeeds
}

/**
 * Handle GET_STATUS command - Complete device status
 *
 * This command returns comprehensive status information about the Cat Feeder
 * including WiFi connection, motor status, feeding statistics, and system health.
 * Mobile apps use this to display real-time device status to users.
 *
 * Command: "GET_STATUS"
 * Response: "STATUS:{json_object_with_all_status_info}"
 *
 * @param command Command string ("GET_STATUS")
 * @param params Parameters string (empty for GET_STATUS)
 * @param response Buffer to store JSON response
 * @param response_size Maximum size of response buffer
 * @param username Username of the requesting user
 * @return ESP_OK on success, ESP_ERR_* on failure
 *
 * External functions used:
 * - esp_get_free_heap_size(): Get available RAM in bytes
 * - esp_timer_get_time(): Get microseconds since boot (for uptime calculation)
 * - esp_wifi_sta_get_ap_info(): Get info about connected WiFi access point
 * - feeding_controller_get_status(): Get feeding system status
 * - motor_control_get_status(): Get motor system status
 */
esp_err_t bt_cmd_get_status(const char* command, const char* params,
                           char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_STATUS command from user: %s", username);

    // Get system memory information
    // esp_get_free_heap_size() returns available RAM in bytes
    uint32_t free_heap = esp_get_free_heap_size();

    // Calculate uptime in seconds
    // esp_timer_get_time() returns microseconds since boot, convert to seconds
    int64_t uptime = esp_timer_get_time() / 1000000;

    // Get WiFi connection status and access point information
    wifi_ap_record_t ap_info;
    // esp_wifi_sta_get_ap_info() returns ESP_OK if connected, fills ap_info structure
    // ap_info.ssid contains the network name, ap_info.rssi contains signal strength
    bool wifi_connected = (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK);

    // Get feeding controller status (feedings today, last feeding time, etc.)
    feeding_controller_status_t feeding_status;
    feeding_controller_get_status(&feeding_status);

    // Get motor system status (ready state, motor type, etc.)
    motor_status_t motor_status;
    motor_control_get_status(&motor_status);

    // Format comprehensive status response as JSON
    // Uses conditional operators to handle null/invalid values safely
    snprintf(response, response_size,
        "STATUS:{"
        "\"wifi_connected\":%s,"           // true/false WiFi connection status
        "\"wifi_ssid\":\"%s\","            // Connected network name or empty
        "\"bt_status\":\"connected\","     // Bluetooth is connected (we're processing this command)
        "\"motor_ready\":%s,"              // true/false motor ready status
        "\"motor_type\":%d,"               // Motor type: 0=stepper, 1=DC_time, 2=DC_sensor
        "\"feedings_today\":%" PRIu32 ","  // Number of feedings completed today
        "\"last_feeding\":%lld,"           // Unix timestamp of last feeding
        "\"free_heap\":%lu,"               // Available RAM in bytes
        "\"uptime\":%lld,"                 // Device uptime in seconds
        "\"firmware_version\":\"1.0.0\""   // Firmware version string
        "}",
        wifi_connected ? "true" : "false",                    // WiFi status
        wifi_connected ? (char*)ap_info.ssid : "",            // Network name or empty
        motor_status.is_ready ? "true" : "false",             // Motor ready status
        motor_status.type,                                    // Motor type enum value
        feeding_status.feedings_today,                        // Today's feeding count
        feeding_status.last_feeding_time,                     // Last feeding timestamp
        free_heap,                                            // Available memory
        uptime                                                // Uptime in seconds
    );

    return ESP_OK;
}

/**
 * Handle GET_DEVICE_INFO command - Device information
 */
esp_err_t bt_cmd_get_device_info(const char* command, const char* params, 
                                char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_DEVICE_INFO command from user: %s", username);
    
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    
    // Get MAC address
    uint8_t mac[6];
    esp_wifi_get_mac(WIFI_IF_STA, mac);
    
    snprintf(response, response_size,
        "INFO:{"
        "\"chip_model\":\"ESP32-WROOM-32\","
        "\"chip_revision\":%d,"
        "\"cpu_cores\":%d,"
        "\"flash_size\":%d,"
        "\"device_name\":\"CatFeeder_ESP32\","
        "\"mac_address\":\"%02X:%02X:%02X:%02X:%02X:%02X\","
        "\"firmware_version\":\"1.0.0\","
        "\"build_date\":\"%s\","
        "\"build_time\":\"%s\""
        "}",
        chip_info.revision,
        chip_info.cores,
        4 * 1024 * 1024, // 4MB flash size (typical for ESP32)
        mac[0], mac[1], mac[2], mac[3], mac[4], mac[5],
        __DATE__,
        __TIME__
    );
    
    return ESP_OK;
}

/**
 * Handle GET_VERSION command - Get firmware version information
 *
 * This command returns detailed version information including version number,
 * build date/time, and company information. Mobile apps use this to verify
 * firmware compatibility and display version info to users.
 *
 * Command: "GET_VERSION"
 * Response: "VERSION:{json_object_with_version_info}"
 *
 * @param command Command string ("GET_VERSION")
 * @param params Parameters string (empty for GET_VERSION)
 * @param response Buffer to store response string
 * @param response_size Maximum size of response buffer
 * @param username Username of the requesting user (for logging)
 * @return ESP_OK always
 *
 * External functions used:
 * - get_version_string(): Get version string from version.h
 * - get_full_version_info(): Get full version info with build date/time
 * - get_version_int(): Get version as integer for comparison
 */
esp_err_t bt_cmd_get_version(const char* command, const char* params,
                            char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_VERSION command from user: %s", username);

    snprintf(response, response_size,
        "VERSION:{"
        "\"version\":\"%s\","
        "\"version_int\":%" PRIu32 ","
        "\"major\":%d,"
        "\"minor\":%d,"
        "\"patch\":%d,"
        "\"build\":%d,"
        "\"full_info\":\"%s\","
        "\"company\":\"%s\","
        "\"product\":\"%s\","
        "\"build_date\":\"%s\","
        "\"build_time\":\"%s\""
        "}",
        get_version_string(),
        get_version_int(),
        VERSION_MAJOR,
        VERSION_MINOR,
        VERSION_PATCH,
        VERSION_BUILD,
        get_full_version_info(),
        COMPANY_NAME,
        PRODUCT_NAME,
        BUILD_DATE,
        BUILD_TIME
    );

    return ESP_OK;
}

/**
 * Handle MANUAL_FEED command - Execute manual feeding
 */
esp_err_t bt_cmd_manual_feed(const char* command, const char* params,
                            char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "MANUAL_FEED command from user: %s, params: %s", username, params);

    // Parse portion size (default to 200 if not specified)
    uint32_t portion_size = 200;
    if (strlen(params) > 0) {
        portion_size = (uint32_t)atoi(params);
    }

    // Validate portion size
    if (portion_size < 50 || portion_size > 2000) {
        snprintf(response, response_size, "ERROR:Invalid portion size (50-2000)");
        return ESP_ERR_INVALID_ARG;
    }

    // Record start time for duration calculation
    int64_t start_time = esp_timer_get_time();

    // Execute feeding
    feeding_result_t result = feeding_controller_feed_manual(portion_size);

    // Calculate duration
    uint32_t duration_ms = (uint32_t)((esp_timer_get_time() - start_time) / 1000);

    // Determine feeding result
    feeding_result_t feeding_result = (result == ESP_OK) ? FEEDING_RESULT_SUCCESS : FEEDING_RESULT_MOTOR_ERROR;
    uint32_t actual_portion = (result == ESP_OK) ? portion_size : 0;

    // Create and record feeding history entry
    feeding_entry_t entry = feeding_history_create_entry(
        portion_size,
        actual_portion,
        feeding_result,
        FEEDING_TRIGGER_BLUETOOTH,
        duration_ms,
        username,
        (result == ESP_OK) ? "Manual feed via Bluetooth" : "Manual feed failed"
    );

    esp_err_t history_result = feeding_history_add_entry(&entry);
    if (history_result != ESP_OK) {
        ESP_LOGW(TAG, "Failed to record feeding history: %s", esp_err_to_name(history_result));
    }

    if (result == ESP_OK) {
        snprintf(response, response_size, "SUCCESS:Manual feeding completed (%lu units, %lu ms)",
                 actual_portion, duration_ms);
    } else {
        snprintf(response, response_size, "ERROR:Feeding failed - %s", esp_err_to_name(result));
    }

    return result;
}

/**
 * Handle SET_MOTOR_TYPE command - Set motor type
 */
esp_err_t bt_cmd_set_motor_type(const char* command, const char* params, 
                               char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "SET_MOTOR_TYPE command from user: %s, params: %s", username, params);
    
    if (strlen(params) == 0) {
        snprintf(response, response_size, "ERROR:Motor type required (0=stepper, 1=dc_time, 2=dc_sensor)");
        return ESP_ERR_INVALID_ARG;
    }
    
    int motor_type = atoi(params);
    if (motor_type < 0 || motor_type > 2) {
        snprintf(response, response_size, "ERROR:Invalid motor type (0-2)");
        return ESP_ERR_INVALID_ARG;
    }
    
    // Load current configuration
    app_config_t app_config;
    esp_err_t ret = app_config_load(&app_config);
    if (ret != ESP_OK) {
        snprintf(response, response_size, "ERROR:Failed to load configuration");
        return ret;
    }
    
    // Update motor type
    app_config.motor_type = (motor_type_t)motor_type;
    
    // Save configuration
    ret = app_config_save(&app_config);
    if (ret != ESP_OK) {
        snprintf(response, response_size, "ERROR:Failed to save configuration");
        return ret;
    }
    
    const char* motor_names[] = {"Stepper", "DC Time-based", "DC Sensor-based"};
    snprintf(response, response_size, "SUCCESS:Motor type set to %s", motor_names[motor_type]);
    
    return ESP_OK;
}

/**
 * Handle WIFI_CONNECT command - Connect to WiFi
 */
esp_err_t bt_cmd_wifi_connect(const char* command, const char* params, 
                             char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "WIFI_CONNECT command from user: %s", username);
    
    // Parse SSID and password
    char ssid[32] = {0};
    char password[64] = {0};
    
    const char* comma = strchr(params, ',');
    if (comma == NULL) {
        snprintf(response, response_size, "ERROR:Format: WIFI_CONNECT:ssid,password");
        return ESP_ERR_INVALID_ARG;
    }
    
    size_t ssid_len = comma - params;
    if (ssid_len >= sizeof(ssid)) {
        snprintf(response, response_size, "ERROR:SSID too long");
        return ESP_ERR_INVALID_ARG;
    }
    
    strncpy(ssid, params, ssid_len);
    strncpy(password, comma + 1, sizeof(password) - 1);
    
    ESP_LOGI(TAG, "Connecting to WiFi: %s", ssid);
    
    // Connect to WiFi
    esp_err_t ret = wifi_manager_connect(ssid, password);
    
    if (ret == ESP_OK) {
        snprintf(response, response_size, "SUCCESS:Connecting to WiFi %s", ssid);
    } else {
        snprintf(response, response_size, "ERROR:Failed to connect to WiFi");
    }
    
    return ret;
}

/**
 * Handle GET_TIME command - Get current time
 */
esp_err_t bt_cmd_get_time(const char* command, const char* params, 
                         char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_TIME command from user: %s", username);
    
    time_t now;
    time(&now);
    
    struct tm timeinfo;
    localtime_r(&now, &timeinfo);
    
    snprintf(response, response_size, 
        "TIME:{"
        "\"unix_timestamp\":%lld,"
        "\"formatted\":\"%04d-%02d-%02d %02d:%02d:%02d\","
        "\"timezone\":\"UTC\""
        "}",
        now,
        timeinfo.tm_year + 1900,
        timeinfo.tm_mon + 1,
        timeinfo.tm_mday,
        timeinfo.tm_hour,
        timeinfo.tm_min,
        timeinfo.tm_sec
    );
    
    return ESP_OK;
}

/**
 * Handle RESTART command - Restart device
 */
esp_err_t bt_cmd_restart(const char* command, const char* params, 
                        char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "RESTART command from user: %s", username);
    
    snprintf(response, response_size, "SUCCESS:Restarting device in 3 seconds");
    
    // Schedule restart after response is sent
    // Note: In a real implementation, you'd use a timer to delay the restart
    // For now, we'll just acknowledge the command
    
    return ESP_OK;
}

/**
 * Handle GET_FEEDING_STATS command - Get feeding statistics
 */
esp_err_t bt_cmd_get_feeding_stats(const char* command, const char* params,
                                  char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_FEEDING_STATS command from user: %s", username);

    feeding_stats_t stats;
    esp_err_t result = feeding_history_get_stats(&stats);

    if (result != ESP_OK) {
        snprintf(response, response_size, "ERROR:Failed to get feeding statistics");
        return result;
    }

    snprintf(response, response_size,
        "STATS:{"
        "\"total_feedings\":%lu,"
        "\"feedings_today\":%lu,"
        "\"feedings_this_week\":%lu,"
        "\"feedings_this_month\":%lu,"
        "\"last_feeding_time\":%lld,"
        "\"last_feeding_portion\":%lu,"
        "\"last_feeding_result\":%d,"
        "\"successful_today\":%lu,"
        "\"failed_today\":%lu,"
        "\"avg_portion_week\":%.1f,"
        "\"total_food_today\":%.1f"
        "}",
        stats.total_feedings,
        stats.feedings_today,
        stats.feedings_this_week,
        stats.feedings_this_month,
        stats.last_feeding_time,
        stats.last_feeding_portion,
        stats.last_feeding_result,
        stats.successful_feedings_today,
        stats.failed_feedings_today,
        stats.avg_portion_size_week,
        stats.total_food_dispensed_today
    );

    return ESP_OK;
}

/**
 * Handle SYNC_FEEDING_HISTORY command - Delta sync feeding history
 */
esp_err_t bt_cmd_sync_feeding_history(const char* command, const char* params,
                                     char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "SYNC_FEEDING_HISTORY command from user: %s, params: %s", username, params);

    // Parse sync request parameters: last_sync_id,max_entries
    sync_request_t request = {0};
    request.last_sync_id = 0;
    request.max_entries = 10; // Default

    if (strlen(params) > 0) {
        char* comma = strchr(params, ',');
        if (comma != NULL) {
            request.last_sync_id = (uint32_t)atoi(params);
            request.max_entries = (uint32_t)atoi(comma + 1);
        } else {
            request.last_sync_id = (uint32_t)atoi(params);
        }
    }

    // Limit max entries to prevent response overflow
    if (request.max_entries > 10) {
        request.max_entries = 10;
    }

    sync_response_t sync_response;
    esp_err_t result = feeding_history_process_sync_request(&request, &sync_response);

    if (result != ESP_OK) {
        snprintf(response, response_size, "ERROR:Failed to process sync request");
        return result;
    }

    // Build JSON response with feeding entries
    int offset = snprintf(response, response_size,
        "SYNC:{"
        "\"latest_id\":%lu,"
        "\"entry_count\":%lu,"
        "\"has_more\":%s,"
        "\"entries\":[",
        sync_response.latest_id,
        sync_response.entry_count,
        sync_response.has_more ? "true" : "false"
    );

    // Add feeding entries
    for (uint32_t i = 0; i < sync_response.entry_count && offset < response_size - 100; i++) {
        const feeding_entry_t* entry = &sync_response.entries[i];

        offset += snprintf(response + offset, response_size - offset,
            "%s{\"id\":%lu,\"time\":%lld,\"portion\":%lu,\"actual\":%lu,\"result\":%d,\"trigger\":%d,\"duration\":%lu,\"user\":\"%s\"}",
            (i > 0) ? "," : "",
            entry->id,
            entry->timestamp,
            entry->portion_size,
            entry->actual_portion,
            entry->result,
            entry->trigger,
            entry->duration_ms,
            entry->user
        );
    }

    snprintf(response + offset, response_size - offset, "]}");

    return ESP_OK;
}

/**
 * Handle GET_RECENT_FEEDINGS command - Get recent feeding entries
 */
esp_err_t bt_cmd_get_recent_feedings(const char* command, const char* params,
                                    char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "GET_RECENT_FEEDINGS command from user: %s, params: %s", username, params);

    // Parse count parameter (default to 5)
    uint32_t count = 5;
    if (strlen(params) > 0) {
        count = (uint32_t)atoi(params);
        if (count > 10) count = 10; // Limit to prevent response overflow
    }

    feeding_entry_t entries[10];
    uint32_t actual_count;

    esp_err_t result = feeding_history_get_recent(entries, count, &actual_count);

    if (result != ESP_OK) {
        snprintf(response, response_size, "ERROR:Failed to get recent feedings");
        return result;
    }

    // Build JSON response
    int offset = snprintf(response, response_size, "RECENT:[");

    for (uint32_t i = 0; i < actual_count && offset < response_size - 100; i++) {
        const feeding_entry_t* entry = &entries[i];

        offset += snprintf(response + offset, response_size - offset,
            "%s{\"id\":%lu,\"time\":%lld,\"portion\":%lu,\"result\":%d,\"trigger\":%d,\"user\":\"%s\"}",
            (i > 0) ? "," : "",
            entry->id,
            entry->timestamp,
            entry->portion_size,
            entry->result,
            entry->trigger,
            entry->user
        );
    }

    snprintf(response + offset, response_size - offset, "]");

    return ESP_OK;
}

/**
 * Handle ECHO command - Echo back parameters
 */
esp_err_t bt_cmd_echo(const char* command, const char* params,
                     char* response, size_t response_size, const char* username)
{
    ESP_LOGI(TAG, "ECHO command from user: %s, params: %s", username, params);
    snprintf(response, response_size, "ECHO:%s", params);
    return ESP_OK;
}

/**
 * Initialize Bluetooth command handlers
 */
esp_err_t bt_commands_init(void)
{
    ESP_LOGI(TAG, "Initializing Bluetooth commands");

    // Initialize feeding history system
    esp_err_t ret = feeding_history_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize feeding history: %s", esp_err_to_name(ret));
        return ret;
    }

    // Register all command handlers
    bluetooth_manager_register_command("PING", bt_cmd_ping);
    bluetooth_manager_register_command("GET_STATUS", bt_cmd_get_status);
    bluetooth_manager_register_command("GET_DEVICE_INFO", bt_cmd_get_device_info);
    bluetooth_manager_register_command("GET_VERSION", bt_cmd_get_version);
    bluetooth_manager_register_command("MANUAL_FEED", bt_cmd_manual_feed);
    bluetooth_manager_register_command("SET_MOTOR_TYPE", bt_cmd_set_motor_type);
    bluetooth_manager_register_command("WIFI_CONNECT", bt_cmd_wifi_connect);
    bluetooth_manager_register_command("GET_TIME", bt_cmd_get_time);
    bluetooth_manager_register_command("RESTART", bt_cmd_restart);
    bluetooth_manager_register_command("GET_FEEDING_STATS", bt_cmd_get_feeding_stats);
    bluetooth_manager_register_command("SYNC_FEEDING_HISTORY", bt_cmd_sync_feeding_history);
    bluetooth_manager_register_command("GET_RECENT_FEEDINGS", bt_cmd_get_recent_feedings);
    bluetooth_manager_register_command("ECHO", bt_cmd_echo);

    ESP_LOGI(TAG, "Bluetooth commands initialized - 13 commands registered");
    return ESP_OK;
}
